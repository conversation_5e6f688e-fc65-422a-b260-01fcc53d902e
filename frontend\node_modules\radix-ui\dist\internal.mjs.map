{"version": 3, "sources": ["../src/internal.ts"], "sourcesContent": ["import { Primitive as BasePrimitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nexport * as Arrow from '@radix-ui/react-arrow';\nexport * as Collection from '@radix-ui/react-collection';\nexport { composeRefs, useComposedRefs } from '@radix-ui/react-compose-refs';\nexport * as Context from '@radix-ui/react-context';\nexport * as DismissableLayer from '@radix-ui/react-dismissable-layer';\nexport * as FocusGuards from '@radix-ui/react-focus-guards';\nexport * as FocusScope from '@radix-ui/react-focus-scope';\nexport * as Menu from '@radix-ui/react-menu';\nexport * as Popper from '@radix-ui/react-popper';\nexport * as Presence from '@radix-ui/react-presence';\nexport type { PrimitivePropsWithRef } from '@radix-ui/react-primitive';\nexport * as RovingFocus from '@radix-ui/react-roving-focus';\nexport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nexport {\n  useControllableState,\n  useControllableStateReducer,\n} from '@radix-ui/react-use-controllable-state';\nexport { useEffectEvent } from '@radix-ui/react-use-effect-event';\nexport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\nexport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\nexport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nexport { useSize } from '@radix-ui/react-use-size';\nexport { composeEventHandlers } from '@radix-ui/primitive';\n\nconst Primitive = BasePrimitive as typeof BasePrimitive & {\n  Root: typeof BasePrimitive;\n  dispatchDiscreteCustomEvent: typeof dispatchDiscreteCustomEvent;\n};\nPrimitive.dispatchDiscreteCustomEvent = dispatchDiscreteCustomEvent;\nPrimitive.Root = BasePrimitive;\nexport { Primitive };\n"], "mappings": ";AAAA,SAAS,aAAa,eAAe,mCAAmC;AACxE,YAAY,WAAW;AACvB,YAAY,gBAAgB;AAC5B,SAAS,aAAa,uBAAuB;AAC7C,YAAY,aAAa;AACzB,YAAY,sBAAsB;AAClC,YAAY,iBAAiB;AAC7B,YAAY,gBAAgB;AAC5B,YAAY,UAAU;AACtB,YAAY,YAAY;AACxB,YAAY,cAAc;AAE1B,YAAY,iBAAiB;AAC7B,SAAS,sBAAsB;AAC/B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,sBAAsB;AAC/B,SAAS,wBAAwB;AACjC,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAChC,SAAS,eAAe;AACxB,SAAS,4BAA4B;AAErC,IAAM,YAAY;AAIlB,UAAU,8BAA8B;AACxC,UAAU,OAAO;", "names": []}