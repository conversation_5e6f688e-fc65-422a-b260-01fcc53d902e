{"version": 3, "sources": ["../../../src/server/dev/middleware-webpack.ts"], "sourcesContent": ["import { findSourceMap, type SourceMap } from 'module'\nimport path from 'path'\nimport { fileURLToPath, pathToFileURL } from 'url'\nimport { SourceMapConsumer } from 'next/dist/compiled/source-map08'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport { getSourceMapFromFile } from './get-source-map-from-file'\nimport {\n  devirtualizeReactServerURL,\n  findApplicableSourceMapPayload,\n  sourceMapIgnoreListsEverything,\n  type BasicSourceMapPayload,\n  type ModernSourceMapPayload,\n} from '../lib/source-maps'\nimport { openFileInEditor } from '../../next-devtools/server/launch-editor'\nimport {\n  getOriginalCodeFrame,\n  ignoreListAnonymousStackFramesIfSandwiched,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from '../../next-devtools/server/shared'\nimport { middlewareResponse } from '../../next-devtools/server/middleware-response'\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type webpack from 'webpack'\nimport type {\n  NullableMappedPosition,\n  RawSourceMap,\n} from 'next/dist/compiled/source-map08'\nimport { formatFrameSourceFile } from '../../next-devtools/shared/webpack-module-path'\nimport type { MappedPosition } from 'source-map'\nimport { inspect } from 'util'\n\nfunction shouldIgnoreSource(sourceURL: string): boolean {\n  return (\n    sourceURL.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    sourceURL.includes('next/dist') ||\n    sourceURL.startsWith('node:')\n  )\n}\n\ntype IgnoredSources = Array<{ url: string; ignored: boolean }>\n\nexport interface IgnorableStackFrame extends StackFrame {\n  ignored: boolean\n}\n\ntype SourceAttributes = {\n  sourcePosition: NullableMappedPosition\n  sourceContent: string | null\n}\n\ntype Source =\n  | {\n      type: 'file'\n      sourceMap: BasicSourceMapPayload\n      ignoredSources: IgnoredSources\n      moduleURL: string\n    }\n  | {\n      type: 'bundle'\n      sourceMap: BasicSourceMapPayload\n      ignoredSources: IgnoredSources\n      compilation: webpack.Compilation\n      moduleId: string\n      moduleURL: string\n    }\n\nfunction getModuleById(\n  id: string | undefined,\n  compilation: webpack.Compilation\n) {\n  const { chunkGraph, modules } = compilation\n\n  return [...modules].find((module) => chunkGraph.getModuleId(module) === id)\n}\n\nfunction findModuleNotFoundFromError(errorMessage: string | undefined) {\n  return errorMessage?.match(/'([^']+)' module/)?.[1]\n}\n\nfunction getSourcePath(source: string) {\n  if (source.startsWith('file://')) {\n    return fileURLToPath(source)\n  }\n  return source.replace(/^(webpack:\\/\\/\\/|webpack:\\/\\/|webpack:\\/\\/_N_E\\/)/, '')\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function findOriginalSourcePositionAndContent(\n  sourceMap: ModernSourceMapPayload,\n  position: { lineNumber: number | null; column: number | null }\n): Promise<SourceAttributes | null> {\n  let consumer: SourceMapConsumer\n  try {\n    consumer = await new SourceMapConsumer(sourceMap)\n  } catch (cause) {\n    console.error(\n      new Error(\n        `${sourceMap.file}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    )\n    return null\n  }\n\n  try {\n    const sourcePosition = consumer.originalPositionFor({\n      line: position.lineNumber ?? 1,\n      // 0-based columns out requires 0-based columns in.\n      column: (position.column ?? 1) - 1,\n    })\n\n    if (!sourcePosition.source) {\n      return null\n    }\n\n    const sourceContent: string | null =\n      consumer.sourceContentFor(\n        sourcePosition.source,\n        /* returnNullOnMissing */ true\n      ) ?? null\n\n    return {\n      sourcePosition,\n      sourceContent,\n    }\n  } finally {\n    consumer.destroy()\n  }\n}\n\nexport function getIgnoredSources(\n  sourceMap: RawSourceMap & { ignoreList?: number[] }\n): IgnoredSources {\n  const ignoreList = new Set<number>(sourceMap.ignoreList ?? [])\n  const moduleFilenames = sourceMap?.sources ?? []\n\n  for (let index = 0; index < moduleFilenames.length; index++) {\n    // bundlerFilePath case: webpack://./app/page.tsx\n    const webpackSourceURL = moduleFilenames[index]\n    // Format the path to the normal file path\n    const formattedFilePath = formatFrameSourceFile(webpackSourceURL)\n    if (shouldIgnoreSource(formattedFilePath)) {\n      ignoreList.add(index)\n    }\n  }\n\n  const ignoredSources = sourceMap.sources.map((source, index) => {\n    return {\n      url: source,\n      ignored: ignoreList.has(sourceMap.sources.indexOf(source)),\n      content: sourceMap.sourcesContent?.[index] ?? null,\n    }\n  })\n  return ignoredSources\n}\n\nfunction isIgnoredSource(\n  source: Source,\n  sourcePosition: MappedPosition | NullableMappedPosition\n) {\n  if (sourcePosition.source == null) {\n    return true\n  }\n  for (const ignoredSource of source.ignoredSources) {\n    if (ignoredSource.ignored && ignoredSource.url === sourcePosition.source) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction findOriginalSourcePositionAndContentFromCompilation(\n  moduleId: string | undefined,\n  importedModule: string,\n  compilation: webpack.Compilation\n): SourceAttributes | null {\n  const module = getModuleById(moduleId, compilation)\n  return module?.buildInfo?.importLocByPath?.get(importedModule) ?? null\n}\n\nexport async function createOriginalStackFrame({\n  ignoredByDefault,\n  source,\n  rootDirectory,\n  frame,\n  errorMessage,\n}: {\n  /** setting this to true will not consult ignoreList */\n  ignoredByDefault: boolean\n  source: Source\n  rootDirectory: string\n  frame: StackFrame\n  errorMessage?: string\n}): Promise<OriginalStackFrameResponse | null> {\n  const moduleNotFound = findModuleNotFoundFromError(errorMessage)\n  const result = await (() => {\n    if (moduleNotFound) {\n      if (source.type === 'file') {\n        return undefined\n      }\n\n      return findOriginalSourcePositionAndContentFromCompilation(\n        source.moduleId,\n        moduleNotFound,\n        source.compilation\n      )\n    }\n    return findOriginalSourcePositionAndContent(source.sourceMap, frame)\n  })()\n\n  if (!result) {\n    return null\n  }\n  const { sourcePosition, sourceContent } = result\n\n  if (!sourcePosition.source) {\n    return null\n  }\n\n  const ignored =\n    ignoredByDefault ||\n    isIgnoredSource(source, sourcePosition) ||\n    // If the source file is externals, should be excluded even it's not ignored source.\n    // e.g. webpack://next/dist/.. needs to be ignored\n    shouldIgnoreSource(source.moduleURL)\n\n  const sourcePath = getSourcePath(\n    // When sourcePosition.source is the loader path the modulePath is generally better.\n    (sourcePosition.source!.includes('|')\n      ? source.moduleURL\n      : sourcePosition.source) || source.moduleURL\n  )\n  const filePath = path.resolve(rootDirectory, sourcePath)\n  const resolvedFilePath = path.relative(rootDirectory, filePath)\n\n  const traced: IgnorableStackFrame = {\n    file: resolvedFilePath,\n    lineNumber: sourcePosition.line,\n    column: (sourcePosition.column ?? 0) + 1,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      // default is not a valid identifier in JS so webpack uses a custom variable when it's an unnamed default export\n      // Resolve it back to `default` for the method name if the source position didn't have the method.\n      frame.methodName\n        ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n        ?.replace('__webpack_exports__.', ''),\n    arguments: [],\n    ignored,\n  }\n\n  return {\n    originalStackFrame: traced,\n    originalCodeFrame: getOriginalCodeFrame(traced, sourceContent),\n  }\n}\n\nasync function getSourceMapFromCompilation(\n  id: string,\n  compilation: webpack.Compilation\n): Promise<RawSourceMap | undefined> {\n  try {\n    const module = getModuleById(id, compilation)\n\n    if (!module) {\n      return undefined\n    }\n\n    // @ts-expect-error The types for `CodeGenerationResults.get` require a\n    // runtime to be passed as second argument, but apparently it also works\n    // without it.\n    const codeGenerationResult = compilation.codeGenerationResults.get(module)\n    const source = codeGenerationResult?.sources.get('javascript')\n\n    return source?.map() ?? undefined\n  } catch (err) {\n    console.error(`Failed to lookup module by ID (\"${id}\"):`, err)\n    return undefined\n  }\n}\n\nasync function getSource(\n  frame: {\n    file: string | null\n    lineNumber: number | null\n    column: number | null\n  },\n  options: {\n    getCompilations: () => webpack.Compilation[]\n  }\n): Promise<Source | undefined> {\n  let sourceURL = frame.file ?? ''\n  const { getCompilations } = options\n\n  sourceURL = devirtualizeReactServerURL(sourceURL)\n\n  let nativeSourceMap: SourceMap | undefined\n  try {\n    nativeSourceMap = findSourceMap(sourceURL)\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (nativeSourceMap !== undefined) {\n    const sourceMapPayload = nativeSourceMap.payload\n    return {\n      type: 'file',\n      sourceMap: findApplicableSourceMapPayload(\n        frame.lineNumber ?? 0,\n        frame.column ?? 0,\n        sourceMapPayload\n      )!,\n\n      ignoredSources: getIgnoredSources(\n        // @ts-expect-error -- TODO: Support IndexSourceMap\n        sourceMapPayload\n      ),\n      moduleURL: sourceURL,\n    }\n  }\n\n  if (path.isAbsolute(sourceURL)) {\n    sourceURL = pathToFileURL(sourceURL).href\n  }\n\n  if (sourceURL.startsWith('file:')) {\n    const sourceMap = await getSourceMapFromFile(sourceURL)\n    return sourceMap\n      ? {\n          type: 'file',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          moduleURL: sourceURL,\n        }\n      : undefined\n  }\n\n  // webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n  // webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n  const moduleId = sourceURL\n    .replace(/^(webpack-internal:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)/, '')\n    .replace(/\\?\\d+$/, '')\n\n  // (rsc)/./src/hello.tsx => ./src/hello.tsx\n  const moduleURL = moduleId.replace(/^(\\(.*\\)\\/?)/, '')\n\n  for (const compilation of getCompilations()) {\n    const sourceMap = await getSourceMapFromCompilation(moduleId, compilation)\n\n    if (sourceMap) {\n      const ignoredSources = getIgnoredSources(sourceMap)\n      return {\n        type: 'bundle',\n        sourceMap,\n        compilation,\n        moduleId,\n        moduleURL,\n        ignoredSources,\n      }\n    }\n  }\n\n  return undefined\n}\n\nexport async function getOriginalStackFrames({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frames,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frames: StackFrame[]\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFramesResponse> {\n  const frameResponses = await Promise.all(\n    frames.map(\n      (frame): Promise<OriginalStackFramesResponse[number]> =>\n        getOriginalStackFrame({\n          isServer,\n          isEdgeServer,\n          isAppDirectory,\n          frame,\n          clientStats,\n          serverStats,\n          edgeServerStats,\n          rootDirectory,\n        }).then(\n          (value) => {\n            return {\n              status: 'fulfilled',\n              value,\n            }\n          },\n          (reason) => {\n            return {\n              status: 'rejected',\n              reason: inspect(reason, { colors: false }),\n            }\n          }\n        )\n    )\n  )\n\n  ignoreListAnonymousStackFramesIfSandwiched(frameResponses)\n\n  return frameResponses\n}\n\nasync function getOriginalStackFrame({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frame,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frame: StackFrame\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFrameResponse> {\n  const filename = frame.file ?? ''\n  const source = await getSource(frame, {\n    getCompilations: () => {\n      const compilations: webpack.Compilation[] = []\n\n      // Try Client Compilation first. In `pages` we leverage\n      // `isClientError` to check. In `app` it depends on if it's a server\n      // / client component and when the code throws. E.g. during HTML\n      // rendering it's the server/edge compilation.\n      if ((!isEdgeServer && !isServer) || isAppDirectory) {\n        const compilation = clientStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Server Compilation. In `pages` this could be something\n      // imported in getServerSideProps/getStaticProps as the code for\n      // those is tree-shaken. In `app` this finds server components and\n      // code that was imported from a server component. It also covers\n      // when client component code throws during HTML rendering.\n      if (isServer || isAppDirectory) {\n        const compilation = serverStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Edge Server Compilation. Both cases are the same as Server\n      // Compilation, main difference is that it covers `runtime: 'edge'`\n      // pages/app routes.\n      if (isEdgeServer || isAppDirectory) {\n        const compilation = edgeServerStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      return compilations\n    },\n  })\n\n  let defaultNormalizedStackFrameLocation = frame.file\n  if (\n    defaultNormalizedStackFrameLocation !== null &&\n    defaultNormalizedStackFrameLocation.startsWith('file://')\n  ) {\n    defaultNormalizedStackFrameLocation = path.relative(\n      rootDirectory,\n      fileURLToPath(defaultNormalizedStackFrameLocation)\n    )\n  }\n  // This stack frame is used for the one that couldn't locate the source or source mapped frame\n  const defaultStackFrame: IgnorableStackFrame = {\n    file: defaultNormalizedStackFrameLocation,\n    lineNumber: frame.lineNumber,\n    column: frame.column ?? 1,\n    methodName: frame.methodName,\n    ignored: shouldIgnoreSource(filename),\n    arguments: [],\n  }\n  if (!source) {\n    // return original stack frame with no source map\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n  defaultStackFrame.ignored ||= sourceMapIgnoreListsEverything(source.sourceMap)\n\n  const originalStackFrameResponse = await createOriginalStackFrame({\n    ignoredByDefault: defaultStackFrame.ignored,\n    frame,\n    source,\n    rootDirectory,\n  })\n\n  if (!originalStackFrameResponse) {\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n\n  return originalStackFrameResponse\n}\n\nexport function getOverlayMiddleware(options: {\n  rootDirectory: string\n  isSrcDir: boolean\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { rootDirectory, isSrcDir, clientStats, serverStats, edgeServerStats } =\n    options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      try {\n        const { frames, isServer, isEdgeServer, isAppDirectory } = JSON.parse(\n          body\n        ) as OriginalStackFramesRequest\n\n        return middlewareResponse.json(\n          res,\n          await getOriginalStackFrames({\n            isServer,\n            isEdgeServer,\n            isAppDirectory,\n            frames: frames.map((frame) => ({\n              ...frame,\n              lineNumber: frame.lineNumber ?? 0,\n              column: frame.column ?? 0,\n            })),\n            clientStats,\n            serverStats,\n            edgeServerStats,\n            rootDirectory,\n          })\n        )\n      } catch (err) {\n        return middlewareResponse.badRequest(res)\n      }\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = {\n        file: searchParams.get('file') as string,\n        methodName: searchParams.get('methodName') as string,\n        lineNumber: parseInt(searchParams.get('lineNumber') ?? '0', 10) || 0,\n        column: parseInt(searchParams.get('column') ?? '0', 10) || 0,\n        arguments: searchParams.getAll('arguments').filter(Boolean),\n      } satisfies StackFrame\n\n      if (!frame.file) return middlewareResponse.badRequest(res)\n\n      let openEditorResult\n      const isAppRelativePath = searchParams.get('isAppRelativePath') === '1'\n      if (isAppRelativePath) {\n        const relativeFilePath = searchParams.get('file') || ''\n        const absoluteFilePath = path.join(\n          rootDirectory,\n          'app',\n          isSrcDir ? 'src' : '',\n          relativeFilePath\n        )\n        openEditorResult = await openFileInEditor(absoluteFilePath, 1, 1)\n      } else {\n        // frame files may start with their webpack layer, like (middleware)/middleware.js\n        const filePath = path.resolve(\n          rootDirectory,\n          frame.file.replace(/^\\([^)]+\\)\\//, '')\n        )\n        openEditorResult = await openFileInEditor(\n          filePath,\n          frame.lineNumber,\n          frame.column ?? 1\n        )\n      }\n      if (openEditorResult.error) {\n        console.error('Failed to launch editor:', openEditorResult.error)\n        return middlewareResponse.internalServerError(\n          res,\n          openEditorResult.error\n        )\n      }\n      if (!openEditorResult.found) {\n        return middlewareResponse.notFound(res)\n      }\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(options: {\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { clientStats, serverStats, edgeServerStats } = options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    const filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    let source: Source | undefined\n\n    try {\n      source = await getSource(\n        {\n          file: filename,\n          // Webpack doesn't use Index Source Maps\n          lineNumber: null,\n          column: null,\n        },\n        {\n          getCompilations: () => {\n            const compilations: webpack.Compilation[] = []\n\n            for (const stats of [\n              clientStats(),\n              serverStats(),\n              edgeServerStats(),\n            ]) {\n              if (stats?.compilation) {\n                compilations.push(stats.compilation)\n              }\n            }\n\n            return compilations\n          },\n        }\n      )\n    } catch (error) {\n      return middlewareResponse.internalServerError(res, error)\n    }\n\n    if (!source) {\n      return middlewareResponse.noContent(res)\n    }\n\n    return middlewareResponse.json(res, source.sourceMap)\n  }\n}\n"], "names": ["createOriginalStackFrame", "getIgnoredSources", "getOriginalStackFrames", "getOverlayMiddleware", "getSourceMapMiddleware", "shouldIgnoreSource", "sourceURL", "includes", "startsWith", "getModuleById", "id", "compilation", "chunkGraph", "modules", "find", "module", "getModuleId", "findModuleNotFoundFromError", "errorMessage", "match", "getSourcePath", "source", "fileURLToPath", "replace", "findOriginalSourcePositionAndContent", "sourceMap", "position", "consumer", "SourceMapConsumer", "cause", "console", "error", "Error", "file", "sourcePosition", "originalPositionFor", "line", "lineNumber", "column", "sourceContent", "sourceContentFor", "destroy", "ignoreList", "Set", "moduleFilenames", "sources", "index", "length", "webpackSourceURL", "formattedFilePath", "formatFrameSourceFile", "add", "ignoredSources", "map", "url", "ignored", "has", "indexOf", "content", "sourcesContent", "isIgnoredSource", "ignoredSource", "findOriginalSourcePositionAndContentFromCompilation", "moduleId", "importedModule", "buildInfo", "importLocByPath", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rootDirectory", "frame", "moduleNotFound", "result", "type", "undefined", "moduleURL", "sourcePath", "filePath", "path", "resolve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relative", "traced", "methodName", "arguments", "originalStackFrame", "originalCodeFrame", "getOriginalCodeFrame", "getSourceMapFromCompilation", "codeGenerationResult", "codeGenerationResults", "err", "getSource", "options", "getCompilations", "devirtualizeReactServerURL", "nativeSourceMap", "findSourceMap", "sourceMapPayload", "payload", "findApplicableSourceMapPayload", "isAbsolute", "pathToFileURL", "href", "getSourceMapFromFile", "isServer", "isEdgeServer", "isAppDirectory", "frames", "clientStats", "serverStats", "edgeServerStats", "frameResponses", "Promise", "all", "getOriginalStackFrame", "then", "value", "status", "reason", "inspect", "colors", "ignoreListAnonymousStackFramesIfSandwiched", "filename", "compilations", "push", "defaultNormalizedStackFrameLocation", "defaultStackFrame", "sourceMapIgnoreListsEverything", "originalStackFrameResponse", "isSrcDir", "req", "res", "next", "pathname", "searchParams", "URL", "method", "middlewareResponse", "badRequest", "body", "reject", "data", "on", "chunk", "JSON", "parse", "json", "parseInt", "getAll", "filter", "Boolean", "openEditorResult", "isAppRelativePath", "relativeFilePath", "absoluteFilePath", "join", "openFileInEditor", "internalServerError", "found", "notFound", "noContent", "stats"], "mappings": ";;;;;;;;;;;;;;;;;;IA0LsBA,wBAAwB;eAAxBA;;IAnDNC,iBAAiB;eAAjBA;;IAiPMC,sBAAsB;eAAtBA;;IAkKNC,oBAAoB;eAApBA;;IA2GAC,sBAAsB;eAAtBA;;;wBAroB8B;6DAC7B;qBAC4B;6BACX;sCAEG;4BAO9B;8BAC0B;wBAO1B;oCAC4B;mCAQG;sBAEd;;;;;;AAExB,SAASC,mBAAmBC,SAAiB;IAC3C,OACEA,UAAUC,QAAQ,CAAC,mBACnB,2EAA2E;IAC3ED,UAAUC,QAAQ,CAAC,gBACnBD,UAAUE,UAAU,CAAC;AAEzB;AA6BA,SAASC,cACPC,EAAsB,EACtBC,WAAgC;IAEhC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE,GAAGF;IAEhC,OAAO;WAAIE;KAAQ,CAACC,IAAI,CAAC,CAACC,UAAWH,WAAWI,WAAW,CAACD,aAAYL;AAC1E;AAEA,SAASO,4BAA4BC,YAAgC;QAC5DA;IAAP,OAAOA,iCAAAA,sBAAAA,aAAcC,KAAK,CAAC,wCAApBD,mBAAyC,CAAC,EAAE;AACrD;AAEA,SAASE,cAAcC,MAAc;IACnC,IAAIA,OAAOb,UAAU,CAAC,YAAY;QAChC,OAAOc,IAAAA,kBAAa,EAACD;IACvB;IACA,OAAOA,OAAOE,OAAO,CAAC,qDAAqD;AAC7E;AAEA;;CAEC,GACD,eAAeC,qCACbC,SAAiC,EACjCC,QAA8D;IAE9D,IAAIC;IACJ,IAAI;QACFA,WAAW,MAAM,IAAIC,8BAAiB,CAACH;IACzC,EAAE,OAAOI,OAAO;QACdC,QAAQC,KAAK,CACX,qBAGC,CAHD,IAAIC,MACF,GAAGP,UAAUQ,IAAI,CAAC,wFAAwF,CAAC,EAC3G;YAAEJ;QAAM,IAFV,qBAAA;mBAAA;wBAAA;0BAAA;QAGA;QAEF,OAAO;IACT;IAEA,IAAI;QACF,MAAMK,iBAAiBP,SAASQ,mBAAmB,CAAC;YAClDC,MAAMV,SAASW,UAAU,IAAI;YAC7B,mDAAmD;YACnDC,QAAQ,AAACZ,CAAAA,SAASY,MAAM,IAAI,CAAA,IAAK;QACnC;QAEA,IAAI,CAACJ,eAAeb,MAAM,EAAE;YAC1B,OAAO;QACT;QAEA,MAAMkB,gBACJZ,SAASa,gBAAgB,CACvBN,eAAeb,MAAM,EACrB,uBAAuB,GAAG,SACvB;QAEP,OAAO;YACLa;YACAK;QACF;IACF,SAAU;QACRZ,SAASc,OAAO;IAClB;AACF;AAEO,SAASxC,kBACdwB,SAAmD;IAEnD,MAAMiB,aAAa,IAAIC,IAAYlB,UAAUiB,UAAU,IAAI,EAAE;IAC7D,MAAME,kBAAkBnB,CAAAA,6BAAAA,UAAWoB,OAAO,KAAI,EAAE;IAEhD,IAAK,IAAIC,QAAQ,GAAGA,QAAQF,gBAAgBG,MAAM,EAAED,QAAS;QAC3D,iDAAiD;QACjD,MAAME,mBAAmBJ,eAAe,CAACE,MAAM;QAC/C,0CAA0C;QAC1C,MAAMG,oBAAoBC,IAAAA,wCAAqB,EAACF;QAChD,IAAI3C,mBAAmB4C,oBAAoB;YACzCP,WAAWS,GAAG,CAACL;QACjB;IACF;IAEA,MAAMM,iBAAiB3B,UAAUoB,OAAO,CAACQ,GAAG,CAAC,CAAChC,QAAQyB;YAIzCrB;QAHX,OAAO;YACL6B,KAAKjC;YACLkC,SAASb,WAAWc,GAAG,CAAC/B,UAAUoB,OAAO,CAACY,OAAO,CAACpC;YAClDqC,SAASjC,EAAAA,4BAAAA,UAAUkC,cAAc,qBAAxBlC,yBAA0B,CAACqB,MAAM,KAAI;QAChD;IACF;IACA,OAAOM;AACT;AAEA,SAASQ,gBACPvC,MAAc,EACda,cAAuD;IAEvD,IAAIA,eAAeb,MAAM,IAAI,MAAM;QACjC,OAAO;IACT;IACA,KAAK,MAAMwC,iBAAiBxC,OAAO+B,cAAc,CAAE;QACjD,IAAIS,cAAcN,OAAO,IAAIM,cAAcP,GAAG,KAAKpB,eAAeb,MAAM,EAAE;YACxE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASyC,oDACPC,QAA4B,EAC5BC,cAAsB,EACtBrD,WAAgC;QAGzBI,mCAAAA;IADP,MAAMA,UAASN,cAAcsD,UAAUpD;IACvC,OAAOI,CAAAA,4BAAAA,oBAAAA,QAAQkD,SAAS,sBAAjBlD,oCAAAA,kBAAmBmD,eAAe,qBAAlCnD,kCAAoCoD,GAAG,CAACH,oBAAmB;AACpE;AAEO,eAAehE,yBAAyB,EAC7CoE,gBAAgB,EAChB/C,MAAM,EACNgD,aAAa,EACbC,KAAK,EACLpD,YAAY,EAQb;QA+CK,sEAAsE;IACtE,4EAA4E;IAC5E,kCAAkC;IAClC,oGAAoG;IACpG,gHAAgH;IAChH,kGAAkG;IAClGoD,2BAAAA;IApDJ,MAAMC,iBAAiBtD,4BAA4BC;IACnD,MAAMsD,SAAS,MAAM,AAAC,CAAA;QACpB,IAAID,gBAAgB;YAClB,IAAIlD,OAAOoD,IAAI,KAAK,QAAQ;gBAC1B,OAAOC;YACT;YAEA,OAAOZ,oDACLzC,OAAO0C,QAAQ,EACfQ,gBACAlD,OAAOV,WAAW;QAEtB;QACA,OAAOa,qCAAqCH,OAAOI,SAAS,EAAE6C;IAChE,CAAA;IAEA,IAAI,CAACE,QAAQ;QACX,OAAO;IACT;IACA,MAAM,EAAEtC,cAAc,EAAEK,aAAa,EAAE,GAAGiC;IAE1C,IAAI,CAACtC,eAAeb,MAAM,EAAE;QAC1B,OAAO;IACT;IAEA,MAAMkC,UACJa,oBACAR,gBAAgBvC,QAAQa,mBACxB,oFAAoF;IACpF,kDAAkD;IAClD7B,mBAAmBgB,OAAOsD,SAAS;IAErC,MAAMC,aAAaxD,cAEjB,AADA,oFAAoF;IACnFc,CAAAA,eAAeb,MAAM,CAAEd,QAAQ,CAAC,OAC7Bc,OAAOsD,SAAS,GAChBzC,eAAeb,MAAM,AAAD,KAAMA,OAAOsD,SAAS;IAEhD,MAAME,WAAWC,aAAI,CAACC,OAAO,CAACV,eAAeO;IAC7C,MAAMI,mBAAmBF,aAAI,CAACG,QAAQ,CAACZ,eAAeQ;IAEtD,MAAMK,SAA8B;QAClCjD,MAAM+C;QACN3C,YAAYH,eAAeE,IAAI;QAC/BE,QAAQ,AAACJ,CAAAA,eAAeI,MAAM,IAAI,CAAA,IAAK;QACvC6C,UAAU,GAORb,oBAAAA,MAAMa,UAAU,sBAAhBb,4BAAAA,kBACI/C,OAAO,CAAC,8BAA8B,+BAD1C+C,0BAEI/C,OAAO,CAAC,wBAAwB;QACtC6D,WAAW,EAAE;QACb7B;IACF;IAEA,OAAO;QACL8B,oBAAoBH;QACpBI,mBAAmBC,IAAAA,4BAAoB,EAACL,QAAQ3C;IAClD;AACF;AAEA,eAAeiD,4BACb9E,EAAU,EACVC,WAAgC;IAEhC,IAAI;QACF,MAAMI,UAASN,cAAcC,IAAIC;QAEjC,IAAI,CAACI,SAAQ;YACX,OAAO2D;QACT;QAEA,uEAAuE;QACvE,wEAAwE;QACxE,cAAc;QACd,MAAMe,uBAAuB9E,YAAY+E,qBAAqB,CAACvB,GAAG,CAACpD;QACnE,MAAMM,SAASoE,wCAAAA,qBAAsB5C,OAAO,CAACsB,GAAG,CAAC;QAEjD,OAAO9C,CAAAA,0BAAAA,OAAQgC,GAAG,OAAMqB;IAC1B,EAAE,OAAOiB,KAAK;QACZ7D,QAAQC,KAAK,CAAC,CAAC,gCAAgC,EAAErB,GAAG,GAAG,CAAC,EAAEiF;QAC1D,OAAOjB;IACT;AACF;AAEA,eAAekB,UACbtB,KAIC,EACDuB,OAEC;IAED,IAAIvF,YAAYgE,MAAMrC,IAAI,IAAI;IAC9B,MAAM,EAAE6D,eAAe,EAAE,GAAGD;IAE5BvF,YAAYyF,IAAAA,sCAA0B,EAACzF;IAEvC,IAAI0F;IACJ,IAAI;QACFA,kBAAkBC,IAAAA,qBAAa,EAAC3F;IAClC,EAAE,OAAOuB,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIG,MACR,GAAG1B,UAAU,wFAAwF,CAAC,EACtG;YAAEuB;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAImE,oBAAoBtB,WAAW;QACjC,MAAMwB,mBAAmBF,gBAAgBG,OAAO;QAChD,OAAO;YACL1B,MAAM;YACNhD,WAAW2E,IAAAA,0CAA8B,EACvC9B,MAAMjC,UAAU,IAAI,GACpBiC,MAAMhC,MAAM,IAAI,GAChB4D;YAGF9C,gBAAgBnD,kBACd,mDAAmD;YACnDiG;YAEFvB,WAAWrE;QACb;IACF;IAEA,IAAIwE,aAAI,CAACuB,UAAU,CAAC/F,YAAY;QAC9BA,YAAYgG,IAAAA,kBAAa,EAAChG,WAAWiG,IAAI;IAC3C;IAEA,IAAIjG,UAAUE,UAAU,CAAC,UAAU;QACjC,MAAMiB,YAAY,MAAM+E,IAAAA,0CAAoB,EAAClG;QAC7C,OAAOmB,YACH;YACEgD,MAAM;YACNhD;YACA2B,gBAAgBnD,kBAAkBwB;YAClCkD,WAAWrE;QACb,IACAoE;IACN;IAEA,yDAAyD;IACzD,oDAAoD;IACpD,MAAMX,WAAWzD,UACdiB,OAAO,CAAC,oDAAoD,IAC5DA,OAAO,CAAC,UAAU;IAErB,2CAA2C;IAC3C,MAAMoD,YAAYZ,SAASxC,OAAO,CAAC,gBAAgB;IAEnD,KAAK,MAAMZ,eAAemF,kBAAmB;QAC3C,MAAMrE,YAAY,MAAM+D,4BAA4BzB,UAAUpD;QAE9D,IAAIc,WAAW;YACb,MAAM2B,iBAAiBnD,kBAAkBwB;YACzC,OAAO;gBACLgD,MAAM;gBACNhD;gBACAd;gBACAoD;gBACAY;gBACAvB;YACF;QACF;IACF;IAEA,OAAOsB;AACT;AAEO,eAAexE,uBAAuB,EAC3CuG,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,WAAW,EACXC,eAAe,EACf1C,aAAa,EAUd;IACC,MAAM2C,iBAAiB,MAAMC,QAAQC,GAAG,CACtCN,OAAOvD,GAAG,CACR,CAACiB,QACC6C,sBAAsB;YACpBV;YACAC;YACAC;YACArC;YACAuC;YACAC;YACAC;YACA1C;QACF,GAAG+C,IAAI,CACL,CAACC;YACC,OAAO;gBACLC,QAAQ;gBACRD;YACF;QACF,GACA,CAACE;YACC,OAAO;gBACLD,QAAQ;gBACRC,QAAQC,IAAAA,aAAO,EAACD,QAAQ;oBAAEE,QAAQ;gBAAM;YAC1C;QACF;IAKRC,IAAAA,kDAA0C,EAACV;IAE3C,OAAOA;AACT;AAEA,eAAeG,sBAAsB,EACnCV,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdrC,KAAK,EACLuC,WAAW,EACXC,WAAW,EACXC,eAAe,EACf1C,aAAa,EAUd;IACC,MAAMsD,WAAWrD,MAAMrC,IAAI,IAAI;IAC/B,MAAMZ,SAAS,MAAMuE,UAAUtB,OAAO;QACpCwB,iBAAiB;YACf,MAAM8B,eAAsC,EAAE;YAE9C,uDAAuD;YACvD,oEAAoE;YACpE,gEAAgE;YAChE,8CAA8C;YAC9C,IAAI,AAAC,CAAClB,gBAAgB,CAACD,YAAaE,gBAAgB;oBAC9BE;gBAApB,MAAMlG,eAAckG,eAAAA,kCAAAA,aAAelG,WAAW;gBAE9C,IAAIA,aAAa;oBACfiH,aAAaC,IAAI,CAAClH;gBACpB;YACF;YAEA,6DAA6D;YAC7D,gEAAgE;YAChE,kEAAkE;YAClE,iEAAiE;YACjE,2DAA2D;YAC3D,IAAI8F,YAAYE,gBAAgB;oBACVG;gBAApB,MAAMnG,eAAcmG,eAAAA,kCAAAA,aAAenG,WAAW;gBAE9C,IAAIA,aAAa;oBACfiH,aAAaC,IAAI,CAAClH;gBACpB;YACF;YAEA,iEAAiE;YACjE,mEAAmE;YACnE,oBAAoB;YACpB,IAAI+F,gBAAgBC,gBAAgB;oBACdI;gBAApB,MAAMpG,eAAcoG,mBAAAA,sCAAAA,iBAAmBpG,WAAW;gBAElD,IAAIA,aAAa;oBACfiH,aAAaC,IAAI,CAAClH;gBACpB;YACF;YAEA,OAAOiH;QACT;IACF;IAEA,IAAIE,sCAAsCxD,MAAMrC,IAAI;IACpD,IACE6F,wCAAwC,QACxCA,oCAAoCtH,UAAU,CAAC,YAC/C;QACAsH,sCAAsChD,aAAI,CAACG,QAAQ,CACjDZ,eACA/C,IAAAA,kBAAa,EAACwG;IAElB;IACA,8FAA8F;IAC9F,MAAMC,oBAAyC;QAC7C9F,MAAM6F;QACNzF,YAAYiC,MAAMjC,UAAU;QAC5BC,QAAQgC,MAAMhC,MAAM,IAAI;QACxB6C,YAAYb,MAAMa,UAAU;QAC5B5B,SAASlD,mBAAmBsH;QAC5BvC,WAAW,EAAE;IACf;IACA,IAAI,CAAC/D,QAAQ;QACX,iDAAiD;QACjD,OAAO;YACLgE,oBAAoB0C;YACpBzC,mBAAmB;QACrB;IACF;IACAyC,kBAAkBxE,OAAO,KAAKyE,IAAAA,0CAA8B,EAAC3G,OAAOI,SAAS;IAE7E,MAAMwG,6BAA6B,MAAMjI,yBAAyB;QAChEoE,kBAAkB2D,kBAAkBxE,OAAO;QAC3Ce;QACAjD;QACAgD;IACF;IAEA,IAAI,CAAC4D,4BAA4B;QAC/B,OAAO;YACL5C,oBAAoB0C;YACpBzC,mBAAmB;QACrB;IACF;IAEA,OAAO2C;AACT;AAEO,SAAS9H,qBAAqB0F,OAMpC;IACC,MAAM,EAAExB,aAAa,EAAE6D,QAAQ,EAAErB,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAC1ElB;IAEF,OAAO,eACLsC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEL,IAAI7E,GAAG,EAAE;QAE/D,IAAIgF,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAOC,sCAAkB,CAACC,UAAU,CAACP;YACvC;YAEA,MAAMQ,OAAO,MAAM,IAAI3B,QAAgB,CAAClC,SAAS8D;gBAC/C,IAAIC,OAAO;gBACXX,IAAIY,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAb,IAAIY,EAAE,CAAC,OAAO,IAAMhE,QAAQ+D;gBAC5BX,IAAIY,EAAE,CAAC,SAASF;YAClB;YAEA,IAAI;gBACF,MAAM,EAAEjC,MAAM,EAAEH,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAE,GAAGsC,KAAKC,KAAK,CACnEN;gBAGF,OAAOF,sCAAkB,CAACS,IAAI,CAC5Bf,KACA,MAAMlI,uBAAuB;oBAC3BuG;oBACAC;oBACAC;oBACAC,QAAQA,OAAOvD,GAAG,CAAC,CAACiB,QAAW,CAAA;4BAC7B,GAAGA,KAAK;4BACRjC,YAAYiC,MAAMjC,UAAU,IAAI;4BAChCC,QAAQgC,MAAMhC,MAAM,IAAI;wBAC1B,CAAA;oBACAuE;oBACAC;oBACAC;oBACA1C;gBACF;YAEJ,EAAE,OAAOsB,KAAK;gBACZ,OAAO+C,sCAAkB,CAACC,UAAU,CAACP;YACvC;QACF,OAAO,IAAIE,aAAa,2BAA2B;YACjD,MAAMhE,QAAQ;gBACZrC,MAAMsG,aAAapE,GAAG,CAAC;gBACvBgB,YAAYoD,aAAapE,GAAG,CAAC;gBAC7B9B,YAAY+G,SAASb,aAAapE,GAAG,CAAC,iBAAiB,KAAK,OAAO;gBACnE7B,QAAQ8G,SAASb,aAAapE,GAAG,CAAC,aAAa,KAAK,OAAO;gBAC3DiB,WAAWmD,aAAac,MAAM,CAAC,aAAaC,MAAM,CAACC;YACrD;YAEA,IAAI,CAACjF,MAAMrC,IAAI,EAAE,OAAOyG,sCAAkB,CAACC,UAAU,CAACP;YAEtD,IAAIoB;YACJ,MAAMC,oBAAoBlB,aAAapE,GAAG,CAAC,yBAAyB;YACpE,IAAIsF,mBAAmB;gBACrB,MAAMC,mBAAmBnB,aAAapE,GAAG,CAAC,WAAW;gBACrD,MAAMwF,mBAAmB7E,aAAI,CAAC8E,IAAI,CAChCvF,eACA,OACA6D,WAAW,QAAQ,IACnBwB;gBAEFF,mBAAmB,MAAMK,IAAAA,8BAAgB,EAACF,kBAAkB,GAAG;YACjE,OAAO;gBACL,kFAAkF;gBAClF,MAAM9E,WAAWC,aAAI,CAACC,OAAO,CAC3BV,eACAC,MAAMrC,IAAI,CAACV,OAAO,CAAC,gBAAgB;gBAErCiI,mBAAmB,MAAMK,IAAAA,8BAAgB,EACvChF,UACAP,MAAMjC,UAAU,EAChBiC,MAAMhC,MAAM,IAAI;YAEpB;YACA,IAAIkH,iBAAiBzH,KAAK,EAAE;gBAC1BD,QAAQC,KAAK,CAAC,4BAA4ByH,iBAAiBzH,KAAK;gBAChE,OAAO2G,sCAAkB,CAACoB,mBAAmB,CAC3C1B,KACAoB,iBAAiBzH,KAAK;YAE1B;YACA,IAAI,CAACyH,iBAAiBO,KAAK,EAAE;gBAC3B,OAAOrB,sCAAkB,CAACsB,QAAQ,CAAC5B;YACrC;YACA,OAAOM,sCAAkB,CAACuB,SAAS,CAAC7B;QACtC;QAEA,OAAOC;IACT;AACF;AAEO,SAASjI,uBAAuByF,OAItC;IACC,MAAM,EAAEgB,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAGlB;IAEtD,OAAO,eACLsC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEL,IAAI7E,GAAG,EAAE;QAE/D,IAAIgF,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,MAAMV,WAAWY,aAAapE,GAAG,CAAC;QAElC,IAAI,CAACwD,UAAU;YACb,OAAOe,sCAAkB,CAACC,UAAU,CAACP;QACvC;QAEA,IAAI/G;QAEJ,IAAI;YACFA,SAAS,MAAMuE,UACb;gBACE3D,MAAM0F;gBACN,wCAAwC;gBACxCtF,YAAY;gBACZC,QAAQ;YACV,GACA;gBACEwD,iBAAiB;oBACf,MAAM8B,eAAsC,EAAE;oBAE9C,KAAK,MAAMsC,SAAS;wBAClBrD;wBACAC;wBACAC;qBACD,CAAE;wBACD,IAAImD,yBAAAA,MAAOvJ,WAAW,EAAE;4BACtBiH,aAAaC,IAAI,CAACqC,MAAMvJ,WAAW;wBACrC;oBACF;oBAEA,OAAOiH;gBACT;YACF;QAEJ,EAAE,OAAO7F,OAAO;YACd,OAAO2G,sCAAkB,CAACoB,mBAAmB,CAAC1B,KAAKrG;QACrD;QAEA,IAAI,CAACV,QAAQ;YACX,OAAOqH,sCAAkB,CAACuB,SAAS,CAAC7B;QACtC;QAEA,OAAOM,sCAAkB,CAACS,IAAI,CAACf,KAAK/G,OAAOI,SAAS;IACtD;AACF", "ignoreList": [0]}