"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[926],{1414:(e,r,t)=>{e.exports=t(2436)},2436:(e,r,t)=>{var o=t(2115),n="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},a=o.useState,s=o.useEffect,l=o.useLayoutEffect,i=o.useDebugValue;function c(e){var r=e.getSnapshot;e=e.value;try{var t=r();return!n(e,t)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var t=r(),o=a({inst:{value:t,getSnapshot:r}}),n=o[0].inst,d=o[1];return l(function(){n.value=t,n.getSnapshot=r,c(n)&&d({inst:n})},[e,t,r]),s(function(){return c(n)&&d({inst:n}),e(function(){c(n)&&d({inst:n})})},[e]),i(t),t};r.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:d},2596:(e,r,t)=>{t.d(r,{$:()=>o});function o(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}},2664:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let o=t(9991),n=t(7102);function a(e){if(!(0,o.isAbsoluteUrl)(e))return!0;try{let r=(0,o.getLocationOrigin)(),t=new URL(e,r);return t.origin===r&&(0,n.hasBasePath)(t.pathname)}catch(e){return!1}}},2757:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return l},urlObjectKeys:function(){return s}});let o=t(6966)._(t(8859)),n=/https?|ftp|gopher|file/;function a(e){let{auth:r,hostname:t}=e,a=e.protocol||"",s=e.pathname||"",l=e.hash||"",i=e.query||"",c=!1;r=r?encodeURIComponent(r).replace(/%3A/i,":")+"@":"",e.host?c=r+e.host:t&&(c=r+(~t.indexOf(":")?"["+t+"]":t),e.port&&(c+=":"+e.port)),i&&"object"==typeof i&&(i=String(o.urlQueryToSearchParams(i)));let d=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||n.test(a))&&!1!==c?(c="//"+(c||""),s&&"/"!==s[0]&&(s="/"+s)):c||(c=""),l&&"#"!==l[0]&&(l="#"+l),d&&"?"!==d[0]&&(d="?"+d),""+a+c+(s=s.replace(/[?#]/g,encodeURIComponent))+(d=d.replace("#","%23"))+l}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return a(e)}},3180:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}},6874:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return g},useLinkStatus:function(){return y}});let o=t(6966),n=t(5155),a=o._(t(2115)),s=t(2757),l=t(5227),i=t(9818),c=t(6654),d=t(9991),u=t(5929);t(3230);let p=t(4930),f=t(2664),m=t(6634);function b(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let r,t,o,[s,g]=(0,a.useOptimistic)(p.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:k,as:x,children:w,prefetch:v=null,passHref:z,replace:P,shallow:j,scroll:E,onClick:S,onMouseEnter:O,onTouchStart:_,legacyBehavior:N=!1,onNavigate:C,ref:M,unstable_dynamicOnHover:T,...I}=e;r=w,N&&("string"==typeof r||"number"==typeof r)&&(r=(0,n.jsx)("a",{children:r}));let A=a.default.useContext(l.AppRouterContext),L=!1!==v,U=null===v||"auto"===v?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:R,as:G}=a.default.useMemo(()=>{let e=b(k);return{href:e,as:x?b(x):e}},[k,x]);N&&(t=a.default.Children.only(r));let D=N?t&&"object"==typeof t&&t.ref:M,F=a.default.useCallback(e=>(null!==A&&(y.current=(0,p.mountLinkInstance)(e,R,A,U,L,g)),()=>{y.current&&((0,p.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,p.unmountPrefetchableInstance)(e)}),[L,R,A,U,g]),K={ref:(0,c.useMergedRef)(F,D),onClick(e){N||"function"!=typeof S||S(e),N&&t.props&&"function"==typeof t.props.onClick&&t.props.onClick(e),A&&(e.defaultPrevented||function(e,r,t,o,n,s,l){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let r=e.currentTarget.getAttribute("target");return r&&"_self"!==r||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(r)){n&&(e.preventDefault(),location.replace(r));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,m.dispatchNavigateAction)(t||r,n?"replace":"push",null==s||s,o.current)})}}(e,R,G,y,P,E,C))},onMouseEnter(e){N||"function"!=typeof O||O(e),N&&t.props&&"function"==typeof t.props.onMouseEnter&&t.props.onMouseEnter(e),A&&L&&(0,p.onNavigationIntent)(e.currentTarget,!0===T)},onTouchStart:function(e){N||"function"!=typeof _||_(e),N&&t.props&&"function"==typeof t.props.onTouchStart&&t.props.onTouchStart(e),A&&L&&(0,p.onNavigationIntent)(e.currentTarget,!0===T)}};return(0,d.isAbsoluteUrl)(G)?K.href=G:N&&!z&&("a"!==t.type||"href"in t.props)||(K.href=(0,u.addBasePath)(G)),o=N?a.default.cloneElement(t,K):(0,n.jsx)("a",{...I,...K,children:r}),(0,n.jsx)(h.Provider,{value:s,children:o})}t(3180);let h=(0,a.createContext)(p.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(h);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8859:(e,r)=>{function t(e){let r={};for(let[t,o]of e.entries()){let e=r[t];void 0===e?r[t]=o:Array.isArray(e)?e.push(o):r[t]=[e,o]}return r}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let r=new URLSearchParams;for(let[t,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)r.append(t,o(e));else r.set(t,o(n));return r}function a(e){for(var r=arguments.length,t=Array(r>1?r-1:0),o=1;o<r;o++)t[o-1]=arguments[o];for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,o]of r.entries())e.append(t,o)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{assign:function(){return a},searchParamsToUrlQuery:function(){return t},urlQueryToSearchParams:function(){return n}})},9688:(e,r,t)=>{t.d(r,{QP:()=>ee});let o=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],n=r.nextPart.get(t),a=n?o(e.slice(1),n):void 0;if(a)return a;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},n=/^\[(.+)\]$/,a=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:s(r,e)).classGroupId=t;return}if("function"==typeof e)return l(e)?void a(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{a(n,s(r,e),t,o)})})},s=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},l=e=>e.isThemeGetter,i=/\s+/;function c(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=d(e))&&(o&&(o+=" "),o+=r);return o}let d=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=d(e[o]))&&(t&&(t+=" "),t+=r);return t},u=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},p=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,f=/^\((?:(\w[\w-]*):)?(.+)\)$/i,m=/^\d+\/\d+$/,b=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,h=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,k=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>m.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),v=e=>!!e&&Number.isInteger(Number(e)),z=e=>e.endsWith("%")&&w(e.slice(0,-1)),P=e=>b.test(e),j=()=>!0,E=e=>g.test(e)&&!h.test(e),S=()=>!1,O=e=>y.test(e),_=e=>k.test(e),N=e=>!M(e)&&!R(e),C=e=>B(e,Z,S),M=e=>p.test(e),T=e=>B(e,J,E),I=e=>B(e,X,w),A=e=>B(e,Q,S),L=e=>B(e,V,_),U=e=>B(e,Y,O),R=e=>f.test(e),G=e=>q(e,J),D=e=>q(e,H),F=e=>q(e,Q),K=e=>q(e,Z),$=e=>q(e,V),W=e=>q(e,Y,!0),B=(e,r,t)=>{let o=p.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},q=(e,r,t=!1)=>{let o=f.exec(e);return!!o&&(o[1]?r(o[1]):t)},Q=e=>"position"===e||"percentage"===e,V=e=>"image"===e||"url"===e,Z=e=>"length"===e||"size"===e||"bg-size"===e,J=e=>"length"===e,X=e=>"number"===e,H=e=>"family-name"===e,Y=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...r){let t,s,l,d=function(i){let c;return s=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}})((c=r.reduce((e,r)=>r(e),e())).cacheSize),parseClassName:(e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t,o=[],n=0,a=0,s=0;for(let t=0;t<e.length;t++){let l=e[t];if(0===n&&0===a){if(":"===l){o.push(e.slice(s,t)),s=t+1;continue}if("/"===l){r=t;continue}}"["===l?n++:"]"===l?n--:"("===l?a++:")"===l&&a--}let l=0===o.length?e:e.substring(s),i=(t=l).endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t;return{modifiers:o,hasImportantModifier:i!==l,baseClassName:i,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o})(c),sortModifiers:(e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}})(c),...(e=>{let r=(e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)a(t[e],o,e,r);return o})(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),o(t,r)||(e=>{if(n.test(e)){let r=n.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}})(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&s[e]?[...o,...s[e]]:o}}})(c)}).cache.get,l=t.cache.set,d=u,u(i)};function u(e){let r=s(e);if(r)return r;let o=((e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,s=[],l=e.trim().split(i),c="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{isExternal:i,modifiers:d,hasImportantModifier:u,baseClassName:p,maybePostfixModifierPosition:f}=t(r);if(i){c=r+(c.length>0?" "+c:c);continue}let m=!!f,b=o(m?p.substring(0,f):p);if(!b){if(!m||!(b=o(p))){c=r+(c.length>0?" "+c:c);continue}m=!1}let g=a(d).join(":"),h=u?g+"!":g,y=h+b;if(s.includes(y))continue;s.push(y);let k=n(b,m);for(let e=0;e<k.length;++e){let r=k[e];s.push(h+r)}c=r+(c.length>0?" "+c:c)}return c})(e,t);return l(e,o),o}return function(){return d(c.apply(null,arguments))}}(()=>{let e=u("color"),r=u("font"),t=u("text"),o=u("font-weight"),n=u("tracking"),a=u("leading"),s=u("breakpoint"),l=u("container"),i=u("spacing"),c=u("radius"),d=u("shadow"),p=u("inset-shadow"),f=u("text-shadow"),m=u("drop-shadow"),b=u("blur"),g=u("perspective"),h=u("aspect"),y=u("ease"),k=u("animate"),E=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],O=()=>[...S(),R,M],_=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],q=()=>[R,M,i],Q=()=>[x,"full","auto",...q()],V=()=>[v,"none","subgrid",R,M],Z=()=>["auto",{span:["full",v,R,M]},v,R,M],J=()=>[v,"auto",R,M],X=()=>["auto","min","max","fr",R,M],H=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Y=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...q()],er=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],et=()=>[e,R,M],eo=()=>[...S(),F,A,{position:[R,M]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",K,C,{size:[R,M]}],es=()=>[z,G,T],el=()=>["","none","full",c,R,M],ei=()=>["",w,G,T],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[w,z,F,A],ep=()=>["","none",b,R,M],ef=()=>["none",w,R,M],em=()=>["none",w,R,M],eb=()=>[w,R,M],eg=()=>[x,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[P],breakpoint:[P],color:[j],container:[P],"drop-shadow":[P],ease:["in","out","in-out"],font:[N],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[P],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[P],shadow:[P],spacing:["px",w],text:[P],"text-shadow":[P],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,M,R,h]}],container:["container"],columns:[{columns:[w,M,R,l]}],"break-after":[{"break-after":E()}],"break-before":[{"break-before":E()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:O()}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Q()}],"inset-x":[{"inset-x":Q()}],"inset-y":[{"inset-y":Q()}],start:[{start:Q()}],end:[{end:Q()}],top:[{top:Q()}],right:[{right:Q()}],bottom:[{bottom:Q()}],left:[{left:Q()}],visibility:["visible","invisible","collapse"],z:[{z:[v,"auto",R,M]}],basis:[{basis:[x,"full","auto",l,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",M]}],grow:[{grow:["",w,R,M]}],shrink:[{shrink:["",w,R,M]}],order:[{order:[v,"first","last","none",R,M]}],"grid-cols":[{"grid-cols":V()}],"col-start-end":[{col:Z()}],"col-start":[{"col-start":J()}],"col-end":[{"col-end":J()}],"grid-rows":[{"grid-rows":V()}],"row-start-end":[{row:Z()}],"row-start":[{"row-start":J()}],"row-end":[{"row-end":J()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":X()}],"auto-rows":[{"auto-rows":X()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[...H(),"normal"]}],"justify-items":[{"justify-items":[...Y(),"normal"]}],"justify-self":[{"justify-self":["auto",...Y()]}],"align-content":[{content:["normal",...H()]}],"align-items":[{items:[...Y(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Y(),{baseline:["","last"]}]}],"place-content":[{"place-content":H()}],"place-items":[{"place-items":[...Y(),"baseline"]}],"place-self":[{"place-self":["auto",...Y()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[l,"screen",...er()]}],"min-w":[{"min-w":[l,"screen","none",...er()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[s]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,G,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,R,I]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",z,M]}],"font-family":[{font:[D,M,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,R,M]}],"line-clamp":[{"line-clamp":[w,"none",R,I]}],leading:[{leading:[a,...q()]}],"list-image":[{"list-image":["none",R,M]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",R,M]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",R,T]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[w,"auto",R,M]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",R,M]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",R,M]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},v,R,M],radial:["",R,M],conic:[v,R,M]},$,L]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,R,M]}],"outline-w":[{outline:["",w,G,T]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",d,W,U]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",p,W,U]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[w,T]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",f,W,U]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[w,R,M]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[R,M]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",R,M]}],filter:[{filter:["","none",R,M]}],blur:[{blur:ep()}],brightness:[{brightness:[w,R,M]}],contrast:[{contrast:[w,R,M]}],"drop-shadow":[{"drop-shadow":["","none",m,W,U]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",w,R,M]}],"hue-rotate":[{"hue-rotate":[w,R,M]}],invert:[{invert:["",w,R,M]}],saturate:[{saturate:[w,R,M]}],sepia:[{sepia:["",w,R,M]}],"backdrop-filter":[{"backdrop-filter":["","none",R,M]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[w,R,M]}],"backdrop-contrast":[{"backdrop-contrast":[w,R,M]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,R,M]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,R,M]}],"backdrop-invert":[{"backdrop-invert":["",w,R,M]}],"backdrop-opacity":[{"backdrop-opacity":[w,R,M]}],"backdrop-saturate":[{"backdrop-saturate":[w,R,M]}],"backdrop-sepia":[{"backdrop-sepia":["",w,R,M]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",R,M]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",R,M]}],ease:[{ease:["linear","initial",y,R,M]}],delay:[{delay:[w,R,M]}],animate:[{animate:["none",k,R,M]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,R,M]}],"perspective-origin":[{"perspective-origin":O()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[R,M,"","none","gpu","cpu"]}],"transform-origin":[{origin:O()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",R,M]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",R,M]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[w,G,T,I]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9991:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return h},NormalizeError:function(){return b},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return t},execOnce:function(){return o},getDisplayName:function(){return i},getLocationOrigin:function(){return s},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return k}});let t=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let r,t=!1;return function(){for(var o=arguments.length,n=Array(o),a=0;a<o;a++)n[a]=arguments[a];return t||(t=!0,r=e(...n)),r}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>n.test(e);function s(){let{protocol:e,hostname:r,port:t}=window.location;return e+"//"+r+(t?":"+t:"")}function l(){let{href:e}=window.location,r=s();return e.substring(r.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}async function u(e,r){let t=r.res||r.ctx&&r.ctx.res;if(!e.getInitialProps)return r.ctx&&r.Component?{pageProps:await u(r.Component,r.ctx)}:{};let o=await e.getInitialProps(r);if(t&&c(t))return o;if(!o)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class b extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class h extends Error{constructor(e,r){super(),this.message="Failed to load static file for page: "+e+" "+r}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function k(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);