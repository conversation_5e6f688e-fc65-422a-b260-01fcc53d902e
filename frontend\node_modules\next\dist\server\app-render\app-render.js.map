{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HASH_COOKIE,\n} from '../../client/components/app-router-headers'\nimport { createMetadataContext } from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags, type ImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport {\n  getShortDynamicParamType,\n  dynamicParamTypes,\n} from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport {\n  getServerModuleMap,\n  setReferenceManifestsSingleton,\n} from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createPostponedAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  PreludeState,\n  consumeDynamicAccess,\n  type DynamicAccess,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseParameter } from '../../shared/lib/router/utils/route-regex'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getIsPossibleServerAction } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../client/components/app-router-instance'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport {\n  ServerPrerenderStreamResult,\n  processPrelude,\n} from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport { INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n  type PrerenderResumeDataCache,\n  type RenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\nimport { getPreviouslyRevalidatedTags } from '../server-utils'\nimport { executeRevalidates } from '../revalidation-utils'\nimport {\n  trackPendingChunkLoad,\n  trackPendingImport,\n  trackPendingModules,\n} from './module-loading/track-module-loading.external'\nimport { isReactLargeShellError } from './react-large-shell-error'\nimport type { GlobalErrorComponent } from '../../client/components/builtin/global-error'\nimport { normalizeConventionFilePath } from './segment-explorer-path'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n} | null\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isPossibleServerAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n  /**\n   * For now, the implicit tags are common for the whole route. If we ever start\n   * rendering/revalidating segments independently, they need to move to the\n   * work unit store.\n   */\n  implicitTags: ImplicitTags\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n  readonly previewModeId: string | undefined\n}\n\nconst flightDataPathHeadKey = 'h'\nconst getFlightViewportKey = (requestId: string) => requestId + 'v'\nconst getFlightMetadataKey = (requestId: string) => requestId + 'm'\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n  readonly previouslyRevalidatedTags: string[]\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isPrefetchRequest =\n    isDevWarmupRequest ||\n    headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] !== undefined\n\n  const isHmrRefresh =\n    headers[NEXT_HMR_REFRESH_HEADER.toLowerCase()] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest =\n    isDevWarmupRequest || headers[RSC_HEADER.toLowerCase()] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n      )\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n    headers,\n    options.previewModeId\n  )\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n    previouslyRevalidatedTags,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  const components = loaderTree[2]\n  const hasGlobalNotFound = !!components['global-not-found']\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['global-not-found'] ?? components['not-found'],\n        },\n      ],\n    },\n    // When global-not-found is present, skip layout from components\n    hasGlobalNotFound ? components : {},\n  ]\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n\n    const key = segmentParam.param\n\n    let value = params[key]\n\n    if (fallbackRouteParams && fallbackRouteParams.has(segmentParam.param)) {\n      value = fallbackRouteParams.get(segmentParam.param)\n    } else if (Array.isArray(value)) {\n      value = value.map((i) => encodeURIComponent(i))\n    } else if (typeof value === 'string') {\n      value = encodeURIComponent(value)\n    }\n\n    if (!value) {\n      const isCatchall = segmentParam.type === 'catchall'\n      const isOptionalCatchall = segmentParam.type === 'optional-catchall'\n\n      if (isCatchall || isOptionalCatchall) {\n        const dynamicParamType = dynamicParamTypes[segmentParam.type]\n        // handle the case where an optional catchall does not have a value,\n        // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n        if (isOptionalCatchall) {\n          return {\n            param: key,\n            value: null,\n            type: dynamicParamType,\n            treeSegment: [key, '', dynamicParamType],\n          }\n        }\n\n        // handle the case where a catchall or optional catchall does not have a value,\n        // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n        value = pagePath\n          .split('/')\n          // remove the first empty string\n          .slice(1)\n          // replace any dynamic params with the actual values\n          .flatMap((pathSegment) => {\n            const param = parseParameter(pathSegment)\n            // if the segment matches a param, return the param value\n            // otherwise, it's a static segment, so just return that\n            return params[param.key] ?? param.key\n          })\n\n        return {\n          param: key,\n          value,\n          type: dynamicParamType,\n          // This value always has to be a string.\n          treeSegment: [key, value.join('/'), dynamicParamType],\n        }\n      }\n    }\n\n    const type = getShortDynamicParamType(segmentParam.type)\n\n    return {\n      param: key,\n      // The value that is passed to user code.\n      value: value,\n      // The value that is rendered in the router tree.\n      treeSegment: [key, Array.isArray(value) ? value.join('/') : value, type],\n      type: type,\n    }\n  }\n}\n\nfunction NonIndex({\n  pagePath,\n  statusCode,\n  isPossibleServerAction,\n}: {\n  pagePath: string\n  statusCode: number | undefined\n  isPossibleServerAction: boolean\n}) {\n  const is404Page = pagePath === '/404'\n  const isInvalidStatusCode = typeof statusCode === 'number' && statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  // TODO: is this correct if `isPossibleServerAction` is a false positive?\n  if (!isPossibleServerAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `Next-Router-State-Tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      parsedQuery: query,\n      pathname: url.pathname,\n      metadataContext: createMetadataContext(ctx.renderOpts),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex\n              pagePath={ctx.pagePath}\n              statusCode={ctx.res.statusCode}\n              isPossibleServerAction={ctx.isPossibleServerAction}\n            />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={getFlightViewportKey(requestId)} />\n            <MetadataTree key={getFlightMetadataKey(requestId)} />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    // TODO: is this correct if `isPossibleServerAction` is a false positive?\n    routeType: ctx.isPossibleServerAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during dynamicIO development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  if (\n    // We only want this behavior when running `next dev`\n    renderOpts.dev &&\n    // We only want this behavior when we have React's dev builds available\n    process.env.NODE_ENV === 'development' &&\n    // We only have a Prerender environment for projects opted into dynamicIO\n    renderOpts.experimental.dynamicIO\n  ) {\n    const [resolveValidation, validationOutlet] = createValidationOutlet()\n    RSCPayload._validation = validationOutlet\n\n    spawnDynamicValidationInDev(\n      resolveValidation,\n      ctx.componentMod.tree,\n      ctx,\n      false,\n      ctx.clientReferenceManifest,\n      requestStore\n    )\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n      filterStackFrame,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const {\n    clientReferenceManifest,\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    dev,\n    onInstrumentationRequestError,\n  } = renderOpts\n\n  if (!dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash: req.cookies[NEXT_HMR_REFRESH_HASH_COOKIE],\n    captureOwnerStack: ComponentMod.captureOwnerStack,\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    ComponentMod.renderToReadableStream,\n    rscPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: workStore.fetchMetrics,\n    renderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const hasGlobalNotFound = !!tree[2]['global-not-found']\n\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    // When it's using global-not-found, metadata errorType is undefined, which will retrieve the\n    // metadata from the page.\n    // When it's using not-found, metadata errorType is 'not-found', which will retrieve the\n    // metadata from the not-found.js boundary.\n    // TODO: remove this condition and keep it undefined when global-not-found is stabilized.\n    errorType: is404 && !hasGlobalNotFound ? 'not-found' : undefined,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\nfunction assertClientReferenceManifest(\n  clientReferenceManifest: RenderOpts['clientReferenceManifest']\n): asserts clientReferenceManifest is NonNullable<\n  RenderOpts['clientReferenceManifest']\n> {\n  if (!clientReferenceManifest) {\n    throw new InvariantError('Expected clientReferenceManifest to be defined.')\n  }\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  gracefullyDegrade,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  gracefullyDegrade: boolean\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedHTMLProvider>\n        <AppRouter\n          actionQueue={actionQueue}\n          globalErrorState={response.G}\n          assetPrefix={response.p}\n          gracefullyDegrade={gracefullyDegrade}\n        />\n      </ServerInsertedHTMLProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction ErrorApp<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  gracefullyDegrade,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  gracefullyDegrade: boolean\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  return (\n    <ServerInsertedHTMLProvider>\n      <AppRouter\n        actionQueue={actionQueue}\n        globalErrorState={response.G}\n        assetPrefix={response.p}\n        gracefullyDegrade={gracefullyDegrade}\n      />\n    </ServerInsertedHTMLProvider>\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  requestEndedState: { ended?: boolean },\n  postponedState: PostponedState | null,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    clientReferenceManifest,\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we track calls to `loadChunk` and `require`. This allows us\n    // to treat chunk/module loading with similar semantics as cache reads to avoid\n    // module loading from causing a prerender to abort too early.\n\n    const shouldTrackModuleLoading = () => {\n      if (!renderOpts.experimental.dynamicIO) {\n        return false\n      }\n      if (renderOpts.dev) {\n        return true\n      }\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      return !!(\n        workUnitStore &&\n        (workUnitStore.type === 'prerender' ||\n          workUnitStore.type === 'prerender-client' ||\n          workUnitStore.type === 'cache')\n      )\n    }\n\n    const __next_require__: typeof instrumented.require = (...args) => {\n      const exportsOrPromise = instrumented.require(...args)\n      if (shouldTrackModuleLoading()) {\n        // requiring an async module returns a promise.\n        trackPendingImport(exportsOrPromise)\n      }\n      return exportsOrPromise\n    }\n    // @ts-expect-error\n    globalThis.__next_require__ = __next_require__\n\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      if (shouldTrackModuleLoading()) {\n        trackPendingChunkLoad(loadingChunk)\n      }\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    req.originalRequest.on('end', () => {\n      requestEndedState.ended = true\n\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {\n    statusCode: isNotFoundPath ? 404 : undefined,\n  }\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to Client Components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  const { isStaticGeneration, fallbackRouteParams } = workStore\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (isStaticGeneration) {\n    requestId = Buffer.from(\n      await crypto.subtle.digest('SHA-1', Buffer.from(req.url))\n    ).toString('hex')\n  } else if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = (\n      require('next/dist/compiled/nanoid') as typeof import('next/dist/compiled/nanoid')\n    ).nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isPossibleActionRequest = getIsPossibleServerAction(req)\n\n  const implicitTags = await getImplicitTags(\n    workStore.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isPossibleServerAction: isPossibleActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n    implicitTags,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      loaderTree\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (workStore.invalidDynamicUsageError) {\n      throw workStore.invalidDynamicUsageError\n    }\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    if (response.collectedTags) {\n      metadata.fetchTags = response.collectedTags.join(',')\n    }\n\n    // Let the client router know how long to keep the cached entry around.\n    const staleHeader = String(response.collectedStale)\n    res.setHeader(NEXT_ROUTER_STALE_TIME_HEADER, staleHeader)\n    metadata.headers ??= {}\n    metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n    // If force static is specifically set to false, we should not revalidate\n    // the page.\n    if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } else {\n      // Copy the cache control value onto the render result metadata.\n      metadata.cacheControl = {\n        revalidate:\n          response.collectedRevalidate >= INFINITE_CACHE\n            ? false\n            : response.collectedRevalidate,\n        expire:\n          response.collectedExpire >= INFINITE_CACHE\n            ? undefined\n            : response.collectedExpire,\n      }\n    }\n\n    // provide bailout info for debugging\n    if (metadata.cacheControl?.revalidate === 0) {\n      metadata.staticBailoutInfo = {\n        description: workStore.dynamicUsageDescription,\n        stack: workStore.dynamicUsageStack,\n      }\n    }\n\n    if (response.renderResumeDataCache) {\n      metadata.renderResumeDataCache = response.renderResumeDataCache\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.renderResumeDataCache ?? postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      return generateDynamicFlightRenderResult(req, ctx, requestStore)\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isPossibleActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n        metadata,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          metadata.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            notFoundLoaderTree,\n            formState,\n            postponedState,\n            metadata\n          )\n\n          return new RenderResult(stream, { metadata })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      loaderTree,\n      formState,\n      postponedState,\n      metadata\n    )\n\n    if (workStore.invalidDynamicUsageError) {\n      throw workStore.invalidDynamicUsageError\n    }\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n    previewModeId: renderOpts.previewProps?.previewModeId,\n  })\n\n  const { isPrefetchRequest, previouslyRevalidatedTags } = parsedRequestHeaders\n\n  const requestEndedState = { ended: false }\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.renderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    fallbackRouteParams,\n    renderOpts,\n    requestEndedState,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n    previouslyRevalidatedTags,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    requestEndedState,\n    postponedState,\n    serverComponentsHmrCache,\n    sharedContext\n  )\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null,\n  metadata: AppPageRenderResultMetadata\n): Promise<ReadableStream<Uint8Array>> {\n  const { assetPrefix, nonce, pagePath, renderOpts } = ctx\n\n  const {\n    basePath,\n    botType,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    shouldWaitOnAllReady,\n    subresourceIntegrityManifest,\n    supportsDynamicResponse,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into dynamicIO\n      experimental.dynamicIO\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during dynamicIO development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame,\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        requestStore\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const postponed = getPostponedFromState(postponedState)\n\n        const resume = (\n          require('react-dom/server') as typeof import('react-dom/server')\n        ).resume\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n            gracefullyDegrade={!!botType}\n          />,\n          postponed,\n          { onError: htmlRendererErrorHandler, nonce }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = (\n      require('react-dom/server') as typeof import('react-dom/server')\n    ).renderToReadableStream\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        gracefullyDegrade={!!botType}\n        nonce={nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n      buildId: ctx.workStore.buildId,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout: dev,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n        buildId: ctx.workStore.buildId,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout: dev,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\n/**\n * This function is a fork of prerenderToStream dynamicIO branch.\n * While it doesn't return a stream we want it to have identical\n * prerender semantics to prerenderToStream and should update it\n * in conjunction with any changes to that function.\n */\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  requestStore: RequestStore\n): Promise<void> {\n  const {\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const { allowEmptyStaticShell = false, botType } = renderOpts\n\n  // These values are placeholder values for this validating render\n  // that are provided during the actual prerenderToStream.\n  const preinitScripts = () => {}\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  const hmrRefreshHash = requestStore.cookies.get(\n    NEXT_HMR_REFRESH_HASH_COOKIE\n  )?.value\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are ready\n  // to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const captureOwnerStackClient = React.captureOwnerStack\n  const captureOwnerStackServer = ComponentMod.captureOwnerStack\n\n  // The resume data cache here should use a fresh instance as it's\n  // performing a fresh prerender. If we get to implementing the\n  // prerendering of an already prerendered page, we should use the passed\n  // resume data cache instead.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (isReactLargeShellError(err)) {\n          // TODO: Aggregate\n          console.error(err)\n          return undefined\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerRenderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  if (workStore.invalidDynamicUsageError) {\n    resolveValidation(\n      <LogSafely\n        fn={() => {\n          console.error(workStore.invalidDynamicUsageError)\n        }}\n      />\n    )\n    return\n  }\n\n  let initialServerResult\n  try {\n    initialServerResult = await createReactServerPrerenderResult(\n      pendingInitialServerResult\n    )\n  } catch (err) {\n    if (\n      initialServerRenderController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n  }\n\n  if (initialServerResult) {\n    const initialClientRenderController = new AbortController()\n    const initialClientPrerenderController = new AbortController()\n    const initialClientPrerenderStore: PrerenderStore = {\n      type: 'prerender-client',\n      phase: 'render',\n      rootParams,\n      implicitTags,\n      renderSignal: initialClientRenderController.signal,\n      controller: initialClientPrerenderController,\n      // For HTML Generation the only cache tracked activity\n      // is module loading, which has it's own cache signal\n      cacheSignal: null,\n      dynamicTracking: null,\n      allowEmptyStaticShell,\n      revalidate: INFINITE_CACHE,\n      expire: INFINITE_CACHE,\n      stale: INFINITE_CACHE,\n      tags: [...implicitTags.tags],\n      prerenderResumeDataCache,\n      renderResumeDataCache: null,\n      hmrRefreshHash: undefined,\n      captureOwnerStack: captureOwnerStackClient,\n    }\n\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={initialServerResult.asUnclosingStream()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        gracefullyDegrade={!!botType}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientRenderController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (isReactLargeShellError(err)) {\n            // TODO: Aggregate\n            console.error(err)\n            return undefined\n          }\n\n          if (initialClientRenderController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        },\n        // We don't need bootstrap scripts in this prerender\n        // bootstrapScripts: [bootstrapScript],\n      }\n    )\n\n    pendingInitialClientResult.catch((err) => {\n      if (\n        initialServerRenderController.signal.aborted ||\n        isPrerenderInterruptedError(err)\n      ) {\n        // These are expected errors that might error the prerender. we ignore them.\n      } else if (\n        process.env.NEXT_DEBUG_BUILD ||\n        process.env.__NEXT_VERBOSE_LOGGING\n      ) {\n        // We don't normally log these errors because we are going to retry anyway but\n        // it can be useful for debugging Next.js itself to get visibility here when needed\n        printDebugThrownValueForProspectiveRender(err, workStore.route)\n      }\n    })\n\n    // This is mostly needed for dynamic `import()`s in client components.\n    // Promises passed to client were already awaited above (assuming that they came from cached functions)\n    trackPendingModules(cacheSignal)\n    await cacheSignal.cacheReady()\n    initialClientRenderController.abort()\n  }\n\n  const finalServerController = new AbortController()\n  const serverDynamicTracking = createDynamicTrackingState(\n    false // isDebugDynamicAccesses\n  )\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const reactServerResult = await createReactServerPrerenderResult(\n    prerenderAndAbortInSequentialTasks(\n      async () => {\n        const prerenderResult = await workUnitAsyncStorage.run(\n          // The store to scope\n          finalServerPrerenderStore,\n          // The function to run\n          ComponentMod.prerender,\n          // ... the arguments for the function to run\n          finalAttemptRSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: (err: unknown) => {\n              if (\n                finalServerController.signal.aborted &&\n                isPrerenderInterruptedError(err)\n              ) {\n                return err.digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n            signal: finalServerController.signal,\n          }\n        )\n        return prerenderResult\n      },\n      () => {\n        finalServerController.abort()\n      }\n    )\n  )\n\n  const clientDynamicTracking = createDynamicTrackingState(\n    false //isDebugDynamicAccesses\n  )\n  const finalClientController = new AbortController()\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender-client',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalClientController.signal,\n    controller: finalClientController,\n    // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackClient,\n  }\n\n  let dynamicValidation = createDynamicValidationState()\n\n  try {\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    let { prelude: unprocessedPrelude } =\n      await prerenderAndAbortInSequentialTasks(\n        () =>\n          workUnitAsyncStorage.run(\n            finalClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={reactServerResult.asUnclosingStream()}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />,\n            {\n              signal: finalClientController.signal,\n              onError: (err: unknown, errorInfo: ErrorInfo) => {\n                if (\n                  isPrerenderInterruptedError(err) ||\n                  finalClientController.signal.aborted\n                ) {\n                  const componentStack = errorInfo.componentStack\n                  if (typeof componentStack === 'string') {\n                    trackAllowedDynamicAccess(\n                      workStore,\n                      componentStack,\n                      dynamicValidation,\n                      clientDynamicTracking\n                    )\n                  }\n                  return\n                }\n\n                if (isReactLargeShellError(err)) {\n                  // TODO: Aggregate\n                  console.error(err)\n                  return undefined\n                }\n\n                return getDigestForWellKnownError(err)\n              },\n              // We don't need bootstrap scripts in this prerender\n              // bootstrapScripts: [bootstrapScript],\n            }\n          ),\n        () => {\n          finalClientController.abort()\n        }\n      )\n\n    const { preludeIsEmpty } = await processPrelude(unprocessedPrelude)\n    resolveValidation(\n      <LogSafely\n        fn={throwIfDisallowedDynamic.bind(\n          null,\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )}\n      />\n    )\n  } catch (thrownValue) {\n    // Even if the root errors we still want to report any dynamic IO errors\n    // that were discovered before the root errored.\n\n    let loggingFunction = throwIfDisallowedDynamic.bind(\n      null,\n      workStore,\n      PreludeState.Errored,\n      dynamicValidation,\n      serverDynamicTracking\n    )\n\n    if (process.env.NEXT_DEBUG_BUILD || process.env.__NEXT_VERBOSE_LOGGING) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      const originalLoggingFunction = loggingFunction\n      loggingFunction = () => {\n        console.error(\n          'During dynamic validation the root of the page errored. The next logged error is the thrown value. It may be a duplicate of errors reported during the normal development mode render.'\n        )\n        console.error(thrownValue)\n        originalLoggingFunction()\n      }\n    }\n\n    resolveValidation(<LogSafely fn={loggingFunction} />)\n  }\n}\n\nasync function LogSafely({ fn }: { fn: () => unknown }) {\n  try {\n    await fn()\n  } catch {}\n  return null\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  tree: LoaderTree\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const {\n    assetPrefix,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    pagePath,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    basePath,\n    botType,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    isDebugDynamicAccesses,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    subresourceIntegrityManifest,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n  const fallbackRouteParams = workStore.fallbackRouteParams\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult:\n    | null\n    | ReactServerPrerenderResult\n    | ServerPrerenderStreamResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  const selectStaleTime = (stale: number) =>\n    stale === INFINITE_CACHE &&\n    typeof experimental.staleTimes?.static === 'number'\n      ? experimental.staleTimes.static\n      : stale\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (experimental.dynamicIO) {\n      /**\n       * dynamicIO with PPR\n       *\n       * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n       * Once we have settled all cache reads we restart the render and abort after a single Task.\n       *\n       * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n       * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n       * and a synchronous abort might prevent us from filling all caches.\n       *\n       * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n       * and the reactServerIsDynamic value to determine how to treat the resulting render\n       */\n\n      // Prerender controller represents the lifetime of the prerender.\n      // It will be aborted when a Task is complete or a synchronously aborting\n      // API is called. Notably during cache-filling renders this does not actually\n      // terminate the render itself which will continue until all caches are filled\n      const initialServerPrerenderController = new AbortController()\n\n      // This controller represents the lifetime of the React render call. Notably\n      // during the cache-filling render it is different from the prerender controller\n      // because we don't want to end the react render until all caches are filled.\n      const initialServerRenderController = new AbortController()\n\n      // The cacheSignal helps us track whether caches are still filling or we are ready\n      // to cut the render off.\n      const cacheSignal = new CacheSignal()\n\n      let resumeDataCache: RenderResumeDataCache | PrerenderResumeDataCache\n      let renderResumeDataCache: RenderResumeDataCache | null = null\n      let prerenderResumeDataCache: PrerenderResumeDataCache | null = null\n\n      if (renderOpts.renderResumeDataCache) {\n        // If a prefilled immutable render resume data cache is provided, e.g.\n        // when prerendering an optional fallback shell after having prerendered\n        // pages with defined params, we use this instead of a prerender resume\n        // data cache.\n        resumeDataCache = renderResumeDataCache =\n          renderOpts.renderResumeDataCache\n      } else {\n        // Otherwise we create a new mutable prerender resume data cache.\n        resumeDataCache = prerenderResumeDataCache =\n          createPrerenderResumeDataCache()\n      }\n\n      const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        renderSignal: initialServerRenderController.signal,\n        controller: initialServerPrerenderController,\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      // We're not going to use the result of this render because the only time it could be used\n      // is if it completes in a microtask and that's likely very rare for any non-trivial app\n      const initialServerPayload = await workUnitAsyncStorage.run(\n        initialServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const pendingInitialServerResult = workUnitAsyncStorage.run(\n        initialServerPrerenderStore,\n        ComponentMod.prerender,\n        initialServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError: (err) => {\n            const digest = getDigestForWellKnownError(err)\n\n            if (digest) {\n              return digest\n            }\n\n            if (isReactLargeShellError(err)) {\n              // TODO: Aggregate\n              console.error(err)\n              return undefined\n            }\n\n            if (initialServerPrerenderController.signal.aborted) {\n              // The render aborted before this error was handled which indicates\n              // the error is caused by unfinished components within the render\n              return\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          },\n          // we don't care to track postpones during the prospective render because we need\n          // to always do a final render anyway\n          onPostpone: undefined,\n          // We don't want to stop rendering until the cacheSignal is complete so we pass\n          // a different signal to this render call than is used by dynamic APIs to signify\n          // transitioning out of the prerender environment\n          signal: initialServerRenderController.signal,\n        }\n      )\n\n      // Wait for all caches to be finished filling and for async imports to resolve\n      trackPendingModules(cacheSignal)\n      await cacheSignal.cacheReady()\n\n      initialServerRenderController.abort()\n      initialServerPrerenderController.abort()\n\n      // We don't need to continue the prerender process if we already\n      // detected invalid dynamic usage in the initial prerender phase.\n      if (workStore.invalidDynamicUsageError) {\n        throw workStore.invalidDynamicUsageError\n      }\n\n      let initialServerResult\n      try {\n        initialServerResult = await createReactServerPrerenderResult(\n          pendingInitialServerResult\n        )\n      } catch (err) {\n        if (\n          initialServerRenderController.signal.aborted ||\n          initialServerPrerenderController.signal.aborted\n        ) {\n          // These are expected errors that might error the prerender. we ignore them.\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          // We don't normally log these errors because we are going to retry anyway but\n          // it can be useful for debugging Next.js itself to get visibility here when needed\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      }\n\n      if (initialServerResult) {\n        const initialClientRenderController = new AbortController()\n        const initialClientPrerenderController = new AbortController()\n        const initialClientPrerenderStore: PrerenderStore = {\n          type: 'prerender-client',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialClientRenderController.signal,\n          controller: initialClientPrerenderController,\n          // For HTML Generation the only cache tracked activity\n          // is module loading, which has it's own cache signal\n          cacheSignal: null,\n          dynamicTracking: null,\n          allowEmptyStaticShell,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          renderResumeDataCache,\n          hmrRefreshHash: undefined,\n          captureOwnerStack: undefined, // Not available in production.\n        }\n\n        const prerender = (\n          require('react-dom/static') as typeof import('react-dom/static')\n        ).prerender\n        const pendingInitialClientResult = workUnitAsyncStorage.run(\n          initialClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={initialServerResult.asUnclosingStream()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            gracefullyDegrade={!!botType}\n            nonce={nonce}\n          />,\n          {\n            signal: initialClientRenderController.signal,\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              if (initialClientRenderController.signal.aborted) {\n                // These are expected errors that might error the prerender. we ignore them.\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                // We don't normally log these errors because we are going to retry anyway but\n                // it can be useful for debugging Next.js itself to get visibility here when needed\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n\n        pendingInitialClientResult.catch((err) => {\n          if (\n            initialServerRenderController.signal.aborted ||\n            isPrerenderInterruptedError(err)\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        })\n\n        // This is mostly needed for dynamic `import()`s in client components.\n        // Promises passed to client were already awaited above (assuming that they came from cached functions)\n        trackPendingModules(cacheSignal)\n        await cacheSignal.cacheReady()\n        initialClientRenderController.abort()\n      }\n\n      let serverIsDynamic = false\n      const finalServerController = new AbortController()\n      const serverDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n\n      const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        renderSignal: finalServerController.signal,\n        controller: finalServerController,\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: serverDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      let prerenderIsPending = true\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResult(\n          prerenderAndAbortInSequentialTasks(\n            async () => {\n              const prerenderResult = await workUnitAsyncStorage.run(\n                // The store to scope\n                finalServerPrerenderStore,\n                // The function to run\n                ComponentMod.prerender,\n                // ... the arguments for the function to run\n                finalAttemptRSCPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  filterStackFrame,\n                  onError: (err: unknown) => {\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerController.signal,\n                }\n              )\n              prerenderIsPending = false\n              return prerenderResult\n            },\n            () => {\n              if (finalServerController.signal.aborted) {\n                // If the server controller is already aborted we must have called something\n                // that required aborting the prerender synchronously such as with new Date()\n                serverIsDynamic = true\n                return\n              }\n\n              if (prerenderIsPending) {\n                // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                // there is something unfinished.\n                serverIsDynamic = true\n              }\n              finalServerController.abort()\n            }\n          )\n        ))\n\n      const clientDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n      const finalClientController = new AbortController()\n      const finalClientPrerenderStore: PrerenderStore = {\n        type: 'prerender-client',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        renderSignal: finalClientController.signal,\n        controller: finalClientController,\n        // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n        cacheSignal: null,\n        dynamicTracking: clientDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      let clientIsDynamic = false\n      let dynamicValidation = createDynamicValidationState()\n\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      let { prelude: unprocessedPrelude, postponed } =\n        await prerenderAndAbortInSequentialTasks(\n          () =>\n            workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                gracefullyDegrade={!!botType}\n                nonce={nonce}\n              />,\n              {\n                signal: finalClientController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientController.signal.aborted\n                  ) {\n                    clientIsDynamic = true\n\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore,\n                        componentStack,\n                        dynamicValidation,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            ),\n          () => {\n            finalClientController.abort()\n          }\n        )\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      // If we've disabled throwing on empty static shell, then we don't need to\n      // track any dynamic access that occurs above the suspense boundary because\n      // we'll do so in the route shell.\n      if (!allowEmptyStaticShell) {\n        throwIfDisallowedDynamic(\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n      metadata.flightData = flightData\n      metadata.segmentData = await collectSegmentData(\n        flightData,\n        finalServerPrerenderStore,\n        ComponentMod,\n        renderOpts,\n        fallbackRouteParams\n      )\n\n      if (serverIsDynamic || clientIsDynamic) {\n        if (postponed != null) {\n          // Dynamic HTML case\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            resumeDataCache\n          )\n        } else {\n          // Dynamic Data case\n          metadata.postponed =\n            await getDynamicDataPostponedState(resumeDataCache)\n        }\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      } else {\n        // Static case\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      }\n    } else if (experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(isDebugDynamicAccesses)\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      }\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      const { prelude, postponed } = await workUnitAsyncStorage.run(\n        ssrPrerenderStore,\n        prerender,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          gracefullyDegrade={!!botType}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          onHeaders: (headers: Headers) => {\n            headers.forEach((value, key) => {\n              appendHeader(key, value)\n            })\n          },\n          maxHeadersLength: reactMaxHeadersLength,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = (\n        require('react-dom/server') as typeof import('react-dom/server')\n      ).renderToReadableStream\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          gracefullyDegrade={!!botType}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: selectStaleTime(prerenderLegacyStore.stale),\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags.tags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      // TODO we should use the same prerender semantics that we initially rendered\n      // with in this case too. The only reason why this is ok atm is because it's essentially\n      // an empty page and no user code runs.\n      const fizzStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream =\n        reactServerPrerenderResult instanceof ServerPrerenderStreamResult\n          ? reactServerPrerenderResult.asStream()\n          : reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout: dev,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale: selectStaleTime(\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE\n        ),\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<{\n  GlobalError: GlobalErrorComponent\n  styles: React.ReactNode | undefined\n}> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  const GlobalErrorComponent: GlobalErrorComponent =\n    ctx.componentMod.GlobalError\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n  if (ctx.renderOpts.dev) {\n    const dir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? process.env.__NEXT_EDGE_PROJECT_DIR!\n        : ctx.renderOpts.dir || ''\n\n    const globalErrorModulePath = normalizeConventionFilePath(\n      dir,\n      globalErrorModule?.[1]\n    )\n    if (ctx.renderOpts.devtoolSegmentExplorer && globalErrorModulePath) {\n      const SegmentViewNode = ctx.componentMod.SegmentViewNode\n      globalErrorStyles = (\n        // This will be rendered next to GlobalError component under ErrorBoundary,\n        // it requires a key to avoid React warning about duplicate keys.\n        <SegmentViewNode\n          key=\"ge-svn\"\n          type=\"global-error\"\n          pagePath={globalErrorModulePath}\n        >\n          {globalErrorStyles}\n        </SegmentViewNode>\n      )\n    }\n  }\n\n  return {\n    GlobalError: GlobalErrorComponent,\n    styles: globalErrorStyles,\n  }\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    // Do not generate per-segment data unless the experimental Segment Cache\n    // flag is enabled.\n    //\n    // We also skip generating segment data if flag is set to \"client-only\",\n    // rather than true. (The \"client-only\" option only affects the behavior of\n    // the client-side implementation; per-segment prefetches are intentionally\n    // disabled in that configuration).\n    renderOpts.experimental.clientSegmentCache !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: getServerModuleMap(),\n  }\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest,\n    fallbackRouteParams\n  )\n}\n"], "names": ["renderToHTMLOrFlight", "flightDataPathHeadKey", "getFlightViewportKey", "requestId", "getFlightMetadataKey", "filterStackFrame", "process", "env", "NODE_ENV", "require", "filterStackFrameDEV", "undefined", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "toLowerCase", "isHmrRefresh", "NEXT_HMR_REFRESH_HEADER", "isRSCRequest", "RSC_HEADER", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "parseAndValidateFlightRouterState", "NEXT_ROUTER_STATE_TREE_HEADER", "isRouteTreePrefetchRequest", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "csp", "nonce", "getScriptNonceFromHeader", "previouslyRevalidatedTags", "getPreviouslyRevalidatedTags", "previewModeId", "createNotFoundLoaderTree", "loaderTree", "components", "hasGlobalNotFound", "children", "PAGE_SEGMENT_KEY", "page", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "getSegmentParam", "key", "param", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "type", "isOptionalCatchall", "dynamicParamType", "dynamicParamTypes", "treeSegment", "split", "slice", "flatMap", "pathSegment", "parseParameter", "join", "getShortDynamicParamType", "NonIndex", "statusCode", "isPossibleServerAction", "is404Page", "isInvalidStatusCode", "meta", "name", "content", "generateDynamicRSCPayload", "ctx", "flightData", "componentMod", "tree", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "workStore", "url", "serveStreamingMetadata", "renderOpts", "skipFlight", "preloadCallbacks", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "parsed<PERSON><PERSON><PERSON>", "pathname", "metadataContext", "createMetadataContext", "walkTreeWithFlightRouterState", "loaderTreeToFilter", "parentParams", "rscHead", "React", "Fragment", "res", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "path", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "createFlightReactServerErrorHandler", "dev", "RSCPayload", "workUnitAsyncStorage", "run", "experimental", "dynamicIO", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "spawnDynamicValidationInDev", "clientReferenceManifest", "flightReadableStream", "renderToReadableStream", "clientModules", "temporaryReferences", "FlightRenderResult", "fetchMetrics", "warmupDevRender", "ComponentMod", "implicitTags", "allowEmptyStaticShell", "InvariantError", "rootParams", "getRootParams", "prerenderResumeDataCache", "createPrerenderResumeDataCache", "renderController", "AbortController", "prerenderController", "cacheSignal", "CacheSignal", "prerenderStore", "phase", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "INFINITE_CACHE", "expire", "stale", "tags", "renderResumeDataCache", "hmrRefreshHash", "cookies", "NEXT_HMR_REFRESH_HASH_COOKIE", "captureOwnerStack", "rscPayload", "trackPendingModules", "cacheReady", "abort", "createRenderResumeDataCache", "prepareInitialCanonicalUrl", "search", "getRSCPayload", "is404", "missingSlots", "initialTree", "createFlightRouterStateFromLoaderTree", "errorType", "seedData", "createComponentTree", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "NEXT_URL", "initialHead", "GlobalError", "styles", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "isError", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "digest", "data-next-error-stack", "stack", "assertClientReferenceManifest", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "gracefully<PERSON><PERSON><PERSON>", "response", "use", "useFlightStream", "initialState", "createInitialRouterState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "createMutableActionQueue", "HeadManagerContext", "Provider", "appDir", "AppRouter", "globalErrorState", "ErrorApp", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "requestEndedState", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "wrapClientComponentLoader", "shouldTrackModuleLoading", "workUnitStore", "getStore", "__next_require__", "args", "exportsOrPromise", "trackPendingImport", "globalThis", "__next_chunk_load__", "loadingChunk", "loadChunk", "trackPendingChunkLoad", "URL", "setIsrStatus", "NEXT_RUNTIME", "isNodeNextRequest", "originalRequest", "on", "ended", "metrics", "getClientComponentLoaderMetrics", "reset", "getTracer", "startSpan", "NextNodeServerSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "metadata", "appUsingSizeAdjust", "serverModuleMap", "createServerModuleMap", "setReferenceManifestsSingleton", "patchFetch", "taintObjectReference", "stripInternalQueries", "<PERSON><PERSON><PERSON>", "from", "crypto", "subtle", "toString", "randomUUID", "nanoid", "isPossibleActionRequest", "getIsPossibleServerAction", "getImplicitTags", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "AppRenderSpan", "getBodyResult", "spanName", "prerenderToStream", "dynamicAccess", "accessedDynamicData", "isDebugDynamicAccesses", "warn", "access", "formatDynamicAPIAccesses", "invalidDynamicUsageError", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "isUserLandError", "pendingRevalidates", "pendingRevalidateWrites", "pendingRevalidatedTags", "pendingPromise", "executeRevalidates", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "collectedTags", "fetchTags", "staleHeader", "String", "collectedStale", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STALE_TIME_HEADER", "forceStatic", "collectedRevalidate", "cacheControl", "collectedExpire", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "RenderResult", "streamToString", "stream", "createRequestStoreForRender", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "handleAction", "generateFlight", "notFoundLoaderTree", "result", "assignMetadata", "parseRelativeUrl", "parsePostponedState", "createWorkStore", "routeModule", "definition", "workAsyncStorage", "basePath", "botType", "buildManifest", "crossOrigin", "nextExport", "reactMaxHeadersLength", "shouldWaitOnAllReady", "subresourceIntegrityManifest", "supportsDynamicResponse", "renderServerInsertedHTML", "createServerInsertedHTML", "getServerInsertedMetadata", "createServerInsertedMetadata", "tracingMetadata", "getTracedMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "getAssetQueryString", "integrity", "noModule", "bootstrapScript", "getRequiredScripts", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "createHTMLReactServerErrorHandler", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "createHTMLErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "scheduleInSequentialTasks", "prerenderPhase", "environmentName", "ReactServerResult", "waitAtLeastOneReactRenderTask", "DynamicState", "DATA", "inlinedReactServerDataStream", "createInlinedDataReadableStream", "tee", "chainStreams", "createDocumentClosingStream", "getPostponedFromState", "resume", "htmlStream", "getServerInsertedHTML", "makeGetServerInsertedHTML", "serverCapturedErrors", "continueDynamicHTMLResume", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bootstrapScripts", "generateStaticHTML", "continueFizzStream", "isBuildTimePrerendering", "validateRootLayout", "isStaticGenBailoutError", "shouldBailoutToCSR", "isBailoutToCSRError", "getStackWithoutErrorMessage", "error", "reason", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isRedirectError", "getRedirectStatusCodeFromError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "Headers", "appendMutableCookies", "mutableCookies", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "errorServerStream", "fizzStream", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "Promise", "resolve", "isNotFound", "initialServerPrerenderController", "initialServerRenderController", "captureOwnerStackClient", "captureOwnerStackServer", "initialServerPrerenderStore", "initialServerPayload", "pendingInitialServerResult", "prerender", "getDigestForWellKnownError", "isReactLargeShellError", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "route", "onPostpone", "LogSafely", "fn", "initialServerResult", "createReactServerPrerenderResult", "initialClientRenderController", "initialClientPrerenderController", "initialClientPrerenderStore", "pendingInitialClientResult", "asUnclosingStream", "catch", "isPrerenderInterruptedError", "finalServerController", "serverDynamicTracking", "createDynamicTrackingState", "finalServerPrerenderStore", "finalAttemptRSCPayload", "prerenderAndAbortInSequentialTasks", "prerenderResult", "clientDynamicTracking", "finalClientController", "finalClientPrerenderStore", "dynamicValidation", "createDynamicValidationState", "prelude", "unprocessedPrelude", "errorInfo", "componentStack", "trackAllowedDynamicAccess", "preludeIsEmpty", "processPrelude", "throwIfDisallowedDynamic", "PreludeState", "Empty", "Full", "thrownValue", "loggingFunction", "Errored", "originalLoggingFunction", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "item", "selectStaleTime", "staleTimes", "static", "resumeDataCache", "serverIsDynamic", "prerenderIsPending", "clientIsDynamic", "streamToBuffer", "asStream", "segmentData", "collectSegmentData", "getDynamicHTMLPostponedState", "getDynamicDataPostponedState", "continueDynamicPrerender", "consumeDynamicAccess", "StaticGenBailoutError", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "createPostponedAbortSignal", "continueStaticP<PERSON><PERSON>", "consumeAsStream", "reactServerPrerenderStore", "createReactServerPrerenderResultFromRender", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "isDynamicServerError", "flightStream", "ServerPrerenderStreamResult", "modules", "globalErrorModule", "parseLoaderTree", "GlobalErrorComponent", "createComponentStylesAndScripts", "filePath", "getComponent", "dir", "__NEXT_EDGE_PROJECT_DIR", "globalErrorModulePath", "normalizeConventionFilePath", "devtoolSegmentExplorer", "SegmentViewNode", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "getServerModuleMap", "staleTime"], "mappings": ";;;;+BAgpDaA;;;eAAAA;;;;0CAhoDN;8DAayC;qEAKzC;sCAWA;+BAC8B;kCAU9B;iCAC+B;8BACM;2BACZ;oCAKzB;0BAIA;+BACyB;8BACmB;2BACD;wBACxB;oCACS;oCAQ5B;0CAIA;iCACyB;0CACS;mDACS;uDACI;+BACzB;8BACO;qBACR;gCACS;oCACI;iCACN;+BACL;2CACY;+CACI;qCACK;qCACf;iCAI7B;gCAKA;oCAM8B;mCAI9B;yCAIA;mCACqC;kCAarC;+CAIA;6BAC+B;yBACJ;4BACH;kCACE;kEACX;yCAGoB;0CACD;mCACA;uBACL;yBACH;yCAK1B;wCAQmD;sCAChB;2BACI;8CAIvC;6BACqB;wBACM;gCACH;4BAEA;iDACiB;iCAChB;iCAMzB;gEAEa;8CACyB;6BACA;mCACV;4CAK5B;sCACgC;qCAEK;;;;;;AAqD5C,MAAMC,wBAAwB;AAC9B,MAAMC,uBAAuB,CAACC,YAAsBA,YAAY;AAChE,MAAMC,uBAAuB,CAACD,YAAsBA,YAAY;AAEhE,MAAME,mBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AAmBN,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,MAAMC,oBACJF,sBACAF,OAAO,CAACK,6CAA2B,CAACC,WAAW,GAAG,KAAKR;IAEzD,MAAMS,eACJP,OAAO,CAACQ,yCAAuB,CAACF,WAAW,GAAG,KAAKR;IAErD,2DAA2D;IAC3D,MAAMW,eACJP,sBAAsBF,OAAO,CAACU,4BAAU,CAACJ,WAAW,GAAG,KAAKR;IAE9D,MAAMa,iCACJF,gBAAiB,CAAA,CAACL,qBAAqB,CAACH,QAAQW,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBG,IAAAA,oEAAiC,EAC/Bd,OAAO,CAACe,+CAA6B,CAACT,WAAW,GAAG,IAEtDR;IAEJ,sEAAsE;IACtE,MAAMkB,6BACJhB,OAAO,CAACiB,qDAAmC,CAACX,WAAW,GAAG,KAAK;IAEjE,MAAMY,MACJlB,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMmB,QACJ,OAAOD,QAAQ,WAAWE,IAAAA,kDAAwB,EAACF,OAAOpB;IAE5D,MAAMuB,4BAA4BC,IAAAA,yCAA4B,EAC5DtB,SACAC,QAAQsB,aAAa;IAGvB,OAAO;QACLV;QACAT;QACAY;QACAT;QACAE;QACAP;QACAiB;QACAE;IACF;AACF;AAEA,SAASG,yBAAyBC,UAAsB;IACtD,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,MAAME,oBAAoB,CAAC,CAACD,UAAU,CAAC,mBAAmB;IAC1D,OAAO;QACL;QACA;YACEE,UAAU;gBACRC,yBAAgB;gBAChB,CAAC;gBACD;oBACEC,MAAMJ,UAAU,CAAC,mBAAmB,IAAIA,UAAU,CAAC,YAAY;gBACjE;aACD;QACH;QACA,gEAAgE;QAChEC,oBAAoBD,aAAa,CAAC;KACnC;AACH;AAEA;;CAEC,GACD,SAASK,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAeC,IAAAA,gCAAe,EAACF;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QAEA,MAAME,MAAMF,aAAaG,KAAK;QAE9B,IAAIC,QAAQT,MAAM,CAACO,IAAI;QAEvB,IAAIL,uBAAuBA,oBAAoBQ,GAAG,CAACL,aAAaG,KAAK,GAAG;YACtEC,QAAQP,oBAAoBS,GAAG,CAACN,aAAaG,KAAK;QACpD,OAAO,IAAII,MAAMC,OAAO,CAACJ,QAAQ;YAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;QAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;YACpCA,QAAQO,mBAAmBP;QAC7B;QAEA,IAAI,CAACA,OAAO;YACV,MAAMQ,aAAaZ,aAAaa,IAAI,KAAK;YACzC,MAAMC,qBAAqBd,aAAaa,IAAI,KAAK;YAEjD,IAAID,cAAcE,oBAAoB;gBACpC,MAAMC,mBAAmBC,2CAAiB,CAAChB,aAAaa,IAAI,CAAC;gBAC7D,oEAAoE;gBACpE,6DAA6D;gBAC7D,IAAIC,oBAAoB;oBACtB,OAAO;wBACLX,OAAOD;wBACPE,OAAO;wBACPS,MAAME;wBACNE,aAAa;4BAACf;4BAAK;4BAAIa;yBAAiB;oBAC1C;gBACF;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxFX,QAAQR,SACLsB,KAAK,CAAC,IACP,gCAAgC;iBAC/BC,KAAK,CAAC,EACP,oDAAoD;iBACnDC,OAAO,CAAC,CAACC;oBACR,MAAMlB,QAAQmB,IAAAA,0BAAc,EAACD;oBAC7B,yDAAyD;oBACzD,wDAAwD;oBACxD,OAAO1B,MAAM,CAACQ,MAAMD,GAAG,CAAC,IAAIC,MAAMD,GAAG;gBACvC;gBAEF,OAAO;oBACLC,OAAOD;oBACPE;oBACAS,MAAME;oBACN,wCAAwC;oBACxCE,aAAa;wBAACf;wBAAKE,MAAMmB,IAAI,CAAC;wBAAMR;qBAAiB;gBACvD;YACF;QACF;QAEA,MAAMF,OAAOW,IAAAA,kDAAwB,EAACxB,aAAaa,IAAI;QAEvD,OAAO;YACLV,OAAOD;YACP,yCAAyC;YACzCE,OAAOA;YACP,iDAAiD;YACjDa,aAAa;gBAACf;gBAAKK,MAAMC,OAAO,CAACJ,SAASA,MAAMmB,IAAI,CAAC,OAAOnB;gBAAOS;aAAK;YACxEA,MAAMA;QACR;IACF;AACF;AAEA,SAASY,SAAS,EAChB7B,QAAQ,EACR8B,UAAU,EACVC,sBAAsB,EAKvB;IACC,MAAMC,YAAYhC,aAAa;IAC/B,MAAMiC,sBAAsB,OAAOH,eAAe,YAAYA,aAAa;IAE3E,gEAAgE;IAChE,yEAAyE;IACzE,IAAI,CAACC,0BAA2BC,CAAAA,aAAaC,mBAAkB,GAAI;QACjE,qBAAO,qBAACC;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbC,GAAqB,EACrBtE,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAIuE,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAMjD,UAAU,EAChBkD,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACD1C,0BAA0B,EAC1B2C,sBAAsB,EACtBC,KAAK,EACLzF,SAAS,EACTuB,iBAAiB,EACjBmE,SAAS,EACTC,GAAG,EACJ,GAAGV;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,IAAI,EAACjF,2BAAAA,QAASmF,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAM,EACJC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;YAC3BD,MAAMjD;YACNkE,aAAaZ;YACba,UAAUX,IAAIW,QAAQ;YACtBC,iBAAiBC,IAAAA,sCAAqB,EAACvB,IAAIY,UAAU;YACrDhD;YACA2C;YACAE;YACAJ;YACAC;YACAK;QACF;QAEAV,aAAa,AACX,CAAA,MAAMuB,IAAAA,4DAA6B,EAAC;YAClCxB;YACAyB,oBAAoBvE;YACpBwE,cAAc,CAAC;YACfpF;YACA,+CAA+C;YAC/CqF,uBACE,sBAACC,cAAK,CAACC,QAAQ;;kCAEb,qBAACtC;wBACC7B,UAAUsC,IAAItC,QAAQ;wBACtB8B,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;wBAC9BC,wBAAwBO,IAAIP,sBAAsB;;kCAGpD,qBAACsB,kBAAkBjG,qBAAqBC;kCACxC,qBAACiG,kBAAkBhG,qBAAqBD;;eATrBF;YAYvBkH,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBlB;YACAC;YACAJ;YACAK;QACF,EAAC,EACD5C,GAAG,CAAC,CAAC6D,OAASA,KAAKnD,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAIvD,2BAAAA,QAAS2G,YAAY,EAAE;QACzB,OAAO;YACLC,GAAG5G,QAAQ2G,YAAY;YACvBE,GAAGtC;YACHuC,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC5BH,GAAGtC;QACH0C,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACP7C,GAAqB,EACrB8C,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAWhD,IAAItC,QAAQ;QACvB,yEAAyE;QACzEuF,WAAWjD,IAAIP,sBAAsB,GAAG,WAAW;QACnDqD;QACAI,kBAAkBC,IAAAA,0BAAmB,EAACnD,IAAIS,SAAS;IACrD;AACF;AACA;;;CAGC,GACD,eAAe2C,kCACbC,GAAoB,EACpBrD,GAAqB,EACrBsD,YAA0B,EAC1B5H,OAMC;IAED,MAAMkF,aAAaZ,IAAIY,UAAU;IAEjC,SAAS2C,wBAAwBC,GAAkB;QACjD,OAAO5C,WAAW6C,6BAA6B,oBAAxC7C,WAAW6C,6BAA6B,MAAxC7C,YACL4C,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAM0D,UAAUC,IAAAA,uDAAmC,EACjD,CAAC,CAAC/C,WAAWgD,GAAG,EAChBL;IAGF,MAAMM,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACAvD,2BACAC,KACAtE;IAGF,IACE,qDAAqD;IACrDkF,WAAWgD,GAAG,IACd,uEAAuE;IACvE1I,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,yEAAyE;IACzEwF,WAAWoD,YAAY,CAACC,SAAS,EACjC;QACA,MAAM,CAACC,mBAAmBC,iBAAiB,GAAGC;QAC9CP,WAAWQ,WAAW,GAAGF;QAEzBG,4BACEJ,mBACAlE,IAAIE,YAAY,CAACC,IAAI,EACrBH,KACA,OACAA,IAAIuE,uBAAuB,EAC3BjB;IAEJ;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMkB,uBAAuBV,kDAAoB,CAACC,GAAG,CACnDT,cACAtD,IAAIE,YAAY,CAACuE,sBAAsB,EACvCZ,YACA7D,IAAIuE,uBAAuB,CAACG,aAAa,EACzC;QACEhB;QACAiB,mBAAmB,EAAEjJ,2BAAAA,QAASiJ,mBAAmB;QACjD1J;IACF;IAGF,OAAO,IAAI2J,sCAAkB,CAACJ,sBAAsB;QAClDK,cAAc7E,IAAIS,SAAS,CAACoE,YAAY;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,eAAeC,gBACbzB,GAAoB,EACpBrD,GAAqB;IAErB,MAAM,EACJuE,uBAAuB,EACvBrE,cAAc6E,YAAY,EAC1BnH,0BAA0B,EAC1BoH,YAAY,EACZpE,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJiF,wBAAwB,KAAK,EAC7BrB,GAAG,EACHH,6BAA6B,EAC9B,GAAG7C;IAEJ,IAAI,CAACgD,KAAK;QACR,MAAM,qBAEL,CAFK,IAAIsB,8BAAc,CACtB,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,aAAaC,IAAAA,kCAAa,EAC9BL,aAAa5E,IAAI,EACjBvC;IAGF,SAAS2F,wBAAwBC,GAAkB;QACjD,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAM0D,UAAUC,IAAAA,uDAAmC,EACjD,MACAJ;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAM8B,2BAA2BC,IAAAA,+CAA8B;IAE/D,MAAMC,mBAAmB,IAAIC;IAC7B,MAAMC,sBAAsB,IAAID;IAChC,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMC,iBAAiC;QACrCjH,MAAM;QACNkH,OAAO;QACPV;QACAH;QACAc,cAAcP,iBAAiBQ,MAAM;QACrCC,YAAYP;QACZC;QACAO,iBAAiB;QACjBhB;QACAiB,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRjB;QACAkB,uBAAuB;QACvBC,gBAAgBnD,IAAIoD,OAAO,CAACC,8CAA4B,CAAC;QACzDC,mBAAmB5B,aAAa4B,iBAAiB;IACnD;IAEA,MAAMC,aAAa,MAAM9C,kDAAoB,CAACC,GAAG,CAC/C6B,gBACA7F,2BACAC;IAGF,0FAA0F;IAC1F,mCAAmC;IACnC8D,kDAAoB,CAACC,GAAG,CACtB6B,gBACAb,aAAaN,sBAAsB,EACnCmC,YACArC,wBAAwBG,aAAa,EACrC;QACEzJ;QACAyI;QACAqC,QAAQR,iBAAiBQ,MAAM;IACjC;IAGF,8EAA8E;IAC9Ec,IAAAA,+CAAmB,EAACnB;IACpB,MAAMA,YAAYoB,UAAU;IAE5B,uFAAuF;IACvFlB,eAAeP,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBE,iBAAiBwB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAInC,sCAAkB,CAAC,IAAI;QAChCC,cAAcpE,UAAUoE,YAAY;QACpC0B,uBAAuBS,IAAAA,4CAA2B,EAChD3B;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAAS4B,2BAA2BvG,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIW,QAAQ,GAAGX,IAAIwG,MAAM,AAAD,EAAGlI,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAemI,cACbhH,IAAgB,EAChBH,GAAqB,EACrBoH,KAAc;IAEd,MAAMrF,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIqF;IAEJ,sDAAsD;IACtD,IAAInM,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CiM,eAAe,IAAIrF;IACrB;IAEA,MAAM,EACJpE,0BAA0B,EAC1B4C,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMsH,cAAcC,IAAAA,4EAAqC,EACvDpH,MACAvC,4BACA4C;IAEF,MAAMG,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAMvD,oBAAoB,CAAC,CAAC+C,IAAI,CAAC,EAAE,CAAC,mBAAmB;IAEvD,MAAM,EACJY,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;QAC3BD;QACA,6FAA6F;QAC7F,0BAA0B;QAC1B,wFAAwF;QACxF,2CAA2C;QAC3C,yFAAyF;QACzFqH,WAAWJ,SAAS,CAAChK,oBAAoB,cAAc7B;QACvD6F,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBC,IAAAA,sCAAqB,EAACvB,IAAIY,UAAU;QACrDhD;QACA2C;QACAE;QACAJ;QACAC;QACAK;IACF;IAEA,MAAMG,mBAAqC,EAAE;IAE7C,MAAM2G,WAAW,MAAMC,IAAAA,wCAAmB,EAAC;QACzC1H;QACA9C,YAAYiD;QACZuB,cAAc,CAAC;QACfK;QACAE;QACAC;QACAC,oBAAoB;QACpBlB;QACAC;QACAmG;QACAvG;QACA6G,gBAAgB3H,IAAIY,UAAU,CAACoD,YAAY,CAAC2D,cAAc;QAC1DxG;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAMyG,aAAa5H,IAAI8B,GAAG,CAAC+F,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACC,0BAAQ;IAEhE,MAAMC,4BACJ,sBAACrG,cAAK,CAACC,QAAQ;;0BACb,qBAACtC;gBACC7B,UAAUsC,IAAItC,QAAQ;gBACtB8B,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,qBAACsB;0BACD,qBAACC;;OAPkBnG;IAWvB,MAAM,EAAEqN,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDlI,MACAH;IAGF,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAMsI,wBACJ7H,UAAUmC,kBAAkB,IAC5B5C,IAAIY,UAAU,CAACoD,YAAY,CAAC3H,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FkM,iBAAG,qBAACC;YAAS1H,kBAAkBA;;QAC/B0B,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC5B+F,GAAGzI,IAAI0I,WAAW;QAClBC,GAAG1B,2BAA2BvG;QAC9BlC,GAAG,CAAC,CAACsJ;QACLvF,GAAG;YACD;gBACE+E;gBACAG;gBACAQ;gBACAK;aACD;SACF;QACDM,GAAGvB;QACHwB,GAAG;YAACX;YAAaE;SAAkB;QACnCU,GAAG,OAAO9I,IAAIY,UAAU,CAACmI,SAAS,KAAK;QACvCpG,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAAS4F,SAAS,EAAE1H,gBAAgB,EAAoC;IACtEA,iBAAiBkI,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb/I,IAAgB,EAChBH,GAAqB,EACrBmJ,QAAiB,EACjB3B,SAAqD;IAErD,MAAM,EACJ5J,0BAA0B,EAC1B4C,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAM,EAAEK,YAAY,EAAED,YAAY,EAAE,GAAGX,yBAAyB;QAC9DD;QACAiB,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBC,IAAAA,sCAAqB,EAACvB,IAAIY,UAAU;QACrD4G;QACA5J;QACA2C;QACAE;QACAJ;QACAC;QACAK,wBAAwBA;IAC1B;IAEA,MAAMsH,4BACJ,sBAACrG,cAAK,CAACC,QAAQ;;0BACb,qBAACtC;gBACC7B,UAAUsC,IAAItC,QAAQ;gBACtB8B,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,qBAACsB;YACA7F,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACwE;gBAAKC,MAAK;gBAAaC,SAAQ;;0BAElC,qBAACkB;;OAVkBnG;IAcvB,MAAMyM,cAAcC,IAAAA,4EAAqC,EACvDpH,MACAvC,4BACA4C;IAGF,IAAIgD,MAAyBjI;IAC7B,IAAI4N,UAAU;QACZ3F,MAAM4F,IAAAA,gBAAO,EAACD,YAAYA,WAAW,qBAAwB,CAAxB,IAAIE,MAAMF,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAM1B,WAA8B;QAClCH,WAAW,CAAC,EAAE;sBACd,sBAACgC;YAAKC,IAAG;;8BACP,qBAACC;8BACD,qBAACC;8BACEvO,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBoI,oBACxC,qBAACkG;wBACCC,2BAAyBnG,IAAIoG,OAAO;wBACpCC,0BAAwB,YAAYrG,MAAMA,IAAIsG,MAAM,GAAG;wBACvDC,yBAAuBvG,IAAIwG,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM,EAAE9B,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDlI,MACAH;IAGF,MAAMsI,wBACJ7H,UAAUmC,kBAAkB,IAC5B5C,IAAIY,UAAU,CAACoD,YAAY,CAAC3H,iBAAiB,KAAK;IAEpD,OAAO;QACLmG,GAAGxC,IAAIyC,aAAa,CAACC,OAAO;QAC5B+F,GAAGzI,IAAI0I,WAAW;QAClBC,GAAG1B,2BAA2BvG;QAC9BkI,GAAGrN;QACHiD,GAAG;QACH+D,GAAG;YACD;gBACE+E;gBACAG;gBACAQ;gBACAK;aACD;SACF;QACDO,GAAG;YAACX;YAAaE;SAAkB;QACnCU,GAAG,OAAO9I,IAAIY,UAAU,CAACmI,SAAS,KAAK;QACvCpG,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA,SAASqH,8BACP1F,uBAA8D;IAI9D,IAAI,CAACA,yBAAyB;QAC5B,MAAM,qBAAqE,CAArE,IAAIW,8BAAc,CAAC,oDAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoE;IAC5E;AACF;AAEA,mFAAmF;AACnF,SAASgF,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACd7F,uBAAuB,EACvB8F,0BAA0B,EAC1BC,iBAAiB,EACjB1N,KAAK,EAQN;IACCwN;IACA,MAAMG,WAAW3I,cAAK,CAAC4I,GAAG,CACxBC,IAAAA,kCAAe,EACbN,mBACA5F,yBACA3H;IAIJ,MAAM8N,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmBN,SAAShI,CAAC;QAC7BuI,0BAA0BP,SAAS5B,CAAC;QACpCoC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVnD,oBAAoByC,SAAS/L,CAAC;QAC9BuK,WAAWwB,SAASzB,CAAC;QACrBoC,aAAaX,SAAS5H,CAAC;IACzB;IAEA,MAAMwI,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,MAAM,EAAEW,kBAAkB,EAAE,GAC1BhQ,QAAQ;IAEV,qBACE,qBAACgQ,mBAAmBC,QAAQ;QAC1BpN,OAAO;YACLqN,QAAQ;YACR3O;QACF;kBAEA,cAAA,qBAACyN;sBACC,cAAA,qBAACmB,kBAAS;gBACRL,aAAaA;gBACbM,kBAAkBlB,SAAS1B,CAAC;gBAC5BH,aAAa6B,SAAS9B,CAAC;gBACvB6B,mBAAmBA;;;;AAK7B;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAASoB,SAAY,EACnBvB,iBAAiB,EACjBC,cAAc,EACd7F,uBAAuB,EACvB8F,0BAA0B,EAC1BC,iBAAiB,EACjB1N,KAAK,EAQN;IACCwN;IACA,MAAMG,WAAW3I,cAAK,CAAC4I,GAAG,CACxBC,IAAAA,kCAAe,EACbN,mBACA5F,yBACA3H;IAIJ,MAAM8N,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmBN,SAAShI,CAAC;QAC7BuI,0BAA0BP,SAAS5B,CAAC;QACpCoC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVnD,oBAAoByC,SAAS/L,CAAC;QAC9BuK,WAAWwB,SAASzB,CAAC;QACrBoC,aAAaX,SAAS5H,CAAC;IACzB;IAEA,MAAMwI,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,qBACE,qBAACL;kBACC,cAAA,qBAACmB,kBAAS;YACRL,aAAaA;YACbM,kBAAkBlB,SAAS1B,CAAC;YAC5BH,aAAa6B,SAAS9B,CAAC;YACvB6B,mBAAmBA;;;AAI3B;AASA,eAAeqB,yBACbtI,GAAoB,EACpBvB,GAAqB,EACrBpB,GAAwC,EACxChD,QAAgB,EAChB8C,KAAyB,EACzBI,UAAsB,EACtBH,SAAoB,EACpBmL,oBAA0C,EAC1CC,iBAAsC,EACtCC,cAAqC,EACrCC,wBAA8D,EAC9DtJ,aAA+B;IAE/B,MAAMuJ,iBAAiBtO,aAAa;IACpC,IAAIsO,gBAAgB;QAClBlK,IAAItC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAMyM,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJ5H,uBAAuB,EACvB6H,qBAAqB,EACrBrH,YAAY,EACZsH,gBAAgB,EAChBC,aAAa,EACb5D,cAAc,EAAE,EAChB6D,cAAc,EACf,GAAG3L;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAImE,aAAayH,YAAY,EAAE;QAC7B,MAAMC,eAAeC,IAAAA,wDAAyB,EAAC3H;QAE/C,kEAAkE;QAClE,0EAA0E;QAC1E,+EAA+E;QAC/E,8DAA8D;QAE9D,MAAM4H,2BAA2B;YAC/B,IAAI,CAAC/L,WAAWoD,YAAY,CAACC,SAAS,EAAE;gBACtC,OAAO;YACT;YACA,IAAIrD,WAAWgD,GAAG,EAAE;gBAClB,OAAO;YACT;YACA,MAAMgJ,gBAAgB9I,kDAAoB,CAAC+I,QAAQ;YACnD,OAAO,CAAC,CACND,CAAAA,iBACCA,CAAAA,cAAcjO,IAAI,KAAK,eACtBiO,cAAcjO,IAAI,KAAK,sBACvBiO,cAAcjO,IAAI,KAAK,OAAM,CAAC;QAEpC;QAEA,MAAMmO,mBAAgD,CAAC,GAAGC;YACxD,MAAMC,mBAAmBP,aAAapR,OAAO,IAAI0R;YACjD,IAAIJ,4BAA4B;gBAC9B,+CAA+C;gBAC/CM,IAAAA,8CAAkB,EAACD;YACrB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBE,WAAWJ,gBAAgB,GAAGA;QAE9B,MAAMK,sBAAqD,CAAC,GAAGJ;YAC7D,MAAMK,eAAeX,aAAaY,SAAS,IAAIN;YAC/C,IAAIJ,4BAA4B;gBAC9BW,IAAAA,iDAAqB,EAACF;YACxB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBF,WAAWC,mBAAmB,GAAGA;IACnC;IAEA,IAAIjS,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAEiG,QAAQ,EAAE,GAAG,IAAIkM,IAAIlK,IAAI3C,GAAG,IAAI,KAAK;QAC7CE,WAAW4M,YAAY,oBAAvB5M,WAAW4M,YAAY,MAAvB5M,YAA0BS,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DnG,QAAQC,GAAG,CAACsS,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAACrK,MAClB;QACAA,IAAIsK,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B/B,kBAAkBgC,KAAK,GAAG;YAE1B,IAAI,iBAAiBX,YAAY;gBAC/B,MAAMY,UAAUC,IAAAA,8DAA+B,EAAC;oBAAEC,OAAO;gBAAK;gBAC9D,IAAIF,SAAS;oBACXG,IAAAA,iBAAS,IACNC,SAAS,CAACC,6BAAkB,CAACC,sBAAsB,EAAE;wBACpDC,WAAWP,QAAQQ,wBAAwB;wBAC3CC,YAAY;4BACV,iCACET,QAAQU,wBAAwB;4BAClC,kBAAkBL,6BAAkB,CAACC,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFX,QAAQQ,wBAAwB,GAC9BR,QAAQY,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMC,WAAwC;QAC5CnP,YAAYwM,iBAAiB,MAAMzQ;IACrC;IAEA,MAAMgF,yBAAyB,CAAC,EAAC8L,oCAAAA,iBAAkBuC,kBAAkB;IAErE3E,8BAA8B1F;IAE9B,MAAMsK,kBAAkBC,IAAAA,kCAAqB,EAAC;QAAE1C;IAAsB;IAEtE2C,IAAAA,+CAA8B,EAAC;QAC7BxR,MAAMkD,UAAUlD,IAAI;QACpBgH;QACA6H;QACAyC;IACF;IAEA9J,aAAaiK,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAE7O,MAAMjD,UAAU,EAAE+R,oBAAoB,EAAE,GAAGlK;IACnD,IAAIwH,gBAAgB;QAClB0C,qBACE,kFACA/T,QAAQC,GAAG;IAEf;IAEAsF,UAAUoE,YAAY,GAAG,EAAE;IAC3B8J,SAAS9J,YAAY,GAAGpE,UAAUoE,YAAY;IAE9C,qCAAqC;IACrCrE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnB0O,IAAAA,mCAAoB,EAAC1O;IAErB,MAAM,EACJlE,iBAAiB,EACjBT,iBAAiB,EACjBK,YAAY,EACZP,kBAAkB,EAClBK,YAAY,EACZY,KAAK,EACN,GAAGgP;IAEJ,MAAM,EAAEhJ,kBAAkB,EAAEjF,mBAAmB,EAAE,GAAG8C;IAEpD;;;GAGC,GACD,IAAI1F;IAEJ,IAAI6H,oBAAoB;QACtB7H,YAAYoU,OAAOC,IAAI,CACrB,MAAMC,OAAOC,MAAM,CAACxF,MAAM,CAAC,SAASqF,OAAOC,IAAI,CAAC/L,IAAI3C,GAAG,IACvD6O,QAAQ,CAAC;IACb,OAAO,IAAIrU,QAAQC,GAAG,CAACsS,YAAY,KAAK,QAAQ;QAC9C1S,YAAYsU,OAAOG,UAAU;IAC/B,OAAO;QACLzU,YAAY,AACVM,QAAQ,6BACRoU,MAAM;IACV;IAEA;;GAEC,GACD,MAAMhS,SAASmD,WAAWnD,MAAM,IAAI,CAAC;IAErC,MAAMG,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAM+R,0BAA0BC,IAAAA,kDAAyB,EAACtM;IAE1D,MAAM2B,eAAe,MAAM4K,IAAAA,6BAAe,EACxCnP,UAAUlD,IAAI,EACdmD,KACA/C;IAGF,MAAMqC,MAAwB;QAC5BE,cAAc6E;QACdrE;QACAE;QACAH;QACAmL;QACAhO;QACA4C;QACAqP,YAAYhU;QACZ4D,wBAAwBiQ;QACxBzD;QACA1L;QACAjE;QACAvB;QACA2C;QACA6G;QACAmE;QACAsD;QACApP;QACAkF;QACAW;QACAuC;IACF;IAEAiJ,IAAAA,iBAAS,IAAG6B,oBAAoB,CAAC,cAAcpS;IAE/C,IAAIkF,oBAAoB;YAwGlB+L;QAvGJ,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMoB,+BAA+B9B,IAAAA,iBAAS,IAAG+B,IAAI,CACnDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAEzS,UAAU;YAC7C6Q,YAAY;gBACV,cAAc7Q;YAChB;QACF,GACA0S;QAGF,MAAM7F,WAAW,MAAMwF,6BACrB1M,KACAvB,KACA9B,KACA2O,UACAzR;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACEqN,SAAS8F,aAAa,IACtBC,IAAAA,qCAAmB,EAAC/F,SAAS8F,aAAa,KAC1CzP,WAAW2P,sBAAsB,EACjC;YACAC,IAAAA,SAAI,EAAC;YACL,KAAK,MAAMC,UAAUC,IAAAA,0CAAwB,EAACnG,SAAS8F,aAAa,EAAG;gBACrEG,IAAAA,SAAI,EAACC;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAIhQ,UAAUkQ,wBAAwB,EAAE;YACtC,MAAMlQ,UAAUkQ,wBAAwB;QAC1C;QACA,IAAIpG,SAASqG,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoBvG,SAASqG,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAG9S,KAAK;YACxE,IAAI4S,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAIvG,SAAS0G,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoBvG,SAAS0G,SAAS,CAACE,IAAI,CAAC,CAAC3N,MACjD4N,IAAAA,mCAAe,EAAC5N;YAElB,IAAIsN,mBAAmB,MAAMA;QAC/B;QAEA,MAAMpV,UAA+B;YACnCiT;QACF;QACA,oEAAoE;QACpE,IACElO,UAAU4Q,kBAAkB,IAC5B5Q,UAAU6Q,uBAAuB,IACjC7Q,UAAU8Q,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAAChR,WAAWiR,OAAO,CAAC;gBAC3D,IAAIxW,QAAQC,GAAG,CAACwW,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CnR;gBAC3D;YACF;YAEA,IAAIE,WAAWkR,SAAS,EAAE;gBACxBlR,WAAWkR,SAAS,CAACN;YACvB,OAAO;gBACL9V,QAAQoW,SAAS,GAAGN;YACtB;QACF;QAEA,IAAIjH,SAASwH,aAAa,EAAE;YAC1BpD,SAASqD,SAAS,GAAGzH,SAASwH,aAAa,CAAC1S,IAAI,CAAC;QACnD;QAEA,uEAAuE;QACvE,MAAM4S,cAAcC,OAAO3H,SAAS4H,cAAc;QAClDrQ,IAAIsQ,SAAS,CAACC,+CAA6B,EAAEJ;QAC7CtD,SAASlT,OAAO,KAAK,CAAC;QACtBkT,SAASlT,OAAO,CAAC4W,+CAA6B,CAAC,GAAGJ;QAElD,yEAAyE;QACzE,YAAY;QACZ,IAAIxR,UAAU6R,WAAW,KAAK,SAAS/H,SAASgI,mBAAmB,KAAK,GAAG;YACzE5D,SAAS6D,YAAY,GAAG;gBAAEtM,YAAY;gBAAGE,QAAQ7K;YAAU;QAC7D,OAAO;YACL,gEAAgE;YAChEoT,SAAS6D,YAAY,GAAG;gBACtBtM,YACEqE,SAASgI,mBAAmB,IAAIpM,0BAAc,GAC1C,QACAoE,SAASgI,mBAAmB;gBAClCnM,QACEmE,SAASkI,eAAe,IAAItM,0BAAc,GACtC5K,YACAgP,SAASkI,eAAe;YAChC;QACF;QAEA,qCAAqC;QACrC,IAAI9D,EAAAA,yBAAAA,SAAS6D,YAAY,qBAArB7D,uBAAuBzI,UAAU,MAAK,GAAG;YAC3CyI,SAAS+D,iBAAiB,GAAG;gBAC3BC,aAAalS,UAAUmS,uBAAuB;gBAC9C5I,OAAOvJ,UAAUoS,iBAAiB;YACpC;QACF;QAEA,IAAItI,SAAShE,qBAAqB,EAAE;YAClCoI,SAASpI,qBAAqB,GAAGgE,SAAShE,qBAAqB;QACjE;QAEA,OAAO,IAAIuM,qBAAY,CAAC,MAAMC,IAAAA,oCAAc,EAACxI,SAASyI,MAAM,GAAGtX;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAM6K,wBACJ3F,WAAW2F,qBAAqB,KAAIuF,kCAAAA,eAAgBvF,qBAAqB;QAE3E,MAAMpB,aAAaC,IAAAA,kCAAa,EAAClI,YAAY8C,IAAIpC,0BAA0B;QAC3E,MAAM0F,eAAe2P,IAAAA,yCAA2B,EAC9C5P,KACAvB,KACApB,KACAyE,YACAH,cACApE,WAAWsS,eAAe,EAC1BtS,WAAWuS,YAAY,EACvBnX,cACA+P,0BACAxF;QAGF,IACErL,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBwF,WAAW4M,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7DtS,QAAQC,GAAG,CAACsS,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAACrK,QAClB,CAAC1H,oBACD;YACA,MAAM6R,eAAe5M,WAAW4M,YAAY;YAC5CnK,IAAIsK,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAACtK,aAAa8P,WAAW,IAAI,CAAC3S,UAAU4S,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAEhS,QAAQ,EAAE,GAAG,IAAIkM,IAAIlK,IAAI3C,GAAG,IAAI,KAAK;oBAC7C8M,aAAanM,UAAU;gBACzB;YACF;QACF;QAEA,IAAI1F,oBAAoB;YACtB,OAAOmJ,gBAAgBzB,KAAKrD;QAC9B,OAAO,IAAI9D,cAAc;YACvB,OAAOkH,kCAAkCC,KAAKrD,KAAKsD;QACrD;QAEA,MAAMgQ,4BAA4BrF,IAAAA,iBAAS,IAAG+B,IAAI,CAChDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAEzS,UAAU;YAC1C6Q,YAAY;gBACV,cAAc7Q;YAChB;QACF,GACA6V;QAGF,IAAIC,YAAwB;QAC5B,IAAI9D,yBAAyB;YAC3B,gFAAgF;YAChF,MAAM+D,sBAAsB,MAAMC,IAAAA,2BAAY,EAAC;gBAC7CrQ;gBACAvB;gBACAiD;gBACA8J;gBACA8E,gBAAgBvQ;gBAChB3C;gBACA6C;gBACAgJ;gBACAtM;gBACA2O;YACF;YAEA,IAAI8E,qBAAqB;gBACvB,IAAIA,oBAAoB9U,IAAI,KAAK,aAAa;oBAC5C,MAAMiV,qBAAqB3W,yBAAyBC;oBACpD4E,IAAItC,UAAU,GAAG;oBACjBmP,SAASnP,UAAU,GAAG;oBACtB,MAAMwT,SAAS,MAAMM,0BACnBhQ,cACAD,KACAvB,KACA9B,KACA4T,oBACAJ,WACA1H,gBACA6C;oBAGF,OAAO,IAAImE,qBAAY,CAACE,QAAQ;wBAAErE;oBAAS;gBAC7C,OAAO,IAAI8E,oBAAoB9U,IAAI,KAAK,QAAQ;oBAC9C,IAAI8U,oBAAoBI,MAAM,EAAE;wBAC9BJ,oBAAoBI,MAAM,CAACC,cAAc,CAACnF;wBAC1C,OAAO8E,oBAAoBI,MAAM;oBACnC,OAAO,IAAIJ,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAM9X,UAA+B;YACnCiT;QACF;QAEA,MAAMqE,SAAS,MAAMM,0BACnBhQ,cACAD,KACAvB,KACA9B,KACA9C,YACAsW,WACA1H,gBACA6C;QAGF,IAAIlO,UAAUkQ,wBAAwB,EAAE;YACtC,MAAMlQ,UAAUkQ,wBAAwB;QAC1C;QAEA,oEAAoE;QACpE,IACElQ,UAAU4Q,kBAAkB,IAC5B5Q,UAAU6Q,uBAAuB,IACjC7Q,UAAU8Q,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAAChR,WAAWiR,OAAO,CAAC;gBAC3D,IAAIxW,QAAQC,GAAG,CAACwW,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CnR;gBAC3D;YACF;YAEA,IAAIE,WAAWkR,SAAS,EAAE;gBACxBlR,WAAWkR,SAAS,CAACN;YACvB,OAAO;gBACL9V,QAAQoW,SAAS,GAAGN;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAIsB,qBAAY,CAACE,QAAQtX;IAClC;AACF;AAcO,MAAMd,uBAAsC,CACjDyI,KACAvB,KACApE,UACA8C,OACA7C,qBACAiD,YACAmL,0BACAnQ,aACA6G;QAaiB7B;IAXjB,IAAI,CAACyC,IAAI3C,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAI2I,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAM3I,MAAMqT,IAAAA,kCAAgB,EAAC1Q,IAAI3C,GAAG,EAAEnF,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAMqQ,uBAAuBpQ,oBAAoB6H,IAAI5H,OAAO,EAAE;QAC5DG;QACAS,mBAAmBuE,WAAWoD,YAAY,CAAC3H,iBAAiB,KAAK;QACjEW,aAAa,GAAE4D,2BAAAA,WAAWuS,YAAY,qBAAvBvS,yBAAyB5D,aAAa;IACvD;IAEA,MAAM,EAAEnB,iBAAiB,EAAEiB,yBAAyB,EAAE,GAAG8O;IAEzD,MAAMC,oBAAoB;QAAEgC,OAAO;IAAM;IACzC,IAAI/B,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAOlL,WAAWmI,SAAS,KAAK,UAAU;QAC5C,IAAIpL,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAIuH,8BAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA4G,iBAAiBkI,IAAAA,mCAAmB,EAClCpT,WAAWmI,SAAS,EACpBnI,WAAWnD,MAAM;IAErB;IAEA,IACEqO,CAAAA,kCAAAA,eAAgBvF,qBAAqB,KACrC3F,WAAW2F,qBAAqB,EAChC;QACA,MAAM,qBAEL,CAFK,IAAIrB,8BAAc,CACtB,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMzE,YAAYwT,IAAAA,0BAAe,EAAC;QAChC1W,MAAMqD,WAAWsT,WAAW,CAACC,UAAU,CAAC5W,IAAI;QAC5CI;QACAiD;QACAiL;QACA,8CAA8C;QAC9ChQ;QACA6G,SAASD,cAAcC,OAAO;QAC9B5F;IACF;IAEA,OAAOsX,0CAAgB,CAACrQ,GAAG,CACzBtD,WACA,sBAAsB;IACtBkL,0BACA,mBAAmB;IACnBtI,KACAvB,KACApB,KACAhD,UACA8C,OACAI,YACAH,WACAmL,sBACAC,mBACAC,gBACAC,0BACAtJ;AAEJ;AAEA,eAAe8Q,eACbjQ,YAA0B,EAC1BD,GAAoB,EACpBvB,GAAqB,EACrB9B,GAAqB,EACrBG,IAAgB,EAChBqT,SAAc,EACd1H,cAAqC,EACrC6C,QAAqC;IAErC,MAAM,EAAEjG,WAAW,EAAE9L,KAAK,EAAEc,QAAQ,EAAEkD,UAAU,EAAE,GAAGZ;IAErD,MAAM,EACJqU,QAAQ,EACRC,OAAO,EACPC,aAAa,EACbhQ,uBAAuB,EACvBQ,YAAY,EACZyP,WAAW,EACX5Q,MAAM,KAAK,EACXI,YAAY,EACZyQ,aAAa,KAAK,EAClBhR,6BAA6B,EAC7BlG,IAAI,EACJmX,qBAAqB,EACrBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,uBAAuB,EACxB,GAAGjU;IAEJqJ,8BAA8B1F;IAE9B,MAAM,EAAE8F,0BAA0B,EAAEyK,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAMC,4BAA4BC,IAAAA,0DAA4B,EAACrY;IAE/D,MAAMsY,kBAAkBC,IAAAA,yBAAiB,EACvClH,IAAAA,iBAAS,IAAGmH,uBAAuB,IACnCpR,aAAaqR,mBAAmB;IAGlC,MAAMC,YACJf,cAAcgB,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnX,GAAG,CAAC,CAACkX,WAAc,CAAA;YAClBE,KAAK,GAAGjN,YAAY,OAAO,EAAE+M,WAAWG,IAAAA,wCAAmB,EACzD5V,KACA,QACC;YACH6V,SAAS,EAAEjB,gDAAAA,4BAA8B,CAACa,SAAS;YACnDjB;YACAsB,UAAU;YACVlZ;QACF,CAAA;IAEJ,MAAM,CAACwN,gBAAgB2L,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DzB,eACA,6CAA6C;IAC7C,8EAA8E;IAC9E7L,aACA8L,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC5V,KAAK,OACzBpD,OACAW;IAGF,MAAM0Y,4BAAwD,IAAIjL;IAClE,MAAMkL,gBAAgB;IACtB,SAASC,qBAAqB3S,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAMoW,+BAA+BC,IAAAA,qDAAiC,EACpEzS,KACA6Q,YACAwB,2BACAC,eACAC;IAGF,SAASG,qBAAqB9S,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IAEA,MAAMuW,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD7S,KACA6Q,YACAwB,2BACAM,mBACAL,eACAI;IAGF,IAAII,oBAA8C;IAElD,MAAMtE,YAAYtQ,IAAIsQ,SAAS,CAACuE,IAAI,CAAC7U;IACrC,MAAM8U,eAAe9U,IAAI8U,YAAY,CAACD,IAAI,CAAC7U;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrD8B,OACA,uEAAuE;QACvE1I,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAACsS,YAAY,KAAK,UAC7B,yEAAyE;QACzEzJ,aAAaC,SAAS,EACtB;YACA,wFAAwF;YACxF,MAAMJ,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACA6D,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,MAAM,CAAC0E,mBAAmBC,iBAAiB,GAAGC;YAC9CP,WAAWQ,WAAW,GAAGF;YAEzB,MAAMgG,oBAAoB,MAAMrG,kDAAoB,CAACC,GAAG,CACtDT,cACAuT,+CAAyB,EACzB;gBACEvT,aAAawT,cAAc,GAAG;gBAC9B,OAAO/R,aAAaN,sBAAsB,CACxCZ,YACAU,wBAAwBG,aAAa,EACrC;oBACEhB,SAAS0S;oBACTW,iBAAiB,IACfzT,aAAawT,cAAc,KAAK,OAAO,cAAc;oBACvD7b;gBACF;YAEJ,GACA;gBACEqI,aAAawT,cAAc,GAAG;YAChC;YAGFxS,4BACEJ,mBACA/D,MACAH,KACA8B,IAAItC,UAAU,KAAK,KACnB+E,yBACAjB;YAGFoT,oBAAoB,IAAIM,0CAAiB,CAAC7M;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMtG,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/CT,cACA6D,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrBkX,oBAAoB,IAAIM,0CAAiB,CACvClT,kDAAoB,CAACC,GAAG,CACtBT,cACAyB,aAAaN,sBAAsB,EACnCZ,YACAU,wBAAwBG,aAAa,EACrC;gBACEzJ;gBACAyI,SAAS0S;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMa,IAAAA,wCAA6B;QAEnC,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAOrW,WAAWmI,SAAS,KAAK,UAAU;YAC5C,IAAI+C,CAAAA,kCAAAA,eAAgBnN,IAAI,MAAKuY,4BAAY,CAACC,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+BC,IAAAA,kDAA+B,EAClEX,kBAAkBY,GAAG,IACrB1a,OACA4W;gBAGF,OAAO+D,IAAAA,kCAAY,EACjBH,8BACAI,IAAAA,iDAA2B;YAE/B,OAAO,IAAI1L,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAM/C,YAAY0O,IAAAA,qCAAqB,EAAC3L;gBAExC,MAAM4L,SAAS,AACbrc,QAAQ,oBACRqc,MAAM;gBAER,MAAMC,aAAa,MAAM7T,kDAAoB,CAACC,GAAG,CAC/CT,cACAoU,sBACA,qBAACxN;oBACCC,mBAAmBuM,kBAAkBY,GAAG;oBACxClN,gBAAgBA;oBAChB7F,yBAAyBA;oBACzB8F,4BAA4BA;oBAC5BzN,OAAOA;oBACP0N,mBAAmB,CAAC,CAACgK;oBAEvBvL,WACA;oBAAErF,SAAS8S;oBAA0B5Z;gBAAM;gBAG7C,MAAMgb,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtDvC;oBACAR;oBACAgD,sBAAsBvB;oBACtBlC;oBACAa,iBAAiBA;gBACnB;gBACA,OAAO,MAAM6C,IAAAA,+CAAyB,EAACJ,YAAY;oBACjDK,mBAAmBX,IAAAA,kDAA+B,EAChDX,kBAAkBuB,OAAO,IACzBrb,OACA4W;oBAEFoE;oBACA5C;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAMvQ,yBAAyB,AAC7BpJ,QAAQ,oBACRoJ,sBAAsB;QAExB,MAAMkT,aAAa,MAAM7T,kDAAoB,CAACC,GAAG,CAC/CT,cACAmB,sCACA,qBAACyF;YACCC,mBAAmBuM,kBAAkBY,GAAG;YACxClN,gBAAgBA;YAChB7F,yBAAyBA;YACzB8F,4BAA4BA;YAC5BC,mBAAmB,CAAC,CAACgK;YACrB1X,OAAOA;YAET;YACE8G,SAAS8S;YACT5Z;YACAsb,WAAW,CAACzc;gBACVA,QAAQuN,OAAO,CAAC,CAAC9K,OAAOF;oBACtB4Y,aAAa5Y,KAAKE;gBACpB;YACF;YACAia,kBAAkBzD;YAClB0D,kBAAkB;gBAACrC;aAAgB;YACnCvC;QACF;QAGF,MAAMoE,wBAAwBC,IAAAA,oDAAyB,EAAC;YACtDvC;YACAR;YACAgD,sBAAsBvB;YACtBlC;YACAa,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMmD,qBACJxD,4BAA4B,QAAQ,CAAC,CAACF;QAExC,OAAO,MAAM2D,IAAAA,wCAAkB,EAACX,YAAY;YAC1CK,mBAAmBX,IAAAA,kDAA+B,EAChDX,kBAAkBuB,OAAO,IACzBrb,OACA4W;YAEF5Q,oBAAoByV;YACpBE,yBAAyBvY,IAAIS,SAAS,CAAC8X,uBAAuB,KAAK;YACnE7V,SAAS1C,IAAIS,SAAS,CAACiC,OAAO;YAC9BkV;YACA5C;YACAwD,oBAAoB5U;QACtB;IACF,EAAE,OAAOJ,KAAK;QACZ,IACEiV,IAAAA,gDAAuB,EAACjV,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIoG,OAAO,KAAK,YACvBpG,IAAIoG,OAAO,CAAC7B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMvE;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMkV,qBAAqBC,IAAAA,iCAAmB,EAACnV;QAC/C,IAAIkV,oBAAoB;YACtB,MAAM1O,QAAQ4O,IAAAA,8CAA2B,EAACpV;YAC1CqV,IAAAA,UAAK,EACH,GAAGrV,IAAIsV,MAAM,CAAC,mDAAmD,EAAEpb,SAAS,kFAAkF,EAAEsM,OAAO;YAGzK,MAAMxG;QACR;QAEA,IAAIgE;QAEJ,IAAIuR,IAAAA,6CAAyB,EAACvV,MAAM;YAClC1B,IAAItC,UAAU,GAAGwZ,IAAAA,+CAA2B,EAACxV;YAC7CmL,SAASnP,UAAU,GAAGsC,IAAItC,UAAU;YACpCgI,YAAYyR,IAAAA,sDAAkC,EAACnX,IAAItC,UAAU;QAC/D,OAAO,IAAI0Z,IAAAA,8BAAe,EAAC1V,MAAM;YAC/BgE,YAAY;YACZ1F,IAAItC,UAAU,GAAG2Z,IAAAA,wCAA8B,EAAC3V;YAChDmL,SAASnP,UAAU,GAAGsC,IAAItC,UAAU;YAEpC,MAAM4Z,cAAcC,IAAAA,4BAAa,EAACC,IAAAA,iCAAuB,EAAC9V,MAAM6Q;YAEhE,gEAAgE;YAChE,YAAY;YACZ,MAAM5Y,UAAU,IAAI8d;YACpB,IAAIC,IAAAA,oCAAoB,EAAC/d,SAAS6H,aAAamW,cAAc,GAAG;gBAC9DrH,UAAU,cAAc/T,MAAM+Q,IAAI,CAAC3T,QAAQsV,MAAM;YACnD;YAEAqB,UAAU,YAAYgH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B5W,IAAItC,UAAU,GAAG;YACjBmP,SAASnP,UAAU,GAAGsC,IAAItC,UAAU;QACtC;QAEA,MAAM,CAACka,qBAAqBC,qBAAqB,GAAG3D,IAAAA,mCAAkB,EACpEzB,eACA7L,aACA8L,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC5V,KAAK,QACzBpD,OACA;QAGF,MAAMgd,kBAAkB,MAAM9V,kDAAoB,CAACC,GAAG,CACpDT,cACA4F,oBACA/I,MACAH,KACAiW,0BAA0B9X,GAAG,CAAC,AAACqF,IAAYsG,MAAM,IAAI,OAAOtG,KAC5DgE;QAGF,MAAMqS,oBAAoB/V,kDAAoB,CAACC,GAAG,CAChDT,cACAyB,aAAaN,sBAAsB,EACnCmV,iBACArV,wBAAwBG,aAAa,EACrC;YACEzJ;YACAyI,SAAS0S;QACX;QAGF,IAAIM,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAMlT;QACR;QAEA,IAAI;YACF,MAAMsW,aAAa,MAAMhW,kDAAoB,CAACC,GAAG,CAC/CT,cACAyW,+CAAyB,EACzB;gBACEC,gBACE3e,QAAQ;gBACV4e,uBACE,qBAACvO;oBACCvB,mBAAmB0P;oBACnBxP,4BAA4BA;oBAC5BD,gBAAgBsP;oBAChBnV,yBAAyBA;oBACzB+F,mBAAmB,CAAC,CAACgK;oBACrB1X,OAAOA;;gBAGXsd,eAAe;oBACbtd;oBACA,wCAAwC;oBACxCwb,kBAAkB;wBAACuB;qBAAqB;oBACxCnG;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAM6E,qBACJxD,4BAA4B,QAAQ,CAAC,CAACF;YACxC,OAAO,MAAM2D,IAAAA,wCAAkB,EAACwB,YAAY;gBAC1C9B,mBAAmBX,IAAAA,kDAA+B,EAChD,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACTX,kBAAkBuB,OAAO,IACzBrb,OACA4W;gBAEF5Q,oBAAoByV;gBACpBE,yBAAyBvY,IAAIS,SAAS,CAAC8X,uBAAuB,KAAK;gBACnE7V,SAAS1C,IAAIS,SAAS,CAACiC,OAAO;gBAC9BkV,uBAAuBC,IAAAA,oDAAyB,EAAC;oBAC/CvC;oBACAR;oBACAgD,sBAAsB,EAAE;oBACxBzD;oBACAa,iBAAiBA;gBACnB;gBACAF;gBACAwD,oBAAoB5U;YACtB;QACF,EAAE,OAAOuW,UAAe;YACtB,IACEjf,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB2d,IAAAA,6CAAyB,EAACoB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1B/e,QAAQ;gBACV+e;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAAS/V;IACP,IAAIF;IACJ,IAAImW,SAAS,IAAIC,QAAyB,CAACC;QACzCrW,oBAAoBqW;IACtB;IACA,OAAO;QAACrW;QAAoBmW;KAAO;AACrC;AAEA;;;;;CAKC,GACD,eAAe/V,4BACbJ,iBAA+D,EAC/D/D,IAAgB,EAChBH,GAAqB,EACrBwa,UAAmB,EACnBjW,uBAA2E,EAC3EjB,YAA0B;QAuBHA;IArBvB,MAAM,EACJpD,cAAc6E,YAAY,EAC1BnH,0BAA0B,EAC1BoH,YAAY,EACZpI,KAAK,EACLgE,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EAAEiF,wBAAwB,KAAK,EAAEqP,OAAO,EAAE,GAAG1T;IAEnD,iEAAiE;IACjE,yDAAyD;IACzD,MAAMwJ,iBAAiB,KAAO;IAC9B,MAAM,EAAEC,0BAA0B,EAAE,GAAG0K,IAAAA,4CAAwB;IAE/D,MAAM5P,aAAaC,IAAAA,kCAAa,EAC9BL,aAAa5E,IAAI,EACjBvC;IAGF,MAAM4I,kBAAiBlD,4BAAAA,aAAamD,OAAO,CAACrI,GAAG,CAC7CsI,8CAA4B,sBADPpD,0BAEpBpF,KAAK;IAER,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMuc,mCAAmC,IAAIjV;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAMkV,gCAAgC,IAAIlV;IAE1C,kFAAkF;IAClF,yBAAyB;IACzB,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMgV,0BAA0B/Y,cAAK,CAAC+E,iBAAiB;IACvD,MAAMiU,0BAA0B7V,aAAa4B,iBAAiB;IAE9D,iEAAiE;IACjE,8DAA8D;IAC9D,wEAAwE;IACxE,6BAA6B;IAC7B,MAAMtB,2BAA2BC,IAAAA,+CAA8B;IAC/D,MAAMuV,8BAA8C;QAClDlc,MAAM;QACNkH,OAAO;QACPV;QACAH;QACAc,cAAc4U,8BAA8B3U,MAAM;QAClDC,YAAYyU;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvB/U;QACAO,iBAAiB;QACjBhB;QACAiB,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM;eAAItB,aAAasB,IAAI;SAAC;QAC5BjB;QACAkB,uBAAuB;QACvBC;QACAG,mBAAmBiU;IACrB;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAME,uBAAuB,MAAMhX,kDAAoB,CAACC,GAAG,CACzD8W,6BACA1T,eACAhH,MACAH,KACAwa;IAGF,MAAMO,6BAA6BjX,kDAAoB,CAACC,GAAG,CACzD8W,6BACA9V,aAAaiW,SAAS,EACtBF,sBACAvW,wBAAwBG,aAAa,EACrC;QACEzJ;QACAyI,SAAS,CAACF;YACR,MAAMsG,SAASmR,IAAAA,8CAA0B,EAACzX;YAE1C,IAAIsG,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAIoR,IAAAA,4CAAsB,EAAC1X,MAAM;gBAC/B,kBAAkB;gBAClBoO,QAAQiH,KAAK,CAACrV;gBACd,OAAOjI;YACT;YAEA,IAAIkf,iCAAiC1U,MAAM,CAACoV,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACLjgB,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;gBACAC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCC,YAAYjgB;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDwK,QAAQ2U,8BAA8B3U,MAAM;IAC9C;IAGF,8EAA8E;IAC9Ec,IAAAA,+CAAmB,EAACnB;IACpB,MAAMA,YAAYoB,UAAU;IAE5B4T,8BAA8B3T,KAAK;IACnC0T,iCAAiC1T,KAAK;IAEtC,gEAAgE;IAChE,iEAAiE;IACjE,IAAItG,UAAUkQ,wBAAwB,EAAE;QACtCzM,gCACE,qBAACuX;YACCC,IAAI;gBACF9J,QAAQiH,KAAK,CAACpY,UAAUkQ,wBAAwB;YAClD;;QAGJ;IACF;IAEA,IAAIgL;IACJ,IAAI;QACFA,sBAAsB,MAAMC,IAAAA,yDAAgC,EAC1Db;IAEJ,EAAE,OAAOvX,KAAK;QACZ,IACEkX,8BAA8B3U,MAAM,CAACoV,OAAO,IAC5CV,iCAAiC1U,MAAM,CAACoV,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACLjgB,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;QAChE;IACF;IAEA,IAAII,qBAAqB;QACvB,MAAME,gCAAgC,IAAIrW;QAC1C,MAAMsW,mCAAmC,IAAItW;QAC7C,MAAMuW,8BAA8C;YAClDpd,MAAM;YACNkH,OAAO;YACPV;YACAH;YACAc,cAAc+V,8BAA8B9V,MAAM;YAClDC,YAAY8V;YACZ,sDAAsD;YACtD,qDAAqD;YACrDpW,aAAa;YACbO,iBAAiB;YACjBhB;YACAiB,YAAYC,0BAAc;YAC1BC,QAAQD,0BAAc;YACtBE,OAAOF,0BAAc;YACrBG,MAAM;mBAAItB,aAAasB,IAAI;aAAC;YAC5BjB;YACAkB,uBAAuB;YACvBC,gBAAgBjL;YAChBoL,mBAAmBgU;QACrB;QAEA,MAAMK,YAAY,AAChB3f,QAAQ,oBACR2f,SAAS;QACX,MAAMgB,6BAA6BlY,kDAAoB,CAACC,GAAG,CACzDgY,6BACAf,yBACA,qBAAC9Q;YACCC,mBAAmBwR,oBAAoBM,iBAAiB;YACxD7R,gBAAgBA;YAChB7F,yBAAyBA;YACzB8F,4BAA4BA;YAC5BC,mBAAmB,CAAC,CAACgK;YACrB1X,OAAOA;YAET;YACEmJ,QAAQ8V,8BAA8B9V,MAAM;YAC5CrC,SAAS,CAACF;gBACR,MAAMsG,SAASmR,IAAAA,8CAA0B,EAACzX;gBAE1C,IAAIsG,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAIoR,IAAAA,4CAAsB,EAAC1X,MAAM;oBAC/B,kBAAkB;oBAClBoO,QAAQiH,KAAK,CAACrV;oBACd,OAAOjI;gBACT;gBAEA,IAAIsgB,8BAA8B9V,MAAM,CAACoV,OAAO,EAAE;gBAChD,4EAA4E;gBAC9E,OAAO,IACLjgB,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;gBAChE;YACF;QAGF;QAGFS,2BAA2BE,KAAK,CAAC,CAAC1Y;YAChC,IACEkX,8BAA8B3U,MAAM,CAACoV,OAAO,IAC5CgB,IAAAA,6CAA2B,EAAC3Y,MAC5B;YACA,4EAA4E;YAC9E,OAAO,IACLtI,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;gBACA,8EAA8E;gBAC9E,mFAAmF;gBACnFC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;YAChE;QACF;QAEA,sEAAsE;QACtE,uGAAuG;QACvG1U,IAAAA,+CAAmB,EAACnB;QACpB,MAAMA,YAAYoB,UAAU;QAC5B+U,8BAA8B9U,KAAK;IACrC;IAEA,MAAMqV,wBAAwB,IAAI5W;IAClC,MAAM6W,wBAAwBC,IAAAA,4CAA0B,EACtD,MAAM,yBAAyB;;IAGjC,MAAMC,4BAA4C;QAChD5d,MAAM;QACNkH,OAAO;QACPV;QACAH;QACAc,cAAcsW,sBAAsBrW,MAAM;QAC1CC,YAAYoW;QACZ,8EAA8E;QAC9E1W,aAAa;QACbO,iBAAiBoW;QACjBpX;QACAiB,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM;eAAItB,aAAasB,IAAI;SAAC;QAC5BjB;QACAkB,uBAAuB;QACvBC;QACAG,mBAAmBiU;IACrB;IAEA,MAAM4B,yBAAyB,MAAM1Y,kDAAoB,CAACC,GAAG,CAC3DwY,2BACApV,eACAhH,MACAH,KACAwa;IAGF,MAAM9D,oBAAoB,MAAMkF,IAAAA,yDAAgC,EAC9Da,IAAAA,2DAAkC,EAChC;QACE,MAAMC,kBAAkB,MAAM5Y,kDAAoB,CAACC,GAAG,CACpD,qBAAqB;QACrBwY,2BACA,sBAAsB;QACtBxX,aAAaiW,SAAS,EACtB,4CAA4C;QAC5CwB,wBACAjY,wBAAwBG,aAAa,EACrC;YACEzJ;YACAyI,SAAS,CAACF;gBACR,IACE4Y,sBAAsBrW,MAAM,CAACoV,OAAO,IACpCgB,IAAAA,6CAA2B,EAAC3Y,MAC5B;oBACA,OAAOA,IAAIsG,MAAM;gBACnB;gBAEA,IAAIoR,IAAAA,4CAAsB,EAAC1X,MAAM;oBAC/B,kBAAkB;oBAClBoO,QAAQiH,KAAK,CAACrV;oBACd,OAAOjI;gBACT;gBAEA,OAAO0f,IAAAA,8CAA0B,EAACzX;YACpC;YACAuC,QAAQqW,sBAAsBrW,MAAM;QACtC;QAEF,OAAO2W;IACT,GACA;QACEN,sBAAsBrV,KAAK;IAC7B;IAIJ,MAAM4V,wBAAwBL,IAAAA,4CAA0B,EACtD,MAAM,wBAAwB;;IAEhC,MAAMM,wBAAwB,IAAIpX;IAClC,MAAMqX,4BAA4C;QAChDle,MAAM;QACNkH,OAAO;QACPV;QACAH;QACAc,cAAc8W,sBAAsB7W,MAAM;QAC1CC,YAAY4W;QACZ,oFAAoF;QACpFlX,aAAa;QACbO,iBAAiB0W;QACjB1X;QACAiB,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM;eAAItB,aAAasB,IAAI;SAAC;QAC5BjB;QACAkB,uBAAuB;QACvBC;QACAG,mBAAmBgU;IACrB;IAEA,IAAImC,oBAAoBC,IAAAA,8CAA4B;IAEpD,IAAI;QACF,MAAM/B,YAAY,AAChB3f,QAAQ,oBACR2f,SAAS;QACX,IAAI,EAAEgC,SAASC,kBAAkB,EAAE,GACjC,MAAMR,IAAAA,2DAAkC,EACtC,IACE3Y,kDAAoB,CAACC,GAAG,CACtB8Y,2BACA7B,yBACA,qBAAC9Q;gBACCC,mBAAmBuM,kBAAkBuF,iBAAiB;gBACtD7R,gBAAgBA;gBAChB7F,yBAAyBA;gBACzB8F,4BAA4BA;gBAC5BC,mBAAmB,CAAC,CAACgK;gBACrB1X,OAAOA;gBAET;gBACEmJ,QAAQ6W,sBAAsB7W,MAAM;gBACpCrC,SAAS,CAACF,KAAc0Z;oBACtB,IACEf,IAAAA,6CAA2B,EAAC3Y,QAC5BoZ,sBAAsB7W,MAAM,CAACoV,OAAO,EACpC;wBACA,MAAMgC,iBAAiBD,UAAUC,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtCC,IAAAA,2CAAyB,EACvB3c,WACA0c,gBACAL,mBACAH;wBAEJ;wBACA;oBACF;oBAEA,IAAIzB,IAAAA,4CAAsB,EAAC1X,MAAM;wBAC/B,kBAAkB;wBAClBoO,QAAQiH,KAAK,CAACrV;wBACd,OAAOjI;oBACT;oBAEA,OAAO0f,IAAAA,8CAA0B,EAACzX;gBACpC;YAGF,IAEJ;YACEoZ,sBAAsB7V,KAAK;QAC7B;QAGJ,MAAM,EAAEsW,cAAc,EAAE,GAAG,MAAMC,IAAAA,uCAAc,EAACL;QAChD/Y,gCACE,qBAACuX;YACCC,IAAI6B,0CAAwB,CAAC5G,IAAI,CAC/B,MACAlW,WACA4c,iBAAiBG,8BAAY,CAACC,KAAK,GAAGD,8BAAY,CAACE,IAAI,EACvDZ,mBACAT;;IAIR,EAAE,OAAOsB,aAAa;QACpB,wEAAwE;QACxE,gDAAgD;QAEhD,IAAIC,kBAAkBL,0CAAwB,CAAC5G,IAAI,CACjD,MACAlW,WACA+c,8BAAY,CAACK,OAAO,EACpBf,mBACAT;QAGF,IAAInhB,QAAQC,GAAG,CAACigB,gBAAgB,IAAIlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAAE;YACtE,8EAA8E;YAC9E,mFAAmF;YACnF,MAAMyC,0BAA0BF;YAChCA,kBAAkB;gBAChBhM,QAAQiH,KAAK,CACX;gBAEFjH,QAAQiH,KAAK,CAAC8E;gBACdG;YACF;QACF;QAEA5Z,gCAAkB,qBAACuX;YAAUC,IAAIkC;;IACnC;AACF;AAEA,eAAenC,UAAU,EAAEC,EAAE,EAAyB;IACpD,IAAI;QACF,MAAMA;IACR,EAAE,OAAM,CAAC;IACT,OAAO;AACT;AAcA;;CAEC,GACD,SAASqC,+BAA+Btd,SAAoB;IAC1D,MAAM,EAAEmC,kBAAkB,EAAE,GAAGnC;IAC/B,IAAI,CAACmC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAewN,kBACb/M,GAAoB,EACpBvB,GAAqB,EACrB9B,GAAqB,EACrB2O,QAAqC,EACrCxO,IAAgB;IAEhB,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMqT,YAAY;IAElB,MAAM,EACJ9K,WAAW,EACX9K,0BAA0B,EAC1BoH,YAAY,EACZpI,KAAK,EACLc,QAAQ,EACRkD,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJiF,wBAAwB,KAAK,EAC7BoP,QAAQ,EACRC,OAAO,EACPC,aAAa,EACbhQ,uBAAuB,EACvBQ,YAAY,EACZyP,WAAW,EACX5Q,MAAM,KAAK,EACXI,YAAY,EACZuM,sBAAsB,EACtBkE,aAAa,KAAK,EAClBhR,6BAA6B,EAC7BlG,IAAI,EACJmX,qBAAqB,EACrBE,4BAA4B,EAC7B,GAAGhU;IAEJqJ,8BAA8B1F;IAE9B,MAAMY,aAAaC,IAAAA,kCAAa,EAACjF,MAAMvC;IACvC,MAAMD,sBAAsB8C,UAAU9C,mBAAmB;IAEzD,MAAM,EAAE0M,0BAA0B,EAAEyK,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAMC,4BAA4BC,IAAAA,0DAA4B,EAACrY;IAE/D,MAAMsY,kBAAkBC,IAAAA,yBAAiB,EACvClH,IAAAA,iBAAS,IAAGmH,uBAAuB,IACnCpR,aAAaqR,mBAAmB;IAGlC,MAAMC,YACJf,cAAcgB,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnX,GAAG,CAAC,CAACkX,WAAc,CAAA;YAClBE,KAAK,GAAGjN,YAAY,OAAO,EAAE+M,WAAWG,IAAAA,wCAAmB,EACzD5V,KACA,QACC;YACH6V,SAAS,EAAEjB,gDAAAA,4BAA8B,CAACa,SAAS;YACnDjB;YACAsB,UAAU;YACVlZ;QACF,CAAA;IAEJ,MAAM,CAACwN,gBAAgB2L,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DzB,eACA,6CAA6C;IAC7C,8EAA8E;IAC9E7L,aACA8L,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC5V,KAAK,OACzBpD,OACAW;IAGF,MAAM0Y,4BAAwD,IAAIjL;IAClE,+EAA+E;IAC/E,MAAMkL,gBAAgB,CAAC,CAAClS,aAAa3H,iBAAiB;IACtD,SAAS8Z,qBAAqB3S,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAMoW,+BAA+BC,IAAAA,qDAAiC,EACpEzS,KACA6Q,YACAwB,2BACAC,eACAC;IAGF,SAASG,qBAAqB9S,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB7C,KAAK;IAE5B;IACA,MAAMuW,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD7S,KACA6Q,YACAwB,2BACAM,mBACAL,eACAI;IAGF,IAAI0H,6BAG8B;IAClC,MAAMC,oBAAoB,CAACpe;QACzB8O,SAASlT,OAAO,KAAK,CAAC;QACtBkT,SAASlT,OAAO,CAACoE,KAAK,GAAGiC,IAAI+F,SAAS,CAAChI;IACzC;IACA,MAAMuS,YAAY,CAACvS,MAAc3B;QAC/B4D,IAAIsQ,SAAS,CAACvS,MAAM3B;QACpB+f,kBAAkBpe;QAClB,OAAOiC;IACT;IACA,MAAM8U,eAAe,CAAC/W,MAAc3B;QAClC,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxBA,MAAM8K,OAAO,CAAC,CAACkV;gBACbpc,IAAI8U,YAAY,CAAC/W,MAAMqe;YACzB;QACF,OAAO;YACLpc,IAAI8U,YAAY,CAAC/W,MAAM3B;QACzB;QACA+f,kBAAkBpe;IACpB;IAEA,MAAMse,kBAAkB,CAAC9X;YAEhBrC;eADPqC,UAAUF,0BAAc,IACxB,SAAOnC,2BAAAA,aAAaoa,UAAU,qBAAvBpa,yBAAyBqa,MAAM,MAAK,WACvCra,aAAaoa,UAAU,CAACC,MAAM,GAC9BhY;;IAEN,IAAIT,iBAAwC;IAE5C,IAAI;QACF,IAAI5B,aAAaC,SAAS,EAAE;YAC1B;;;;;;;;;;;;OAYC,GAED,iEAAiE;YACjE,yEAAyE;YACzE,6EAA6E;YAC7E,8EAA8E;YAC9E,MAAMwW,mCAAmC,IAAIjV;YAE7C,4EAA4E;YAC5E,gFAAgF;YAChF,6EAA6E;YAC7E,MAAMkV,gCAAgC,IAAIlV;YAE1C,kFAAkF;YAClF,yBAAyB;YACzB,MAAME,cAAc,IAAIC,wBAAW;YAEnC,IAAI2Y;YACJ,IAAI/X,wBAAsD;YAC1D,IAAIlB,2BAA4D;YAEhE,IAAIzE,WAAW2F,qBAAqB,EAAE;gBACpC,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,cAAc;gBACd+X,kBAAkB/X,wBAChB3F,WAAW2F,qBAAqB;YACpC,OAAO;gBACL,iEAAiE;gBACjE+X,kBAAkBjZ,2BAChBC,IAAAA,+CAA8B;YAClC;YAEA,MAAMuV,8BAA+CjV,iBAAiB;gBACpEjH,MAAM;gBACNkH,OAAO;gBACPV;gBACAH;gBACAc,cAAc4U,8BAA8B3U,MAAM;gBAClDC,YAAYyU;gBACZ,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvB/U;gBACAO,iBAAiB;gBACjBhB;gBACAiB,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAItB,aAAasB,IAAI;iBAAC;gBAC5BjB;gBACAkB;gBACAC,gBAAgBjL;gBAChBoL,mBAAmBpL;YACrB;YAEA,0FAA0F;YAC1F,wFAAwF;YACxF,MAAMuf,uBAAuB,MAAMhX,kDAAoB,CAACC,GAAG,CACzD8W,6BACA1T,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrB,MAAMub,6BAA6BjX,kDAAoB,CAACC,GAAG,CACzD8W,6BACA9V,aAAaiW,SAAS,EACtBF,sBACAvW,wBAAwBG,aAAa,EACrC;gBACEzJ;gBACAyI,SAAS,CAACF;oBACR,MAAMsG,SAASmR,IAAAA,8CAA0B,EAACzX;oBAE1C,IAAIsG,QAAQ;wBACV,OAAOA;oBACT;oBAEA,IAAIoR,IAAAA,4CAAsB,EAAC1X,MAAM;wBAC/B,kBAAkB;wBAClBoO,QAAQiH,KAAK,CAACrV;wBACd,OAAOjI;oBACT;oBAEA,IAAIkf,iCAAiC1U,MAAM,CAACoV,OAAO,EAAE;wBACnD,mEAAmE;wBACnE,iEAAiE;wBACjE;oBACF,OAAO,IACLjgB,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;wBACAC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;oBAChE;gBACF;gBACA,iFAAiF;gBACjF,qCAAqC;gBACrCC,YAAYjgB;gBACZ,+EAA+E;gBAC/E,iFAAiF;gBACjF,iDAAiD;gBACjDwK,QAAQ2U,8BAA8B3U,MAAM;YAC9C;YAGF,8EAA8E;YAC9Ec,IAAAA,+CAAmB,EAACnB;YACpB,MAAMA,YAAYoB,UAAU;YAE5B4T,8BAA8B3T,KAAK;YACnC0T,iCAAiC1T,KAAK;YAEtC,gEAAgE;YAChE,iEAAiE;YACjE,IAAItG,UAAUkQ,wBAAwB,EAAE;gBACtC,MAAMlQ,UAAUkQ,wBAAwB;YAC1C;YAEA,IAAIgL;YACJ,IAAI;gBACFA,sBAAsB,MAAMC,IAAAA,yDAAgC,EAC1Db;YAEJ,EAAE,OAAOvX,KAAK;gBACZ,IACEkX,8BAA8B3U,MAAM,CAACoV,OAAO,IAC5CV,iCAAiC1U,MAAM,CAACoV,OAAO,EAC/C;gBACA,4EAA4E;gBAC9E,OAAO,IACLjgB,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;gBAChE;YACF;YAEA,IAAII,qBAAqB;gBACvB,MAAME,gCAAgC,IAAIrW;gBAC1C,MAAMsW,mCAAmC,IAAItW;gBAC7C,MAAMuW,8BAA8C;oBAClDpd,MAAM;oBACNkH,OAAO;oBACPV;oBACAH;oBACAc,cAAc+V,8BAA8B9V,MAAM;oBAClDC,YAAY8V;oBACZ,sDAAsD;oBACtD,qDAAqD;oBACrDpW,aAAa;oBACbO,iBAAiB;oBACjBhB;oBACAiB,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAItB,aAAasB,IAAI;qBAAC;oBAC5BjB;oBACAkB;oBACAC,gBAAgBjL;oBAChBoL,mBAAmBpL;gBACrB;gBAEA,MAAMyf,YAAY,AAChB3f,QAAQ,oBACR2f,SAAS;gBACX,MAAMgB,6BAA6BlY,kDAAoB,CAACC,GAAG,CACzDgY,6BACAf,yBACA,qBAAC9Q;oBACCC,mBAAmBwR,oBAAoBM,iBAAiB;oBACxD7R,gBAAgBA;oBAChB7F,yBAAyBA;oBACzB8F,4BAA4BA;oBAC5BC,mBAAmB,CAAC,CAACgK;oBACrB1X,OAAOA;oBAET;oBACEmJ,QAAQ8V,8BAA8B9V,MAAM;oBAC5CrC,SAAS,CAACF;wBACR,MAAMsG,SAASmR,IAAAA,8CAA0B,EAACzX;wBAE1C,IAAIsG,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAIoR,IAAAA,4CAAsB,EAAC1X,MAAM;4BAC/B,kBAAkB;4BAClBoO,QAAQiH,KAAK,CAACrV;4BACd,OAAOjI;wBACT;wBAEA,IAAIsgB,8BAA8B9V,MAAM,CAACoV,OAAO,EAAE;wBAChD,4EAA4E;wBAC9E,OAAO,IACLjgB,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnFC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;wBAChE;oBACF;oBACAnD,kBAAkB;wBAACrC;qBAAgB;gBACrC;gBAGFiG,2BAA2BE,KAAK,CAAC,CAAC1Y;oBAChC,IACEkX,8BAA8B3U,MAAM,CAACoV,OAAO,IAC5CgB,IAAAA,6CAA2B,EAAC3Y,MAC5B;oBACA,4EAA4E;oBAC9E,OAAO,IACLtI,QAAQC,GAAG,CAACigB,gBAAgB,IAC5BlgB,QAAQC,GAAG,CAACkgB,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAAC9X,KAAK/C,UAAU8a,KAAK;oBAChE;gBACF;gBAEA,sEAAsE;gBACtE,uGAAuG;gBACvG1U,IAAAA,+CAAmB,EAACnB;gBACpB,MAAMA,YAAYoB,UAAU;gBAC5B+U,8BAA8B9U,KAAK;YACrC;YAEA,IAAIwX,kBAAkB;YACtB,MAAMnC,wBAAwB,IAAI5W;YAClC,MAAM6W,wBAAwBC,IAAAA,4CAA0B,EACtD/L;YAGF,MAAMgM,4BAA6C3W,iBAAiB;gBAClEjH,MAAM;gBACNkH,OAAO;gBACPV;gBACAH;gBACAc,cAAcsW,sBAAsBrW,MAAM;gBAC1CC,YAAYoW;gBACZ,8EAA8E;gBAC9E1W,aAAa;gBACbO,iBAAiBoW;gBACjBpX;gBACAiB,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAItB,aAAasB,IAAI;iBAAC;gBAC5BjB;gBACAkB;gBACAC,gBAAgBjL;gBAChBoL,mBAAmBpL;YACrB;YAEA,MAAMihB,yBAAyB,MAAM1Y,kDAAoB,CAACC,GAAG,CAC3DwY,2BACApV,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,IAAIgf,qBAAqB;YACzB,MAAM9H,oBAAqBsH,6BACzB,MAAMpC,IAAAA,yDAAgC,EACpCa,IAAAA,2DAAkC,EAChC;gBACE,MAAMC,kBAAkB,MAAM5Y,kDAAoB,CAACC,GAAG,CACpD,qBAAqB;gBACrBwY,2BACA,sBAAsB;gBACtBxX,aAAaiW,SAAS,EACtB,4CAA4C;gBAC5CwB,wBACAjY,wBAAwBG,aAAa,EACrC;oBACEzJ;oBACAyI,SAAS,CAACF;wBACR,OAAO4S,6BAA6B5S;oBACtC;oBACAuC,QAAQqW,sBAAsBrW,MAAM;gBACtC;gBAEFyY,qBAAqB;gBACrB,OAAO9B;YACT,GACA;gBACE,IAAIN,sBAAsBrW,MAAM,CAACoV,OAAO,EAAE;oBACxC,4EAA4E;oBAC5E,6EAA6E;oBAC7EoD,kBAAkB;oBAClB;gBACF;gBAEA,IAAIC,oBAAoB;oBACtB,kFAAkF;oBAClF,iCAAiC;oBACjCD,kBAAkB;gBACpB;gBACAnC,sBAAsBrV,KAAK;YAC7B;YAIN,MAAM4V,wBAAwBL,IAAAA,4CAA0B,EACtD/L;YAEF,MAAMqM,wBAAwB,IAAIpX;YAClC,MAAMqX,4BAA4C;gBAChDle,MAAM;gBACNkH,OAAO;gBACPV;gBACAH;gBACAc,cAAc8W,sBAAsB7W,MAAM;gBAC1CC,YAAY4W;gBACZ,oFAAoF;gBACpFlX,aAAa;gBACbO,iBAAiB0W;gBACjB1X;gBACAiB,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAItB,aAAasB,IAAI;iBAAC;gBAC5BjB;gBACAkB;gBACAC,gBAAgBjL;gBAChBoL,mBAAmBpL;YACrB;YAEA,IAAIkjB,kBAAkB;YACtB,IAAI3B,oBAAoBC,IAAAA,8CAA4B;YAEpD,MAAM/B,YAAY,AAChB3f,QAAQ,oBACR2f,SAAS;YACX,IAAI,EAAEgC,SAASC,kBAAkB,EAAElU,SAAS,EAAE,GAC5C,MAAM0T,IAAAA,2DAAkC,EACtC,IACE3Y,kDAAoB,CAACC,GAAG,CACtB8Y,2BACA7B,yBACA,qBAAC9Q;oBACCC,mBAAmBuM,kBAAkBuF,iBAAiB;oBACtD7R,gBAAgBA;oBAChB7F,yBAAyBA;oBACzB8F,4BAA4BA;oBAC5BC,mBAAmB,CAAC,CAACgK;oBACrB1X,OAAOA;oBAET;oBACEmJ,QAAQ6W,sBAAsB7W,MAAM;oBACpCrC,SAAS,CAACF,KAAc0Z;wBACtB,IACEf,IAAAA,6CAA2B,EAAC3Y,QAC5BoZ,sBAAsB7W,MAAM,CAACoV,OAAO,EACpC;4BACAsD,kBAAkB;4BAElB,MAAMtB,iBAAqC,AACzCD,UACAC,cAAc;4BAChB,IAAI,OAAOA,mBAAmB,UAAU;gCACtCC,IAAAA,2CAAyB,EACvB3c,WACA0c,gBACAL,mBACAH;4BAEJ;4BACA;wBACF;wBAEA,OAAOnG,yBAAyBhT,KAAK0Z;oBACvC;oBACAhF,WAAW,CAACzc;wBACVA,QAAQuN,OAAO,CAAC,CAAC9K,OAAOF;4BACtB4Y,aAAa5Y,KAAKE;wBACpB;oBACF;oBACAia,kBAAkBzD;oBAClB0D,kBAAkB;wBAACrC;qBAAgB;gBACrC,IAEJ;gBACE6G,sBAAsB7V,KAAK;YAC7B;YAGJ,MAAM,EAAEiW,OAAO,EAAEK,cAAc,EAAE,GAC/B,MAAMC,IAAAA,uCAAc,EAACL;YAEvB,0EAA0E;YAC1E,2EAA2E;YAC3E,kCAAkC;YAClC,IAAI,CAAChY,uBAAuB;gBAC1BsY,IAAAA,0CAAwB,EACtB9c,WACA4c,iBAAiBG,8BAAY,CAACC,KAAK,GAAGD,8BAAY,CAACE,IAAI,EACvDZ,mBACAT;YAEJ;YAEA,MAAMzE,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtDvC;gBACAR;gBACAgD,sBAAsBvB;gBACtBlC;gBACAa,iBAAiBA;YACnB;YAEA,MAAMjV,aAAa,MAAMye,IAAAA,oCAAc,EAAChI,kBAAkBiI,QAAQ;YAClEhQ,SAAS1O,UAAU,GAAGA;YACtB0O,SAASiQ,WAAW,GAAG,MAAMC,mBAC3B5e,YACAsc,2BACAxX,cACAnE,YACAjD;YAGF,IAAI4gB,mBAAmBE,iBAAiB;gBACtC,IAAI1V,aAAa,MAAM;oBACrB,oBAAoB;oBACpB4F,SAAS5F,SAAS,GAAG,MAAM+V,IAAAA,4CAA4B,EACrD/V,WACApL,qBACA2gB;gBAEJ,OAAO;oBACL,oBAAoB;oBACpB3P,SAAS5F,SAAS,GAChB,MAAMgW,IAAAA,4CAA4B,EAACT;gBACvC;gBACA5H,kBAAkBuB,OAAO;gBACzB,OAAO;oBACLrH,iBAAiBqF;oBACjBhF,WAAWsF;oBACXvD,QAAQ,MAAMgM,IAAAA,8CAAwB,EAAChC,SAAS;wBAC9CpF;wBACA5C;oBACF;oBACA3E,eAAe4O,IAAAA,sCAAoB,EACjC5C,uBACAM;oBAEF,0CAA0C;oBAC1CpK,qBAAqBgK,0BAA0BrW,UAAU;oBACzDuM,iBAAiB8J,0BAA0BnW,MAAM;oBACjD+L,gBAAgBgM,gBAAgB5B,0BAA0BlW,KAAK;oBAC/D0L,eAAewK,0BAA0BjW,IAAI;oBAC7CC,uBAAuBS,IAAAA,4CAA2B,EAACsX;gBACrD;YACF,OAAO;gBACL,cAAc;gBACd,IAAI7d,UAAU4S,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI6L,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIvH,aAAaqF;gBACjB,IAAIjU,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAM2O,SAAS,AACbrc,QAAQ,oBACRqc,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMyH,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAM3H,qBACzB,qBAACxN;wBACCC,mBAAmBgV;wBACnB/U,gBAAgB,KAAO;wBACvB7F,yBAAyBA;wBACzB8F,4BAA4BA;wBAC5BC,mBAAmB,CAAC,CAACgK;wBACrB1X,OAAOA;wBAET0iB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACzW,aAC1B;wBACEhD,QAAQ0Z,IAAAA,4CAA0B,EAAC;wBACnC/b,SAAS8S;wBACT5Z;oBACF;oBAGF,wGAAwG;oBACxG+a,aAAaJ,IAAAA,kCAAY,EAACyF,SAASqC;gBACrC;gBAEA,OAAO;oBACLzO,iBAAiBqF;oBACjBhF,WAAWsF;oBACXvD,QAAQ,MAAM0M,IAAAA,6CAAuB,EAAC/H,YAAY;wBAChDK,mBAAmBX,IAAAA,kDAA+B,EAChDX,kBAAkBiJ,eAAe,IACjC/iB,OACA4W;wBAEFoE;wBACA5C;wBACAuD,yBACEvY,IAAIS,SAAS,CAAC8X,uBAAuB,KAAK;wBAC5C7V,SAAS1C,IAAIS,SAAS,CAACiC,OAAO;oBAChC;oBACA2N,eAAe4O,IAAAA,sCAAoB,EACjC5C,uBACAM;oBAEF,0CAA0C;oBAC1CpK,qBAAqBgK,0BAA0BrW,UAAU;oBACzDuM,iBAAiB8J,0BAA0BnW,MAAM;oBACjD+L,gBAAgBgM,gBAAgB5B,0BAA0BlW,KAAK;oBAC/D0L,eAAewK,0BAA0BjW,IAAI;oBAC7CC,uBAAuBS,IAAAA,4CAA2B,EAACsX;gBACrD;YACF;QACF,OAAO,IAAIta,aAAa3H,iBAAiB,EAAE;YACzC,uEAAuE;YACvE,IAAI4J,kBAAkBqW,IAAAA,4CAA0B,EAAC/L;YAEjD,MAAMlL,2BAA2BC,IAAAA,+CAA8B;YAC/D,MAAMsa,4BAA6Cha,iBAAiB;gBAClEjH,MAAM;gBACNkH,OAAO;gBACPV;gBACAH;gBACAiB;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAItB,aAAasB,IAAI;iBAAC;gBAC5BjB;YACF;YACA,MAAMxB,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/C6b,2BACAzY,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,MAAMkX,oBAAqBsH,6BACzB,MAAM6B,IAAAA,mEAA0C,EAC9C/b,kDAAoB,CAACC,GAAG,CACtB6b,2BACA7a,aAAaN,sBAAsB,EACnC,4CAA4C;YAC5CZ,YACAU,wBAAwBG,aAAa,EACrC;gBACEzJ;gBACAyI,SAAS0S;YACX;YAIN,MAAM0J,oBAAoC;gBACxCnhB,MAAM;gBACNkH,OAAO;gBACPV;gBACAH;gBACAiB;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAItB,aAAasB,IAAI;iBAAC;gBAC5BjB;YACF;YACA,MAAM2V,YAAY,AAChB3f,QAAQ,oBACR2f,SAAS;YACX,MAAM,EAAEgC,OAAO,EAAEjU,SAAS,EAAE,GAAG,MAAMjF,kDAAoB,CAACC,GAAG,CAC3D+b,mBACA9E,yBACA,qBAAC9Q;gBACCC,mBAAmBuM,kBAAkBuF,iBAAiB;gBACtD7R,gBAAgBA;gBAChB7F,yBAAyBA;gBACzB8F,4BAA4BA;gBAC5BC,mBAAmB,CAAC,CAACgK;gBACrB1X,OAAOA;gBAET;gBACE8G,SAAS8S;gBACT0B,WAAW,CAACzc;oBACVA,QAAQuN,OAAO,CAAC,CAAC9K,OAAOF;wBACtB4Y,aAAa5Y,KAAKE;oBACpB;gBACF;gBACAia,kBAAkBzD;gBAClB0D,kBAAkB;oBAACrC;iBAAgB;YACrC;YAEF,MAAM6B,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtDvC;gBACAR;gBACAgD,sBAAsBvB;gBACtBlC;gBACAa,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMjV,aAAa,MAAMye,IAAAA,oCAAc,EAAChI,kBAAkBiI,QAAQ;YAElE,IAAIZ,+BAA+Btd,YAAY;gBAC7CkO,SAAS1O,UAAU,GAAGA;gBACtB0O,SAASiQ,WAAW,GAAG,MAAMC,mBAC3B5e,YACA6f,mBACA/a,cACAnE,YACAjD;YAEJ;YAEA;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAI2S,IAAAA,qCAAmB,EAACrK,gBAAgB8Z,eAAe,GAAG;gBACxD,IAAIhX,aAAa,MAAM;oBACrB,qBAAqB;oBACrB4F,SAAS5F,SAAS,GAAG,MAAM+V,IAAAA,4CAA4B,EACrD/V,WACApL,qBACA0H;gBAEJ,OAAO;oBACL,qBAAqB;oBACrBsJ,SAAS5F,SAAS,GAAG,MAAMgW,IAAAA,4CAA4B,EACrD1Z;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtDqR,kBAAkBuB,OAAO;gBACzB,OAAO;oBACLrH,iBAAiBqF;oBACjBhF,WAAWsF;oBACXvD,QAAQ,MAAMgM,IAAAA,8CAAwB,EAAChC,SAAS;wBAC9CpF;wBACA5C;oBACF;oBACA3E,eAAepK,gBAAgB8Z,eAAe;oBAC9C,0CAA0C;oBAC1CxN,qBAAqBqN,0BAA0B1Z,UAAU;oBACzDuM,iBAAiBmN,0BAA0BxZ,MAAM;oBACjD+L,gBAAgBgM,gBAAgByB,0BAA0BvZ,KAAK;oBAC/D0L,eAAe6N,0BAA0BtZ,IAAI;gBAC/C;YACF,OAAO,IAAI3I,uBAAuBA,oBAAoBkT,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/BlC,SAAS5F,SAAS,GAAG,MAAMgW,IAAAA,4CAA4B,EACrD1Z;gBAGF,OAAO;oBACLuL,iBAAiBqF;oBACjBhF,WAAWsF;oBACXvD,QAAQ,MAAMgM,IAAAA,8CAAwB,EAAChC,SAAS;wBAC9CpF;wBACA5C;oBACF;oBACA3E,eAAepK,gBAAgB8Z,eAAe;oBAC9C,0CAA0C;oBAC1CxN,qBAAqBqN,0BAA0B1Z,UAAU;oBACzDuM,iBAAiBmN,0BAA0BxZ,MAAM;oBACjD+L,gBAAgBgM,gBAAgByB,0BAA0BvZ,KAAK;oBAC/D0L,eAAe6N,0BAA0BtZ,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAI7F,UAAU4S,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI6L,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIvH,aAAaqF;gBACjB,IAAIjU,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAM2O,SAAS,AACbrc,QAAQ,oBACRqc,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMyH,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAM3H,qBACzB,qBAACxN;wBACCC,mBAAmBgV;wBACnB/U,gBAAgB,KAAO;wBACvB7F,yBAAyBA;wBACzB8F,4BAA4BA;wBAC5BC,mBAAmB,CAAC,CAACgK;wBACrB1X,OAAOA;wBAET0iB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACzW,aAC1B;wBACEhD,QAAQ0Z,IAAAA,4CAA0B,EAAC;wBACnC/b,SAAS8S;wBACT5Z;oBACF;oBAGF,wGAAwG;oBACxG+a,aAAaJ,IAAAA,kCAAY,EAACyF,SAASqC;gBACrC;gBAEA,OAAO;oBACLzO,iBAAiBqF;oBACjBhF,WAAWsF;oBACXvD,QAAQ,MAAM0M,IAAAA,6CAAuB,EAAC/H,YAAY;wBAChDK,mBAAmBX,IAAAA,kDAA+B,EAChDX,kBAAkBiJ,eAAe,IACjC/iB,OACA4W;wBAEFoE;wBACA5C;wBACAuD,yBACEvY,IAAIS,SAAS,CAAC8X,uBAAuB,KAAK;wBAC5C7V,SAAS1C,IAAIS,SAAS,CAACiC,OAAO;oBAChC;oBACA2N,eAAepK,gBAAgB8Z,eAAe;oBAC9C,0CAA0C;oBAC1CxN,qBAAqBqN,0BAA0B1Z,UAAU;oBACzDuM,iBAAiBmN,0BAA0BxZ,MAAM;oBACjD+L,gBAAgBgM,gBAAgByB,0BAA0BvZ,KAAK;oBAC/D0L,eAAe6N,0BAA0BtZ,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAM0Z,uBAAwCpa,iBAAiB;gBAC7DjH,MAAM;gBACNkH,OAAO;gBACPV;gBACAH;gBACAkB,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAItB,aAAasB,IAAI;iBAAC;YAC9B;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAMzC,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/Cic,sBACA7Y,eACAhH,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrB,MAAMkX,oBAAqBsH,6BACzB,MAAM6B,IAAAA,mEAA0C,EAC9C/b,kDAAoB,CAACC,GAAG,CACtBic,sBACAjb,aAAaN,sBAAsB,EACnCZ,YACAU,wBAAwBG,aAAa,EACrC;gBACEzJ;gBACAyI,SAAS0S;YACX;YAIN,MAAM3R,yBAAyB,AAC7BpJ,QAAQ,oBACRoJ,sBAAsB;YACxB,MAAMkT,aAAa,MAAM7T,kDAAoB,CAACC,GAAG,CAC/Cic,sBACAvb,sCACA,qBAACyF;gBACCC,mBAAmBuM,kBAAkBuF,iBAAiB;gBACtD7R,gBAAgBA;gBAChB7F,yBAAyBA;gBACzB8F,4BAA4BA;gBAC5BC,mBAAmB,CAAC,CAACgK;gBACrB1X,OAAOA;gBAET;gBACE8G,SAAS8S;gBACT5Z;gBACAwb,kBAAkB;oBAACrC;iBAAgB;YACrC;YAGF,IAAIgI,+BAA+Btd,YAAY;gBAC7C,MAAMR,aAAa,MAAMye,IAAAA,oCAAc,EAAChI,kBAAkBiI,QAAQ;gBAClEhQ,SAAS1O,UAAU,GAAGA;gBACtB0O,SAASiQ,WAAW,GAAG,MAAMC,mBAC3B5e,YACA+f,sBACAjb,cACAnE,YACAjD;YAEJ;YAEA,MAAMia,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtDvC;gBACAR;gBACAgD,sBAAsBvB;gBACtBlC;gBACAa,iBAAiBA;YACnB;YACA,OAAO;gBACLtE,iBAAiBqF;gBACjBhF,WAAWsF;gBACXvD,QAAQ,MAAMsF,IAAAA,wCAAkB,EAACX,YAAY;oBAC3CK,mBAAmBX,IAAAA,kDAA+B,EAChDX,kBAAkBiJ,eAAe,IACjC/iB,OACA4W;oBAEF5Q,oBAAoB;oBACpB2V,yBACEvY,IAAIS,SAAS,CAAC8X,uBAAuB,KAAK;oBAC5C7V,SAAS1C,IAAIS,SAAS,CAACiC,OAAO;oBAC9BkV;oBACA5C;gBACF;gBACA,0CAA0C;gBAC1CzC,qBAAqByN,qBAAqB9Z,UAAU;gBACpDuM,iBAAiBuN,qBAAqB5Z,MAAM;gBAC5C+L,gBAAgBgM,gBAAgB6B,qBAAqB3Z,KAAK;gBAC1D0L,eAAeiO,qBAAqB1Z,IAAI;YAC1C;QACF;IACF,EAAE,OAAO9C,KAAK;QACZ,IACEiV,IAAAA,gDAAuB,EAACjV,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIoG,OAAO,KAAK,YACvBpG,IAAIoG,OAAO,CAAC7B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMvE;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAIyc,IAAAA,wCAAoB,EAACzc,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMkV,qBAAqBC,IAAAA,iCAAmB,EAACnV;QAC/C,IAAIkV,oBAAoB;YACtB,MAAM1O,QAAQ4O,IAAAA,8CAA2B,EAACpV;YAC1CqV,IAAAA,UAAK,EACH,GAAGrV,IAAIsV,MAAM,CAAC,mDAAmD,EAAEpb,SAAS,kFAAkF,EAAEsM,OAAO;YAGzK,MAAMxG;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIwa,+BAA+B,MAAM;YACvC,MAAMxa;QACR;QAEA,IAAIgE;QAEJ,IAAIuR,IAAAA,6CAAyB,EAACvV,MAAM;YAClC1B,IAAItC,UAAU,GAAGwZ,IAAAA,+CAA2B,EAACxV;YAC7CmL,SAASnP,UAAU,GAAGsC,IAAItC,UAAU;YACpCgI,YAAYyR,IAAAA,sDAAkC,EAACnX,IAAItC,UAAU;QAC/D,OAAO,IAAI0Z,IAAAA,8BAAe,EAAC1V,MAAM;YAC/BgE,YAAY;YACZ1F,IAAItC,UAAU,GAAG2Z,IAAAA,wCAA8B,EAAC3V;YAChDmL,SAASnP,UAAU,GAAGsC,IAAItC,UAAU;YAEpC,MAAM4Z,cAAcC,IAAAA,4BAAa,EAACC,IAAAA,iCAAuB,EAAC9V,MAAM6Q;YAEhEjC,UAAU,YAAYgH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B5W,IAAItC,UAAU,GAAG;YACjBmP,SAASnP,UAAU,GAAGsC,IAAItC,UAAU;QACtC;QAEA,MAAM,CAACka,qBAAqBC,qBAAqB,GAAG3D,IAAAA,mCAAkB,EACpEzB,eACA7L,aACA8L,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC5V,KAAK,QACzBpD,OACA;QAGF,MAAMojB,uBAAwCpa,iBAAiB;YAC7DjH,MAAM;YACNkH,OAAO;YACPV;YACAH,cAAcA;YACdkB,YACE,QAAON,kCAAAA,eAAgBM,UAAU,MAAK,cAClCN,eAAeM,UAAU,GACzBC,0BAAc;YACpBC,QACE,QAAOR,kCAAAA,eAAgBQ,MAAM,MAAK,cAC9BR,eAAeQ,MAAM,GACrBD,0BAAc;YACpBE,OACE,QAAOT,kCAAAA,eAAgBS,KAAK,MAAK,cAC7BT,eAAeS,KAAK,GACpBF,0BAAc;YACpBG,MAAM;mBAAKV,CAAAA,kCAAAA,eAAgBU,IAAI,KAAItB,aAAasB,IAAI;aAAE;QACxD;QACA,MAAMsT,kBAAkB,MAAM9V,kDAAoB,CAACC,GAAG,CACpDic,sBACA9W,oBACA/I,MACAH,KACAiW,0BAA0B9X,GAAG,CAAC,AAACqF,IAAYsG,MAAM,IAAIvO,YAAYiI,KACjEgE;QAGF,MAAMqS,oBAAoB/V,kDAAoB,CAACC,GAAG,CAChDic,sBACAjb,aAAaN,sBAAsB,EACnCmV,iBACArV,wBAAwBG,aAAa,EACrC;YACEzJ;YACAyI,SAAS0S;QACX;QAGF,IAAI;YACF,6EAA6E;YAC7E,wFAAwF;YACxF,uCAAuC;YACvC,MAAM0D,aAAa,MAAMhW,kDAAoB,CAACC,GAAG,CAC/Cic,sBACAjG,+CAAyB,EACzB;gBACEC,gBACE3e,QAAQ;gBACV4e,uBACE,qBAACvO;oBACCvB,mBAAmB0P;oBACnBxP,4BAA4BA;oBAC5BD,gBAAgBsP;oBAChBnV,yBAAyBA;oBACzB+F,mBAAmB,CAAC,CAACgK;oBACrB1X,OAAOA;;gBAGXsd,eAAe;oBACbtd;oBACA,wCAAwC;oBACxCwb,kBAAkB;wBAACuB;qBAAqB;oBACxCnG;gBACF;YACF;YAGF,IAAIuK,+BAA+Btd,YAAY;gBAC7C,MAAMR,aAAa,MAAMye,IAAAA,oCAAc,EACrCV,2BAA2BW,QAAQ;gBAErChQ,SAAS1O,UAAU,GAAGA;gBACtB0O,SAASiQ,WAAW,GAAG,MAAMC,mBAC3B5e,YACA+f,sBACAjb,cACAnE,YACAjD;YAEJ;YAEA,oEAAoE;YACpE,gEAAgE;YAChE,MAAMuiB,eACJlC,sCAAsCmC,oDAA2B,GAC7DnC,2BAA2BW,QAAQ,KACnCX,2BAA2B2B,eAAe;YAEhD,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9B/O,iBAAiBqF;gBACjBhF,WAAWsF;gBACXvD,QAAQ,MAAMsF,IAAAA,wCAAkB,EAACwB,YAAY;oBAC3C9B,mBAAmBX,IAAAA,kDAA+B,EAChD6I,cACAtjB,OACA4W;oBAEF5Q,oBAAoB;oBACpB2V,yBACEvY,IAAIS,SAAS,CAAC8X,uBAAuB,KAAK;oBAC5C7V,SAAS1C,IAAIS,SAAS,CAACiC,OAAO;oBAC9BkV,uBAAuBC,IAAAA,oDAAyB,EAAC;wBAC/CvC;wBACAR;wBACAgD,sBAAsB,EAAE;wBACxBzD;wBACAa,iBAAiBA;oBACnB;oBACAF;oBACAwD,oBAAoB5U;gBACtB;gBACAyM,eAAe;gBACfkC,qBACE3M,mBAAmB,OAAOA,eAAeM,UAAU,GAAGC,0BAAc;gBACtEsM,iBACE7M,mBAAmB,OAAOA,eAAeQ,MAAM,GAAGD,0BAAc;gBAClEgM,gBAAgBgM,gBACdvY,mBAAmB,OAAOA,eAAeS,KAAK,GAAGF,0BAAc;gBAEjE4L,eAAenM,mBAAmB,OAAOA,eAAeU,IAAI,GAAG;YACjE;QACF,EAAE,OAAO6T,UAAe;YACtB,IACEjf,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB2d,IAAAA,6CAAyB,EAACoB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1B/e,QAAQ;gBACV+e;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAM9R,uBAAuB,OAC3BlI,MACAH;IAKA,MAAM,EACJogB,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGC,IAAAA,gCAAe,EAACngB;IAEpB,MAAMogB,uBACJvgB,IAAIE,YAAY,CAACgI,WAAW;IAC9B,IAAIE;IACJ,IAAIiY,mBAAmB;QACrB,MAAM,GAAGlY,OAAO,GAAG,MAAMqY,IAAAA,gEAA+B,EAAC;YACvDxgB;YACAygB,UAAUJ,iBAAiB,CAAC,EAAE;YAC9BK,cAAcL,iBAAiB,CAAC,EAAE;YAClCte,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAoG,oBAAoBD;IACtB;IACA,IAAInI,IAAIY,UAAU,CAACgD,GAAG,EAAE;QACtB,MAAM+c,MACJzlB,QAAQC,GAAG,CAACsS,YAAY,KAAK,SACzBvS,QAAQC,GAAG,CAACylB,uBAAuB,GACnC5gB,IAAIY,UAAU,CAAC+f,GAAG,IAAI;QAE5B,MAAME,wBAAwBC,IAAAA,gDAA2B,EACvDH,KACAN,qCAAAA,iBAAmB,CAAC,EAAE;QAExB,IAAIrgB,IAAIY,UAAU,CAACmgB,sBAAsB,IAAIF,uBAAuB;YAClE,MAAMG,kBAAkBhhB,IAAIE,YAAY,CAAC8gB,eAAe;YACxD5Y,oBACE,2EAA2E;YAC3E,iEAAiE;0BACjE,qBAAC4Y;gBAECriB,MAAK;gBACLjB,UAAUmjB;0BAETzY;eAJG;QAOV;IACF;IAEA,OAAO;QACLF,aAAaqY;QACbpY,QAAQC;IACV;AACF;AAEA,eAAeyW,mBACboC,kBAA0B,EAC1Brb,cAA8B,EAC9Bb,YAA2B,EAC3BnE,UAAsB,EACtBjD,mBAA+C;IAE/C,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAM4G,0BAA0B3D,WAAW2D,uBAAuB;IAClE,IACE,CAACA,2BACD,yEAAyE;IACzE,mBAAmB;IACnB,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,mCAAmC;IACnC3D,WAAWoD,YAAY,CAACkd,kBAAkB,KAAK,MAC/C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgBjmB,QAAQC,GAAG,CAACsS,YAAY,KAAK;IACnD,MAAM2T,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWH,gBACP5c,wBAAwBgd,oBAAoB,GAC5Chd,wBAAwBid,gBAAgB;QAC5C3S,iBAAiB4S,IAAAA,mCAAkB;IACrC;IAEA,MAAMC,YAAY9b,eAAeS,KAAK;IACtC,OAAO,MAAMtB,aAAa8Z,kBAAkB,CAC1CoC,oBACAS,WACAnd,wBAAwBG,aAAa,EACrC0c,wBACAzjB;AAEJ", "ignoreList": [0]}