"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[661],{239:(e,t,n)=>{n.d(t,{bL:()=>b,zi:()=>E});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(5845),u=n(1275),s=n(3655),c=n(5155),d="Switch",[f,p]=(0,l.A)(d),[h,v]=f(d),m=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:l,checked:u,defaultChecked:f,required:p,disabled:v,value:m="on",onCheckedChange:g,form:y,...b}=e,[E,C]=r.useState(null),R=(0,i.s)(t,e=>C(e)),k=r.useRef(!1),A=!E||y||!!E.closest("form"),[M,T]=(0,a.i)({prop:u,defaultProp:null!=f&&f,onChange:g,caller:d});return(0,c.jsxs)(h,{scope:n,checked:M,disabled:v,children:[(0,c.jsx)(s.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":x(M),"data-disabled":v?"":void 0,disabled:v,value:m,...b,ref:R,onClick:(0,o.m)(e.onClick,e=>{T(e=>!e),A&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),A&&(0,c.jsx)(w,{control:E,bubbles:!k.current,name:l,value:m,checked:M,required:p,disabled:v,form:y,style:{transform:"translateX(-100%)"}})]})});m.displayName=d;var g="SwitchThumb",y=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,o=v(g,n);return(0,c.jsx)(s.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});y.displayName=g;var w=r.forwardRef((e,t)=>{let{__scopeSwitch:n,control:o,checked:l,bubbles:a=!0,...s}=e,d=r.useRef(null),f=(0,i.s)(d,t),p=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l),h=(0,u.X)(o);return r.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==l&&t){let n=new Event("click",{bubbles:a});t.call(e,l),e.dispatchEvent(n)}},[p,l,a]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...s,tabIndex:-1,ref:f,style:{...s.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var b=m,E=y},381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},547:(e,t,n)=>{n.d(t,{UC:()=>V,ZL:()=>U,bL:()=>G,i3:()=>X,l9:()=>K});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(9178),u=n(2293),s=n(7900),c=n(1285),d=n(5152),f=n(4378),p=n(8905),h=n(3655),v=n(9708),m=n(5845),g=n(8168),y=n(3795),w=n(5155),x="Popover",[b,E]=(0,l.A)(x,[d.Bk]),C=(0,d.Bk)(),[R,k]=b(x),A=e=>{let{__scopePopover:t,children:n,open:o,defaultOpen:i,onOpenChange:l,modal:a=!1}=e,u=C(t),s=r.useRef(null),[f,p]=r.useState(!1),[h,v]=(0,m.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:x});return(0,w.jsx)(d.bL,{...u,children:(0,w.jsx)(R,{scope:t,contentId:(0,c.B)(),triggerRef:s,open:h,onOpenChange:v,onOpenToggle:r.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:f,onCustomAnchorAdd:r.useCallback(()=>p(!0),[]),onCustomAnchorRemove:r.useCallback(()=>p(!1),[]),modal:a,children:n})})};A.displayName=x;var M="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...o}=e,i=k(M,n),l=C(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:u}=i;return r.useEffect(()=>(a(),()=>u()),[a,u]),(0,w.jsx)(d.Mz,{...l,...o,ref:t})}).displayName=M;var T="PopoverTrigger",S=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,l=k(T,n),a=C(n),u=(0,i.s)(t,l.triggerRef),s=(0,w.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...r,ref:u,onClick:(0,o.m)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?s:(0,w.jsx)(d.Mz,{asChild:!0,...a,children:s})});S.displayName=T;var L="PopoverPortal",[j,P]=b(L,{forceMount:void 0}),N=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,i=k(L,t);return(0,w.jsx)(j,{scope:t,forceMount:n,children:(0,w.jsx)(p.C,{present:n||i.open,children:(0,w.jsx)(f.Z,{asChild:!0,container:o,children:r})})})};N.displayName=L;var O="PopoverContent",D=r.forwardRef((e,t)=>{let n=P(O,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,i=k(O,e.__scopePopover);return(0,w.jsx)(p.C,{present:r||i.open,children:i.modal?(0,w.jsx)(_,{...o,ref:t}):(0,w.jsx)(F,{...o,ref:t})})});D.displayName=O;var I=(0,v.TL)("PopoverContent.RemoveScroll"),_=r.forwardRef((e,t)=>{let n=k(O,e.__scopePopover),l=r.useRef(null),a=(0,i.s)(t,l),u=r.useRef(!1);return r.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,w.jsx)(y.A,{as:I,allowPinchZoom:!0,children:(0,w.jsx)(W,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),u.current||null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;u.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),F=r.forwardRef((e,t)=>{let n=k(O,e.__scopePopover),o=r.useRef(!1),i=r.useRef(!1);return(0,w.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),W=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:h,...v}=e,m=k(O,n),g=C(n);return(0,u.Oh)(),(0,w.jsx)(s.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,w.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:h,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onDismiss:()=>m.onOpenChange(!1),children:(0,w.jsx)(d.UC,{"data-state":H(m.open),role:"dialog",id:m.contentId,...g,...v,ref:t,style:{...v.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),B="PopoverClose";r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=k(B,n);return(0,w.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})}).displayName=B;var z=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=C(n);return(0,w.jsx)(d.i3,{...o,...r,ref:t})});function H(e){return e?"open":"closed"}z.displayName="PopoverArrow";var G=A,K=S,U=N,V=D,X=z},968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(2115),o=n(3655),i=n(5155),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},1007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2115),o=n(2712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},1284:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},1788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("message-circle-more",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}],["path",{d:"M8 12h.01",key:"czm47f"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 12h.01",key:"1l6xoz"}]])},2085:(e,t,n)=>{n.d(t,{F:()=>l});var r=n(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:a}=t,u=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(r);return l[e][i]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...s}[t]):({...a,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2098:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:l()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3109:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3509:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),i=n(9708),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{n.d(t,{A:()=>V});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=i({async:!0,ssr:!1},e),o}(),v=function(){},m=a.forwardRef(function(e,t){var n,r,o,u,s=a.useRef(null),p=a.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=p[0],g=p[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,E=e.enabled,C=e.shards,R=e.sideCar,k=e.noRelative,A=e.noIsolation,M=e.inert,T=e.allowPinchZoom,S=e.as,L=e.gapMode,j=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[s,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,n)},[n]),u),N=i(i({},j),m);return a.createElement(a.Fragment,null,E&&a.createElement(R,{sideCar:h,removeScrollBar:b,shards:C,noRelative:k,noIsolation:A,inert:M,setCallbacks:g,allowPinchZoom:!!T,lockRef:s,gapMode:L}),y?a.cloneElement(a.Children.only(w),i(i({},N),{ref:P})):a.createElement(void 0===S?"div":S,i({},N,{className:x,ref:P}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=x(),A="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},S=function(){a.useEffect(function(){return document.body.setAttribute(A,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;S();var i=a.useMemo(function(){return R(o)},[o]);return a.createElement(k,{styles:M(i,!t,o,n?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){j=!1}var N=!!j&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=_(e,u),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&I(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},z=function(e){return e&&"current"in e?e.current:e},H=0,G=[];let K=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(H++)[0],i=a.useState(x)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(z),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=W(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=D(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(G.length&&G[G.length-1]===i){var n="deltaY"in e?B(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(z).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return G.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,N),document.addEventListener("touchmove",s,N),document.addEventListener("touchstart",d,N),function(){G=G.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,N),document.removeEventListener("touchmove",s,N),document.removeEventListener("touchstart",d,N)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var U=a.forwardRef(function(e,t){return a.createElement(m,i({},e,{ref:t,sideCar:K}))});U.classNames=m.classNames;let V=U},3861:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},3904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},3921:(e,t,n)=>{n.d(t,{i3:()=>Y,UC:()=>$,ZL:()=>q,Kq:()=>U,bL:()=>V,l9:()=>X});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(9178),u=n(1285),s=n(5152),c=n(4378),d=n(8905),f=n(3655),p=n(9708),h=n(5845),v=n(5155),m=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),g=r.forwardRef((e,t)=>(0,v.jsx)(f.sG.span,{...e,ref:t,style:{...m,...e.style}}));g.displayName="VisuallyHidden";var[y,w]=(0,l.A)("Tooltip",[s.Bk]),x=(0,s.Bk)(),b="TooltipProvider",E="tooltip.open",[C,R]=y(b),k=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=r.useRef(!0),u=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};k.displayName=b;var A="Tooltip",[M,T]=y(A),S=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=R(A,e.__scopeTooltip),f=x(t),[p,m]=r.useState(null),g=(0,u.B)(),y=r.useRef(0),w=null!=a?a:d.disableHoverableContent,b=null!=c?c:d.delayDuration,C=r.useRef(!1),[k,T]=(0,h.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(E))):d.onClose(),null==l||l(e)},caller:A}),S=r.useMemo(()=>k?C.current?"delayed-open":"instant-open":"closed",[k]),L=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,C.current=!1,T(!0)},[T]),j=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,T(!1)},[T]),P=r.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{C.current=!0,T(!0),y.current=0},b)},[b,T]);return r.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,v.jsx)(s.bL,{...f,children:(0,v.jsx)(M,{scope:t,contentId:g,open:k,stateAttribute:S,trigger:p,onTriggerChange:m,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?P():L()},[d.isOpenDelayedRef,P,L]),onTriggerLeave:r.useCallback(()=>{w?j():(window.clearTimeout(y.current),y.current=0)},[j,w]),onOpen:L,onClose:j,disableHoverableContent:w,children:n})})};S.displayName=A;var L="TooltipTrigger",j=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=T(L,n),u=R(L,n),c=x(n),d=r.useRef(null),p=(0,i.s)(t,d,a.onTriggerChange),h=r.useRef(!1),m=r.useRef(!1),g=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,v.jsx)(s.Mz,{asChild:!0,...c,children:(0,v.jsx)(f.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(m.current||u.isPointerInTransitRef.current||(a.onTriggerEnter(),m.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),m.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});j.displayName=L;var P="TooltipPortal",[N,O]=y(P,{forceMount:void 0}),D=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=T(P,t);return(0,v.jsx)(N,{scope:t,forceMount:n,children:(0,v.jsx)(d.C,{present:n||i.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:o,children:r})})})};D.displayName=P;var I="TooltipContent",_=r.forwardRef((e,t)=>{let n=O(I,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=T(I,e.__scopeTooltip);return(0,v.jsx)(d.C,{present:r||l.open,children:l.disableHoverableContent?(0,v.jsx)(H,{side:o,...i,ref:t}):(0,v.jsx)(F,{side:o,...i,ref:t})})}),F=r.forwardRef((e,t)=>{let n=T(I,e.__scopeTooltip),o=R(I,e.__scopeTooltip),l=r.useRef(null),a=(0,i.s)(t,l),[u,s]=r.useState(null),{trigger:c,onClose:d}=n,f=l.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{s(null),p(!1)},[p]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&f){let e=e=>m(e,f),t=e=>m(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,m,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,d=a.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}(n,u);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,d,h]),(0,v.jsx)(H,{...e,ref:a})}),[W,B]=y(A,{isInside:!1}),z=(0,p.Dc)("TooltipContent"),H=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:u,...c}=e,d=T(I,n),f=x(n),{onClose:p}=d;return r.useEffect(()=>(document.addEventListener(E,p),()=>document.removeEventListener(E,p)),[p]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(s.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(z,{children:o}),(0,v.jsx)(W,{scope:n,isInside:!0,children:(0,v.jsx)(g,{id:d.contentId,role:"tooltip",children:i||o})})]})})});_.displayName=I;var G="TooltipArrow",K=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=x(n);return B(G,n).isInside?null:(0,v.jsx)(s.i3,{...o,...r,ref:t})});K.displayName=G;var U=k,V=S,X=j,q=D,$=_,Y=K},4011:(e,t,n)=>{n.d(t,{H4:()=>R,_V:()=>C,bL:()=>E});var r=n(2115),o=n(6081),i=n(9033),l=n(2712),a=n(3655),u=n(1414);function s(){return()=>{}}var c=n(5155),d="Avatar",[f,p]=(0,o.A)(d),[h,v]=f(d),m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,l]=r.useState("idle");return(0,c.jsx)(h,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:l,children:(0,c.jsx)(a.sG.span,{...o,ref:t})})});m.displayName=d;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=v(g,n),h=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,i=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),a=r.useRef(null),c=i?(a.current||(a.current=new window.Image),a.current):null,[d,f]=r.useState(()=>b(c,e));return(0,l.N)(()=>{f(b(c,e))},[c,e]),(0,l.N)(()=>{let e=e=>()=>{f(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),n&&(c.referrerPolicy=n),"string"==typeof o&&(c.crossOrigin=o),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,o,n]),d}(o,f),m=(0,i.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==h&&m(h)},[h,m]),"loaded"===h?(0,c.jsx)(a.sG.img,{...f,ref:t,src:o}):null});y.displayName=g;var w="AvatarFallback",x=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,l=v(w,n),[u,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==l.imageLoadingStatus?(0,c.jsx)(a.sG.span,{...i,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var E=m,C=y,R=x},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),i=n(3655),l=n(2712),a=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:s,...c}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let p=s||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...c,ref:t}),p):null});u.displayName="Portal"},4835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5040:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5152:(e,t,n)=>{n.d(t,{Mz:()=>e1,i3:()=>e5,UC:()=>e2,bL:()=>e0,Bk:()=>eF});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let x=["left","right"],b=["right","left"],E=["top","bottom"],C=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function k(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function A(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function M(e,t,n){let r,{reference:o,floating:i}=e,l=y(t),a=v(y(t)),u=m(a),s=p(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=g*(n&&c?-1:1);break;case"end":r[a]+=g*(n&&c?-1:1)}return r}let T=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=M(s,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=M(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function S(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),v=k(h),m=a[p?"floating"===d?"reference":"floating":d],g=A(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),x=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=A(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-b.top+v.top)/x.y,bottom:(b.bottom-g.bottom+v.bottom)/x.y,left:(g.left-b.left+v.left)/x.x,right:(b.right-g.right+v.right)/x.x}}function L(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function j(e){return o.some(t=>e[t]>=0)}let P=new Set(["left","top"]);async function N(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),u="y"===y(n),s=P.has(l)?-1:1,c=i&&u?-1:1,d=f(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof g&&(m="end"===a?-1*g:g),u?{x:m*c,y:v*s}:{x:v*s,y:m*c}}function O(){return"undefined"!=typeof window}function D(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function _(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!O()&&(e instanceof Node||e instanceof I(e).Node)}function W(e){return!!O()&&(e instanceof Element||e instanceof I(e).Element)}function B(e){return!!O()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function z(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let H=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!H.has(o)}let K=new Set(["table","td","th"]),U=[":popover-open",":modal"];function V(e){return U.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let X=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],$=["paint","layout","strict","content"];function Y(e){let t=Z(),n=W(e)?ee(e):e;return X.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||q.some(e=>(n.willChange||"").includes(e))||$.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(D(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===D(e))return e;let t=e.assignedSlot||e.parentNode||z(e)&&e.host||_(e);return z(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:B(n)&&G(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=I(o);if(i){let e=eo(l);return t.concat(l,l.visualViewport||[],G(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=B(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function el(e){return W(e)?e:e.contextElement}function ea(e){let t=el(e);if(!B(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let eu=s(0);function es(e){let t=I(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function ec(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=el(e),a=s(1);t&&(r?W(r)&&(a=ea(r)):a=ea(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===I(l))&&o)?es(l):s(0),c=(i.left+u.x)/a.x,d=(i.top+u.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=I(l),t=r&&W(r)?I(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=ea(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=l,o=eo(n=I(o))}}return A({width:f,height:p,x:c,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:ec(_(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=I(e),r=_(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=Z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=_(e),n=et(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ed(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(_(e));else if(W(t))r=function(e,t){let n=ec(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=B(e)?ea(e):s(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=es(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return A(r)}function ev(e){return"static"===ee(e).position}function em(e,t){if(!B(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return _(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=I(e);if(V(e))return r;if(!B(e)){let t=en(e);for(;t&&!Q(t);){if(W(t)&&!ev(t))return t;t=en(t)}return r}let o=em(e,t);for(;o&&(n=o,K.has(D(n)))&&ev(o);)o=em(o,t);return o&&Q(o)&&ev(o)&&!Y(o)?r:o||function(e){let t=en(e);for(;B(t)&&!Q(t);){if(Y(t))return t;if(V(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=B(t),o=_(t),i="fixed"===n,l=ec(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!i)if(("body"!==D(t)||G(o))&&(a=et(t)),r){let e=ec(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o));i&&!r&&o&&(u.x=ed(o));let c=!o||r||i?s(0):ef(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=_(r),a=!!t&&V(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=B(r);if((f||!f&&!i)&&(("body"!==D(r)||G(l))&&(u=et(r)),B(r))){let e=ec(r);c=ea(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?s(0):ef(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:_,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?V(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>W(e)&&"body"!==D(e)),o=null,i="fixed"===ee(e).position,l=i?en(e):e;for(;W(l)&&!Q(l);){let t=ee(l),n=Y(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||G(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!W(r)||Q(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],s=a.reduce((e,n)=>{let r=eh(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},eh(t,u,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:ea,isElement:W,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:s,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=k(p),w={x:n,y:r},x=v(y(o)),b=m(x),E=await u.getDimensions(d),C="y"===x,R=C?"clientHeight":"clientWidth",A=a.reference[b]+a.reference[x]-w[x]-a.floating[b],M=w[x]-a.reference[x],T=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),S=T?T[R]:0;S&&await (null==u.isElement?void 0:u.isElement(T))||(S=s.floating[R]||a.floating[b]);let L=S/2-E[b]/2-1,j=i(g[C?"top":"left"],L),P=i(g[C?"bottom":"right"],L),N=S-E[b]-P,O=S/2-E[b]/2+(A/2-M/2),D=l(j,i(O,N)),I=!c.arrow&&null!=h(o)&&O!==D&&a.reference[b]/2-(O<j?j:P)-E[b]/2<0,_=I?O<j?O-j:O-N:0;return{[x]:w[x]+_,data:{[x]:D,centerOffset:O-D-_,...I&&{alignmentOffset:_}},reset:I}}});var eE=n(7650),eC="undefined"!=typeof document?r.useLayoutEffect:function(){};function eR(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eR(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eR(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ek(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eA(e,t){let n=ek(e);return Math.round(t*n)/n}function eM(e){let t=r.useRef(e);return eC(()=>{t.current=e}),t}var eT=n(3655),eS=n(5155),eL=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eS.jsx)(eT.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eS.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eL.displayName="Arrow";var ej=n(6101),eP=n(6081),eN=n(9033),eO=n(2712),eD=n(1275),eI="Popper",[e_,eF]=(0,eP.A)(eI),[eW,eB]=e_(eI),ez=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eS.jsx)(eW,{scope:t,anchor:o,onAnchorChange:i,children:n})};ez.displayName=eI;var eH="PopperAnchor",eG=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eB(eH,n),a=r.useRef(null),u=(0,ej.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eS.jsx)(eT.sG.div,{...i,ref:u})});eG.displayName=eH;var eK="PopperContent",[eU,eV]=e_(eK),eX=r.forwardRef((e,t)=>{var n,o,a,s,c,d,g,k;let{__scopePopper:A,side:M="bottom",sideOffset:O=0,align:D="center",alignOffset:I=0,arrowPadding:F=0,avoidCollisions:W=!0,collisionBoundary:B=[],collisionPadding:z=0,sticky:H="partial",hideWhenDetached:G=!1,updatePositionStrategy:K="optimized",onPlaced:U,...V}=e,X=eB(eK,A),[q,$]=r.useState(null),Y=(0,ej.s)(t,e=>$(e)),[Z,J]=r.useState(null),Q=(0,eD.X)(Z),ee=null!=(g=null==Q?void 0:Q.width)?g:0,et=null!=(k=null==Q?void 0:Q.height)?k:0,en="number"==typeof z?z:{top:0,right:0,bottom:0,left:0,...z},eo=Array.isArray(B)?B:[B],ei=eo.length>0,ea={padding:en,boundary:eo.filter(eZ),altBoundary:ei},{refs:eu,floatingStyles:es,placement:ed,isPositioned:ef,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eR(p,o)||h(o);let[v,m]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,m(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=l||v,E=a||g,C=r.useRef(null),R=r.useRef(null),k=r.useRef(d),A=null!=s,M=eM(s),S=eM(i),L=eM(c),j=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};S.current&&(e.platform=S.current),((e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return T(e,t,{...o,platform:i})})(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};P.current&&!eR(k.current,t)&&(k.current=t,eE.flushSync(()=>{f(t)}))})},[p,t,n,S,L]);eC(()=>{!1===c&&k.current.isPositioned&&(k.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let P=r.useRef(!1);eC(()=>(P.current=!0,()=>{P.current=!1}),[]),eC(()=>{if(b&&(C.current=b),E&&(R.current=E),b&&E){if(M.current)return M.current(b,E,j);j()}},[b,E,j,M,A]);let N=r.useMemo(()=>({reference:C,floating:R,setReference:w,setFloating:x}),[w,x]),O=r.useMemo(()=>({reference:b,floating:E}),[b,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eA(O.floating,d.x),r=eA(O.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ek(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,O.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:j,refs:N,elements:O,floatingStyles:D}),[d,j,N,O,D])}({strategy:"fixed",placement:M+("center"!==D?"-"+D:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=el(e),h=a||s?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,r=null,o=_(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=f;if(c||t(),!v||!m)return;let g=u(h),y=u(o.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+m))+"px "+-u(p)+"px",threshold:l(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ex(f,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,m=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?ec(e):null;return f&&function t(){let r=ec(e);y&&!ex(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===K})},elements:{reference:X.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await N(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:O+et,alignmentAxis:I}),W&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await S(t,c),m=y(p(o)),g=v(m),w=d[g],x=d[m];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,i(w,r))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,i(x,r))}let b=s.fn({...t,[g]:w,[m]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:a,[m]:u}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===H?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=f(e,t),c={x:n,y:r},d=y(o),h=v(d),m=c[h],g=c[d],w=f(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;m<t?m=t:m>n&&(m=n)}if(s){var b,E;let e="y"===h?"width":"height",t=P.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:m,[d]:g}}}}(e),options:[e,t]}))():void 0,...ea}),W&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:g}=t,{mainAxis:k=!0,crossAxis:A=!0,fallbackPlacements:M,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:j=!0,...P}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let N=p(a),O=y(c),D=p(c)===c,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),_=M||(D||!j?[R(c)]:function(e){let t=R(e);return[w(e),t,w(t)]}(c)),F="none"!==L;!M&&F&&_.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?E:C;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(c,j,L,I));let W=[c,..._],B=await S(t,P),z=[],H=(null==(r=u.flip)?void 0:r.overflows)||[];if(k&&z.push(B[N]),A){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(y(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=R(l)),[l,R(l)]}(a,s,I);z.push(B[e[0]],B[e[1]])}if(H=[...H,{placement:a,overflows:z}],!z.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=W[e];if(t&&("alignment"!==A||O===y(t)||H.every(e=>e.overflows[0]>0&&y(e.placement)===O)))return{data:{index:e,overflows:H},reset:{placement:t}};let n=null==(i=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(T){case"bestFit":{let e=null==(l=H.filter(e=>{if(F){let t=y(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ea}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:u,rects:s,platform:c,elements:d}=t,{apply:v=()=>{},...m}=f(e,t),g=await S(t,m),w=p(u),x=h(u),b="y"===y(u),{width:E,height:C}=s.floating;"top"===w||"bottom"===w?(o=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=w,o="end"===x?"top":"bottom");let R=C-g.top-g.bottom,k=E-g.left-g.right,A=i(C-g[o],R),M=i(E-g[a],k),T=!t.middlewareData.shift,L=A,j=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(L=R),T&&!x){let e=l(g.left,0),t=l(g.right,0),n=l(g.top,0),r=l(g.bottom,0);b?j=E-2*(0!==e||0!==t?e+t:l(g.left,g.right)):L=C-2*(0!==n||0!==r?n+r:l(g.top,g.bottom))}await v({...t,availableWidth:j,availableHeight:L});let P=await c.getDimensions(d.floating);return E!==P.width||C!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ea,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),Z&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:Z,padding:F}),eJ({arrowWidth:ee,arrowHeight:et}),G&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=L(await S(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:j(e)}}}case"escaped":{let e=L(await S(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:j(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ea})]}),[eh,ev]=eQ(ed),em=(0,eN.c)(U);(0,eO.N)(()=>{ef&&(null==em||em())},[ef,em]);let eg=null==(n=ep.arrow)?void 0:n.x,ey=null==(o=ep.arrow)?void 0:o.y,eL=(null==(a=ep.arrow)?void 0:a.centerOffset)!==0,[eP,eI]=r.useState();return(0,eO.N)(()=>{q&&eI(window.getComputedStyle(q).zIndex)},[q]),(0,eS.jsx)("div",{ref:eu.setFloating,"data-radix-popper-content-wrapper":"",style:{...es,transform:ef?es.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eP,"--radix-popper-transform-origin":[null==(s=ep.transformOrigin)?void 0:s.x,null==(c=ep.transformOrigin)?void 0:c.y].join(" "),...(null==(d=ep.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eS.jsx)(eU,{scope:A,placedSide:eh,onArrowChange:J,arrowX:eg,arrowY:ey,shouldHideArrow:eL,children:(0,eS.jsx)(eT.sG.div,{"data-side":eh,"data-align":ev,...V,ref:Y,style:{...V.style,animation:ef?void 0:"none"}})})})});eX.displayName=eK;var eq="PopperArrow",e$={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eV(eq,n),i=e$[o.placedSide];return(0,eS.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eS.jsx)(eL,{...r,ref:t,style:{...r.style,display:"block"}})})});function eZ(e){return null!==e}eY.displayName=eq;var eJ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=eQ(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=c?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=c?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=c?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function eQ(e){let[t,n="center"]=e.split("-");return[t,n]}var e0=ez,e1=eG,e2=eX,e5=eY},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5560:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("life-buoy",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.93 4.93 4.24 4.24",key:"1ymg45"}],["path",{d:"m14.83 9.17 4.24-4.24",key:"1cb5xl"}],["path",{d:"m14.83 14.83 4.24 4.24",key:"q42g0n"}],["path",{d:"m9.17 14.83-4.24 4.24",key:"bqpfvv"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[s,e,a,u])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(2115),o=n(5155);function i(e,t=[]){let n=[],l=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return l.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[a]||l,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(l,...t)]}},6101:(e,t,n)=>{n.d(t,{s:()=>l,t:()=>i});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function l(...e){return r.useCallback(i(...e),e)}},6521:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("layers-2",[["path",{d:"M13 13.74a2 2 0 0 1-2 0L2.5 8.87a1 1 0 0 1 0-1.74L11 2.26a2 2 0 0 1 2 0l8.5 4.87a1 1 0 0 1 0 1.74z",key:"15q6uc"}],["path",{d:"m20 14.285 1.5.845a1 1 0 0 1 0 1.74L13 21.74a2 2 0 0 1-2 0l-8.5-4.87a1 1 0 0 1 0-1.74l1.5-.845",key:"byia6g"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),i=n(3655),l=n(9033),a=n(5155),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,l.c)(m),E=(0,l.c)(g),C=r.useRef(null),R=(0,o.s)(t,e=>x(e)),k=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(k.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:h(C.current,{select:!0})},t=function(e){if(k.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,k.paused]),r.useEffect(()=>{if(w){v.add(k);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,c);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(s,c);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(s,E),v.remove(k)},0)}}},[w,b,E,k]);let A=r.useCallback(e=>{if(!n&&!d||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,k.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:A})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null==(n=(e=m(e,t))[0])||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},u=function(e,t,n,u){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var c=i[n],d=[],f=new Set,p=new Set(s),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};s.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,l=(r.get(e)||0)+1,a=(c.get(e)||0)+1;r.set(e,l),c.set(e,a),d.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),l++,function(){d.forEach(function(e){var t=r.get(e)-1,i=c.get(e)-1;r.set(e,t),c.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},8500:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},8578:(e,t,n)=>{n.d(t,{UC:()=>tu,YJ:()=>ts,q7:()=>td,JU:()=>tc,ZL:()=>ta,bL:()=>ti,wv:()=>tf,l9:()=>tl});var r,o=n(2115),i=n(5185),l=n(6101),a=n(6081),u=n(5845),s=n(3655);function c(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function d(e,t){var n=c(e,t,"get");return n.get?n.get.call(e):n.value}function f(e,t,n){var r=c(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var p=n(9708),h=n(5155);function v(e){let t=e+"CollectionProvider",[n,r]=(0,a.A)(t),[i,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=o.useRef(null),l=o.useRef(new Map).current;return(0,h.jsx)(i,{scope:t,itemMap:l,collectionRef:r,children:n})};s.displayName=t;let c=e+"CollectionSlot",d=(0,p.TL)(c),f=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=u(c,n),i=(0,l.s)(t,o.collectionRef);return(0,h.jsx)(d,{ref:i,children:r})});f.displayName=c;let v=e+"CollectionItemSlot",m="data-radix-collection-item",g=(0,p.TL)(v),y=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,a=o.useRef(null),s=(0,l.s)(t,a),c=u(v,n);return o.useEffect(()=>(c.itemMap.set(a,{ref:a,...i}),()=>void c.itemMap.delete(a))),(0,h.jsx)(g,{...{[m]:""},ref:s,children:r})});return y.displayName=v,[{Provider:s,Slot:f,ItemSlot:y},function(t){let n=u(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var m=new WeakMap;function g(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=y(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function y(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap,class e extends Map{set(e,t){return m.get(this)&&(this.has(e)?d(this,r)[d(this,r).indexOf(e)]=e:d(this,r).push(e)),super.set(e,t),this}insert(e,t,n){let o,i=this.has(t),l=d(this,r).length,a=y(e),u=a>=0?a:l+a,s=u<0||u>=l?-1:u;if(s===this.size||i&&s===this.size-1||-1===s)return this.set(t,n),this;let c=this.size+ +!i;a<0&&u++;let f=[...d(this,r)],p=!1;for(let e=u;e<c;e++)if(u===e){let r=f[e];f[e]===t&&(r=f[e+1]),i&&this.delete(t),o=this.get(r),this.set(t,n)}else{p||f[e-1]!==t||(p=!0);let n=f[p?e:e-1],r=o;o=this.get(n),this.delete(n),this.set(n,r)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=d(this,r).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let o=d(this,r).indexOf(e);return -1===o?this:this.insert(o,t,n)}after(e){let t=d(this,r).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let o=d(this,r).indexOf(e);return -1===o?this:this.insert(o+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return f(this,r,[]),super.clear()}delete(e){let t=super.delete(e);return t&&d(this,r).splice(d(this,r).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=g(d(this,r),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=g(d(this,r),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return d(this,r).indexOf(e)}keyAt(e){return g(d(this,r),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=0,l=null!=o?o:this.at(0);for(let e of this)l=0===i&&1===t.length?e:Reflect.apply(r,this,[l,e,i,this]),i++;return l}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);i=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[i,n,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,r,{writable:!0,value:void 0}),f(this,r,[...super.keys()]),m.set(this,!0)}};var w=o.createContext(void 0);function x(e){let t=o.useContext(w);return e||t||"ltr"}var b=n(9178),E=n(2293),C=n(7900),R=n(1285),k=n(5152),A=n(4378),M=n(8905),T=n(9033),S="rovingFocusGroup.onEntryFocus",L={bubbles:!1,cancelable:!0},j="RovingFocusGroup",[P,N,O]=v(j),[D,I]=(0,a.A)(j,[O]),[_,F]=D(j),W=o.forwardRef((e,t)=>(0,h.jsx)(P.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(P.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(B,{...e,ref:t})})}));W.displayName=j;var B=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:a=!1,dir:c,currentTabStopId:d,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:p,onEntryFocus:v,preventScrollOnEntryFocus:m=!1,...g}=e,y=o.useRef(null),w=(0,l.s)(t,y),b=x(c),[E,C]=(0,u.i)({prop:d,defaultProp:null!=f?f:null,onChange:p,caller:j}),[R,k]=o.useState(!1),A=(0,T.c)(v),M=N(n),P=o.useRef(!1),[O,D]=o.useState(0);return o.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(S,A),()=>e.removeEventListener(S,A)},[A]),(0,h.jsx)(_,{scope:n,orientation:r,dir:b,loop:a,currentTabStopId:E,onItemFocus:o.useCallback(e=>C(e),[C]),onItemShiftTab:o.useCallback(()=>k(!0),[]),onFocusableItemAdd:o.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>D(e=>e-1),[]),children:(0,h.jsx)(s.sG.div,{tabIndex:R||0===O?-1:0,"data-orientation":r,...g,ref:w,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!P.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(S,L);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=M().filter(e=>e.focusable);K([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),m)}}P.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>k(!1))})})}),z="RovingFocusGroupItem",H=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:l=!1,tabStopId:a,children:u,...c}=e,d=(0,R.B)(),f=a||d,p=F(z,n),v=p.currentTabStopId===f,m=N(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:w}=p;return o.useEffect(()=>{if(r)return g(),()=>y()},[r,g,y]),(0,h.jsx)(P.ItemSlot,{scope:n,id:f,focusable:r,active:l,children:(0,h.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{r?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return G[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>K(n))}}),children:"function"==typeof u?u({isCurrentTabStop:v,hasTabStop:null!=w}):u})})});H.displayName=z;var G={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function K(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var U=n(8168),V=n(3795),X=["Enter"," "],q=["ArrowUp","PageDown","End"],$=["ArrowDown","PageUp","Home",...q],Y={ltr:[...X,"ArrowRight"],rtl:[...X,"ArrowLeft"]},Z={ltr:["ArrowLeft"],rtl:["ArrowRight"]},J="Menu",[Q,ee,et]=v(J),[en,er]=(0,a.A)(J,[et,k.Bk,I]),eo=(0,k.Bk)(),ei=I(),[el,ea]=en(J),[eu,es]=en(J),ec=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:i,onOpenChange:l,modal:a=!0}=e,u=eo(t),[s,c]=o.useState(null),d=o.useRef(!1),f=(0,T.c)(l),p=x(i);return o.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,h.jsx)(k.bL,{...u,children:(0,h.jsx)(el,{scope:t,open:n,onOpenChange:f,content:s,onContentChange:c,children:(0,h.jsx)(eu,{scope:t,onClose:o.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:p,modal:a,children:r})})})};ec.displayName=J;var ed=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eo(n);return(0,h.jsx)(k.Mz,{...o,...r,ref:t})});ed.displayName="MenuAnchor";var ef="MenuPortal",[ep,eh]=en(ef,{forceMount:void 0}),ev=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=ea(ef,t);return(0,h.jsx)(ep,{scope:t,forceMount:n,children:(0,h.jsx)(M.C,{present:n||i.open,children:(0,h.jsx)(A.Z,{asChild:!0,container:o,children:r})})})};ev.displayName=ef;var em="MenuContent",[eg,ey]=en(em),ew=o.forwardRef((e,t)=>{let n=eh(em,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=ea(em,e.__scopeMenu),l=es(em,e.__scopeMenu);return(0,h.jsx)(Q.Provider,{scope:e.__scopeMenu,children:(0,h.jsx)(M.C,{present:r||i.open,children:(0,h.jsx)(Q.Slot,{scope:e.__scopeMenu,children:l.modal?(0,h.jsx)(ex,{...o,ref:t}):(0,h.jsx)(eb,{...o,ref:t})})})})}),ex=o.forwardRef((e,t)=>{let n=ea(em,e.__scopeMenu),r=o.useRef(null),a=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,U.Eq)(e)},[]),(0,h.jsx)(eC,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),eb=o.forwardRef((e,t)=>{let n=ea(em,e.__scopeMenu);return(0,h.jsx)(eC,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),eE=(0,p.TL)("MenuContent.ScrollLock"),eC=o.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:a,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:v,onInteractOutside:m,onDismiss:g,disableOutsideScroll:y,...w}=e,x=ea(em,n),R=es(em,n),A=eo(n),M=ei(n),T=ee(n),[S,L]=o.useState(null),j=o.useRef(null),P=(0,l.s)(t,j,x.onContentChange),N=o.useRef(0),O=o.useRef(""),D=o.useRef(0),I=o.useRef(null),_=o.useRef("right"),F=o.useRef(0),B=y?V.A:o.Fragment;o.useEffect(()=>()=>window.clearTimeout(N.current),[]),(0,E.Oh)();let z=o.useCallback(e=>{var t,n;return _.current===(null==(t=I.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,d=a.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=I.current)?void 0:n.area)},[]);return(0,h.jsx)(eg,{scope:n,searchRef:O,onItemEnter:o.useCallback(e=>{z(e)&&e.preventDefault()},[z]),onItemLeave:o.useCallback(e=>{var t;z(e)||(null==(t=j.current)||t.focus(),L(null))},[z]),onTriggerLeave:o.useCallback(e=>{z(e)&&e.preventDefault()},[z]),pointerGraceTimerRef:D,onPointerGraceIntentChange:o.useCallback(e=>{I.current=e},[]),children:(0,h.jsx)(B,{...y?{as:eE,allowPinchZoom:!0}:void 0,children:(0,h.jsx)(C.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,i.m)(u,e=>{var t;e.preventDefault(),null==(t=j.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,h.jsx)(b.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:v,onInteractOutside:m,onDismiss:g,children:(0,h.jsx)(W,{asChild:!0,...M,dir:R.dir,orientation:"vertical",loop:r,currentTabStopId:S,onCurrentTabStopIdChange:L,onEntryFocus:(0,i.m)(d,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,h.jsx)(k.UC,{role:"menu","aria-orientation":"vertical","data-state":e$(x.open),"data-radix-menu-content":"",dir:R.dir,...A,...w,ref:P,style:{outline:"none",...w.style},onKeyDown:(0,i.m)(w.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{var t,n;let r=O.current+e,o=T().filter(e=>!e.disabled),i=document.activeElement,l=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,l=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let a=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(o.map(e=>e.textValue),r,l),u=null==(n=o.find(e=>e.textValue===a))?void 0:n.ref.current;!function e(t){O.current=t,window.clearTimeout(N.current),""!==t&&(N.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())})(e.key));let o=j.current;if(e.target!==o||!$.includes(e.key))return;e.preventDefault();let i=T().filter(e=>!e.disabled).map(e=>e.ref.current);q.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(N.current),O.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eJ(e=>{let t=e.target,n=F.current!==e.clientX;e.currentTarget.contains(t)&&n&&(_.current=e.clientX>F.current?"right":"left",F.current=e.clientX)}))})})})})})})});ew.displayName=em;var eR=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,h.jsx)(s.sG.div,{role:"group",...r,ref:t})});eR.displayName="MenuGroup";var ek=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,h.jsx)(s.sG.div,{...r,ref:t})});ek.displayName="MenuLabel";var eA="MenuItem",eM="menu.itemSelect",eT=o.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...a}=e,u=o.useRef(null),c=es(eA,e.__scopeMenu),d=ey(eA,e.__scopeMenu),f=(0,l.s)(t,u),p=o.useRef(!1);return(0,h.jsx)(eS,{...a,ref:f,disabled:n,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!n&&e){let t=new CustomEvent(eM,{bubbles:!0,cancelable:!0});e.addEventListener(eM,e=>null==r?void 0:r(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),p.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||X.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eT.displayName=eA;var eS=o.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:a,...u}=e,c=ey(eA,n),d=ei(n),f=o.useRef(null),p=(0,l.s)(t,f),[v,m]=o.useState(!1),[g,y]=o.useState("");return o.useEffect(()=>{let e=f.current;if(e){var t;y((null!=(t=e.textContent)?t:"").trim())}},[u.children]),(0,h.jsx)(Q.ItemSlot,{scope:n,disabled:r,textValue:null!=a?a:g,children:(0,h.jsx)(H,{asChild:!0,...d,focusable:!r,children:(0,h.jsx)(s.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...u,ref:p,onPointerMove:(0,i.m)(e.onPointerMove,eJ(e=>{r?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eJ(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>m(!0)),onBlur:(0,i.m)(e.onBlur,()=>m(!1))})})})}),eL=o.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,h.jsx)(eF,{scope:e.__scopeMenu,checked:n,children:(0,h.jsx)(eT,{role:"menuitemcheckbox","aria-checked":eY(n)?"mixed":n,...o,ref:t,"data-state":eZ(n),onSelect:(0,i.m)(o.onSelect,()=>null==r?void 0:r(!!eY(n)||!n),{checkForDefaultPrevented:!1})})})});eL.displayName="MenuCheckboxItem";var ej="MenuRadioGroup",[eP,eN]=en(ej,{value:void 0,onValueChange:()=>{}}),eO=o.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,T.c)(r);return(0,h.jsx)(eP,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,h.jsx)(eR,{...o,ref:t})})});eO.displayName=ej;var eD="MenuRadioItem",eI=o.forwardRef((e,t)=>{let{value:n,...r}=e,o=eN(eD,e.__scopeMenu),l=n===o.value;return(0,h.jsx)(eF,{scope:e.__scopeMenu,checked:l,children:(0,h.jsx)(eT,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":eZ(l),onSelect:(0,i.m)(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});eI.displayName=eD;var e_="MenuItemIndicator",[eF,eW]=en(e_,{checked:!1}),eB=o.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eW(e_,n);return(0,h.jsx)(M.C,{present:r||eY(i.checked)||!0===i.checked,children:(0,h.jsx)(s.sG.span,{...o,ref:t,"data-state":eZ(i.checked)})})});eB.displayName=e_;var ez=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,h.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ez.displayName="MenuSeparator";var eH=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eo(n);return(0,h.jsx)(k.i3,{...o,...r,ref:t})});eH.displayName="MenuArrow";var[eG,eK]=en("MenuSub"),eU="MenuSubTrigger",eV=o.forwardRef((e,t)=>{let n=ea(eU,e.__scopeMenu),r=es(eU,e.__scopeMenu),a=eK(eU,e.__scopeMenu),u=ey(eU,e.__scopeMenu),s=o.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=o.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return o.useEffect(()=>p,[p]),o.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,h.jsx)(ed,{asChild:!0,...f,children:(0,h.jsx)(eS,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":e$(n.open),...e,ref:(0,l.t)(t,a.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eJ(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eJ(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,l=o[i?"left":"right"],a=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:l,y:o.top},{x:a,y:o.top},{x:a,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let o=""!==u.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&Y[r.dir].includes(t.key)){var i;n.onOpenChange(!0),null==(i=n.content)||i.focus(),t.preventDefault()}})})})});eV.displayName=eU;var eX="MenuSubContent",eq=o.forwardRef((e,t)=>{let n=eh(em,e.__scopeMenu),{forceMount:r=n.forceMount,...a}=e,u=ea(em,e.__scopeMenu),s=es(em,e.__scopeMenu),c=eK(eX,e.__scopeMenu),d=o.useRef(null),f=(0,l.s)(t,d);return(0,h.jsx)(Q.Provider,{scope:e.__scopeMenu,children:(0,h.jsx)(M.C,{present:r||u.open,children:(0,h.jsx)(Q.Slot,{scope:e.__scopeMenu,children:(0,h.jsx)(eC,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=Z[s.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function e$(e){return e?"open":"closed"}function eY(e){return"indeterminate"===e}function eZ(e){return eY(e)?"indeterminate":e?"checked":"unchecked"}function eJ(e){return t=>"mouse"===t.pointerType?e(t):void 0}eq.displayName=eX;var eQ="DropdownMenu",[e0,e1]=(0,a.A)(eQ,[er]),e2=er(),[e5,e4]=e0(eQ),e9=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:i,defaultOpen:l,onOpenChange:a,modal:s=!0}=e,c=e2(t),d=o.useRef(null),[f,p]=(0,u.i)({prop:i,defaultProp:null!=l&&l,onChange:a,caller:eQ});return(0,h.jsx)(e5,{scope:t,triggerId:(0,R.B)(),triggerRef:d,contentId:(0,R.B)(),open:f,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,h.jsx)(ec,{...c,open:f,onOpenChange:p,dir:r,modal:s,children:n})})};e9.displayName=eQ;var e3="DropdownMenuTrigger",e7=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=e4(e3,n),u=e2(n);return(0,h.jsx)(ed,{asChild:!0,...u,children:(0,h.jsx)(s.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,l.t)(t,a.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e7.displayName=e3;var e6=e=>{let{__scopeDropdownMenu:t,...n}=e,r=e2(t);return(0,h.jsx)(ev,{...r,...n})};e6.displayName="DropdownMenuPortal";var e8="DropdownMenuContent",te=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,l=e4(e8,n),a=e2(n),u=o.useRef(!1);return(0,h.jsx)(ew,{id:l.contentId,"aria-labelledby":l.triggerId,...a,...r,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=l.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!l.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});te.displayName=e8;var tt=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eR,{...o,...r,ref:t})});tt.displayName="DropdownMenuGroup";var tn=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(ek,{...o,...r,ref:t})});tn.displayName="DropdownMenuLabel";var tr=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eT,{...o,...r,ref:t})});tr.displayName="DropdownMenuItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eL,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eO,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eI,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eB,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var to=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(ez,{...o,...r,ref:t})});to.displayName="DropdownMenuSeparator",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eH,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eV,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,h.jsx)(eq,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var ti=e9,tl=e7,ta=e6,tu=te,ts=tt,tc=tn,td=tr,tf=to},8905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),o=n(6101),i=n(2712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),s=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:s}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(2115),i=n(5185),l=n(3655),a=n(6101),u=n(9033),s=n(5155),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,E=o.useContext(d),[C,R]=o.useState(null),k=null!=(f=null==C?void 0:C.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,A]=o.useState({}),M=(0,a.s)(t,e=>R(e)),T=Array.from(E.layers),[S]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),L=T.indexOf(S),j=C?T.indexOf(C):-1,P=E.layersWithOutsidePointerEventsDisabled.size>0,N=j>=L,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));N&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},k),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},k);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{j===E.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},k),o.useEffect(()=>{if(C)return v&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),p(),()=>{v&&1===E.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=r)}},[C,k,v,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),p())},[C,E]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(l.sG.div,{...b,ref:M,style:{pointerEvents:P?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9245:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]])},9708:(e,t,n)=>{n.d(t,{Dc:()=>s,TL:()=>l,bL:()=>a});var r=n(2115),o=n(6101),i=n(5155);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var l;let e,a,u=(l=n,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...l}=e,a=r.Children.toArray(o),u=a.find(c);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=l("Slot"),u=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},9946:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(2115);let o=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:n,strokeWidth:u?24*Number(a)/Number(o):a,className:i("lucide",s),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:u,...s}=n;return(0,r.createElement)(a,{ref:l,iconNode:t,className:i("lucide-".concat(o(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),u),...s})});return n.displayName=o(e),n}},9963:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]])}}]);