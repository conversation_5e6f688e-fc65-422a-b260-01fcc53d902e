{"name": "radix-ui", "version": "1.4.2", "license": "MIT", "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./*": {"import": {"types": "./dist/*.d.mts", "default": "./dist/*.mjs"}, "require": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}}}, "files": ["dist", "src", "README.md"], "sideEffects": false, "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-accessible-icon": "1.1.7", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-arrow": "1.1.7", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-context-menu": "2.2.15", "@radix-ui/react-collection": "1.1.7", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-focus-scope": "1.1.7", "@radix-ui/react-form": "0.1.7", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-menu": "2.1.15", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-menubar": "1.1.15", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-one-time-password-field": "0.1.7", "@radix-ui/react-password-toggle-field": "0.1.2", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-roving-focus": "1.1.10", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toolbar": "1.1.10", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-use-effect-event": "0.0.2", "@radix-ui/react-use-escape-keydown": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.3", "@radix-ui/react-use-is-hydrated": "0.1.0"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}, "homepage": "https://radix-ui.com/primitives", "repository": {"type": "git", "url": "git+https://github.com/radix-ui/primitives.git"}, "bugs": {"url": "https://github.com/radix-ui/primitives/issues"}, "scripts": {"lint": "eslint --max-warnings 0 src", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "build:esm": "tsc", "build:cjs": "tsc --module commonjs --moduleResolution node --outDir dist/cjs", "// build": "pnpm run --parallel \"/^build:.*/\"", "build": "radix-build"}, "types": "./dist/index.d.ts"}