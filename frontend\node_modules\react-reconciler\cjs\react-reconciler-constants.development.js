/**
 * @license React
 * react-reconciler-constants.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

"use strict";
"production" !== process.env.NODE_ENV &&
  ((exports.ConcurrentRoot = 1),
  (exports.ContinuousEventPriority = 8),
  (exports.DefaultEventPriority = 32),
  (exports.DiscreteEventPriority = 2),
  (exports.IdleEventPriority = 268435456),
  (exports.LegacyRoot = 0),
  (exports.NoEventPriority = 0));
