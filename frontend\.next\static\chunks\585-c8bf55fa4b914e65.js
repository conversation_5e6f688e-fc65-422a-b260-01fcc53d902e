"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[585],{1824:(e,t,a)=>{a.d(t,{x:()=>r.x});var r=a(6064)},1831:(e,t,a)=>{a.d(t,{default:()=>ea});var r=a(5155),s=a(1284),n=a(9245),l=a(5560),o=a(1788),i=a(2115),d=a(2085),c=a(9708),u=a(9434);let m=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function x(e){let{className:t,variant:a,size:s,asChild:n=!1,...l}=e,o=n?c.bL:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,u.cn)(m({variant:a,size:s,className:t})),...l})}var h=a(8578);function f(e){let{...t}=e;return(0,r.jsx)(h.bL,{"data-slot":"dropdown-menu",...t})}function p(e){let{...t}=e;return(0,r.jsx)(h.l9,{"data-slot":"dropdown-menu-trigger",...t})}function g(e){let{className:t,sideOffset:a=4,onPointerDown:s,onPointerDownOutside:n,onCloseAutoFocus:l,...o}=e,d=i.useRef(!1),c=i.useCallback(e=>{d.current=!0,null==s||s(e)},[s]),m=i.useCallback(e=>{d.current=!0,null==n||n(e)},[n]),x=i.useCallback(e=>{if(l)return l(e);d.current&&(e.preventDefault(),d.current=!1)},[l]);return(0,r.jsx)(h.ZL,{children:(0,r.jsx)(h.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,u.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-40 overflow-hidden rounded-md border p-1 shadow-lg",t),onPointerDown:c,onPointerDownOutside:m,onCloseAutoFocus:x,...o})})}function v(e){let{...t}=e;return(0,r.jsx)(h.YJ,{"data-slot":"dropdown-menu-group",...t})}function j(e){let{className:t,inset:a,variant:s="default",...n}=e;return(0,r.jsx)(h.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":s,className:(0,u.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),...n})}function b(e){let{className:t,inset:a,...s}=e;return(0,r.jsx)(h.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,u.cn)("text-muted-foreground px-2 py-1.5 text-xs font-medium data-[inset]:pl-8",t),...s})}function N(e){let{className:t,...a}=e;return(0,r.jsx)(h.wv,{"data-slot":"dropdown-menu-separator",className:(0,u.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function w(){return(0,r.jsxs)(f,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsx)(x,{size:"icon",variant:"ghost",className:"size-8 rounded-full shadow-none","aria-label":"Open edit menu",children:(0,r.jsx)(s.A,{className:"text-muted-foreground",size:16,"aria-hidden":"true"})})}),(0,r.jsxs)(g,{className:"pb-2",children:[(0,r.jsx)(b,{children:"Precisa de ajuda?"}),(0,r.jsx)(j,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,r.jsxs)("a",{href:"https://docs.supabase.com",target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(n.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Documenta\xe7\xe3o"]})}),(0,r.jsx)(j,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,r.jsxs)("a",{href:"#",onClick:()=>alert("Em breve!"),children:[(0,r.jsx)(l.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Suporte"]})}),(0,r.jsx)(j,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,r.jsxs)("a",{href:"#",onClick:()=>alert("Em breve!"),children:[(0,r.jsx)(o.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Contato"]})})]})]})}var y=a(3861),k=a(547);function C(e){let{...t}=e;return(0,r.jsx)(k.bL,{"data-slot":"popover",...t})}function z(e){let{...t}=e;return(0,r.jsx)(k.l9,{"data-slot":"popover-trigger",...t})}function _(e){let{className:t,align:a="center",sideOffset:s=4,showArrow:n=!1,...l}=e;return(0,r.jsx)(k.ZL,{children:(0,r.jsxs)(k.UC,{"data-slot":"popover-content",align:a,sideOffset:s,className:(0,u.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 rounded-md border p-4 shadow-md outline-hidden",t),...l,children:[l.children,n&&(0,r.jsx)(k.i3,{className:"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]"})]})})}let S=[{id:1,user:"Carlos Silva",action:"solicitou revis\xe3o em",target:"PR #42: Implementa\xe7\xe3o de funcionalidade",timestamp:"h\xe1 15 minutos",unread:!0},{id:2,user:"Ana Santos",action:"compartilhou",target:"Nova biblioteca de componentes",timestamp:"h\xe1 45 minutos",unread:!0},{id:3,user:"Jo\xe3o Oliveira",action:"atribuiu voc\xea para",target:"Tarefa de integra\xe7\xe3o da API",timestamp:"h\xe1 4 horas",unread:!1},{id:4,user:"Maria Costa",action:"respondeu ao seu coment\xe1rio em",target:"Fluxo de autentica\xe7\xe3o",timestamp:"h\xe1 12 horas",unread:!1},{id:5,user:"Pedro Lima",action:"comentou em",target:"Redesign do dashboard",timestamp:"h\xe1 2 dias",unread:!1},{id:6,user:"Lucia Ferreira",action:"mencionou voc\xea em",target:"Imagem do projeto Supabase",timestamp:"h\xe1 2 semanas",unread:!1}];function M(e){let{className:t}=e;return(0,r.jsx)("svg",{width:"6",height:"6",fill:"currentColor",viewBox:"0 0 6 6",xmlns:"http://www.w3.org/2000/svg",className:t,"aria-hidden":"true",children:(0,r.jsx)("circle",{cx:"3",cy:"3",r:"3"})})}function I(){let[e,t]=(0,i.useState)(S),a=e.filter(e=>e.unread).length;return(0,r.jsxs)(C,{children:[(0,r.jsx)(z,{asChild:!0,children:(0,r.jsxs)(x,{size:"icon",variant:"ghost",className:"text-muted-foreground relative size-8 rounded-full shadow-none","aria-label":"Open notifications",children:[(0,r.jsx)(y.A,{size:16,"aria-hidden":"true"}),a>0&&(0,r.jsx)("div",{"aria-hidden":"true",className:"bg-primary absolute top-0.5 right-0.5 size-1 rounded-full"})]})}),(0,r.jsxs)(_,{className:"w-80 p-1",children:[(0,r.jsxs)("div",{className:"flex items-baseline justify-between gap-4 px-3 py-2",children:[(0,r.jsx)("div",{className:"text-sm font-semibold",children:"Notifica\xe7\xf5es"}),a>0&&(0,r.jsx)("button",{className:"text-xs font-medium hover:underline",onClick:()=>{t(e.map(e=>({...e,unread:!1})))},children:"Marcar todas como lidas"})]}),(0,r.jsx)("div",{role:"separator","aria-orientation":"horizontal",className:"bg-border -mx-1 my-1 h-px"}),e.map(a=>(0,r.jsx)("div",{className:"hover:bg-accent rounded-md px-3 py-2 text-sm transition-colors",children:(0,r.jsxs)("div",{className:"relative flex items-start pe-3",children:[(0,r.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,r.jsxs)("button",{className:"text-foreground/80 text-left after:absolute after:inset-0",onClick:()=>{var r;return r=a.id,void t(e.map(e=>e.id===r?{...e,unread:!1}:e))},children:[(0,r.jsx)("span",{className:"text-foreground font-medium hover:underline",children:a.user})," ",a.action," ",(0,r.jsx)("span",{className:"text-foreground font-medium hover:underline",children:a.target}),"."]}),(0,r.jsx)("div",{className:"text-muted-foreground text-xs",children:a.timestamp})]}),a.unread&&(0,r.jsxs)("div",{className:"absolute end-0 self-center",children:[(0,r.jsx)("span",{className:"sr-only",children:"Unread"}),(0,r.jsx)(M,{})]})]})},a.id))]})]})}var E=a(1007),A=a(381),P=a(6521),R=a(9963),L=a(5040),U=a(4835),O=a(5695),B=a(6681),D=a(1824),H=a(4011);function F(e){let{className:t,...a}=e;return(0,r.jsx)(H.bL,{"data-slot":"avatar",className:(0,u.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function V(e){let{className:t,...a}=e;return(0,r.jsx)(H._V,{"data-slot":"avatar-image",className:(0,u.cn)("aspect-square size-full",t),...a})}function J(e){let{className:t,...a}=e;return(0,r.jsx)(H.H4,{"data-slot":"avatar-fallback",className:(0,u.cn)("bg-secondary flex size-full items-center justify-center rounded-[inherit] text-xs",t),...a})}function T(){var e,t,a,s;let{user:n,signOut:l,isLoading:o}=(0,B.A)(),{profile:d}=(0,D.x)(),c=(0,O.useRouter)(),[u,m]=(0,i.useState)(!1),h=async()=>{try{m(!0);let e=await l();e.success?c.push("/login"):console.error("Erro ao fazer logout:",e.error)}catch(e){console.error("Erro inesperado ao fazer logout:",e)}finally{m(!1)}},w=e=>{if(!e)return"US";let t=e.split("@")[0].split(".");return t.length>=2?(t[0][0]+t[1][0]).toUpperCase():e.substring(0,2).toUpperCase()},y=()=>{var e,t;if(null==d?void 0:d.full_name)return d.full_name;if(null==n||null==(e=n.user_metadata)?void 0:e.full_name)return n.user_metadata.full_name;if(null==n||null==(t=n.user_metadata)?void 0:t.name)return n.user_metadata.name;if(null==n?void 0:n.email){let e=n.email.split("@")[0];return e.charAt(0).toUpperCase()+e.slice(1)}return"Usu\xe1rio"},k=e=>{c.push(e)};return o||!n?null:(0,r.jsxs)(f,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsx)(x,{variant:"ghost",className:"h-auto p-2 hover:bg-accent focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg",disabled:u,children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(F,{className:"h-8 w-8",children:[(0,r.jsx)(V,{src:(null==d?void 0:d.avatar_url)||(null==(e=n.user_metadata)?void 0:e.avatar_url)||(null==(t=n.user_metadata)?void 0:t.picture),alt:"Imagem do perfil"}),(0,r.jsx)(J,{className:"bg-primary/10 text-primary font-medium",children:w(n.email||"US")})]}),(0,r.jsxs)("div",{className:"flex flex-col items-start min-w-0",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-foreground truncate max-w-[150px]",children:y()}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground truncate max-w-[150px]",children:n.email})]})]})})}),(0,r.jsxs)(g,{className:"w-64",align:"end",sideOffset:8,children:[(0,r.jsx)(b,{className:"flex min-w-0 flex-col p-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(F,{className:"h-10 w-10",children:[(0,r.jsx)(V,{src:(null==d?void 0:d.avatar_url)||(null==(a=n.user_metadata)?void 0:a.avatar_url)||(null==(s=n.user_metadata)?void 0:s.picture),alt:"Imagem do perfil"}),(0,r.jsx)(J,{className:"bg-primary/10 text-primary font-medium",children:w(n.email||"US")})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-foreground truncate text-sm font-medium",children:y()}),(0,r.jsx)("p",{className:"text-muted-foreground truncate text-xs font-normal",children:n.email})]})]})}),(0,r.jsx)(N,{}),(0,r.jsxs)(v,{children:[(0,r.jsxs)(j,{onClick:()=>k("/profile"),className:"cursor-pointer",children:[(0,r.jsx)(E.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,r.jsx)("span",{children:"Meu Perfil"})]}),(0,r.jsxs)(j,{onClick:()=>k("/configuracoes"),className:"cursor-pointer",children:[(0,r.jsx)(A.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,r.jsx)("span",{children:"Configura\xe7\xf5es"})]})]}),(0,r.jsx)(N,{}),(0,r.jsxs)(v,{children:[(0,r.jsxs)(j,{onClick:()=>k("/projetos"),className:"cursor-pointer",children:[(0,r.jsx)(P.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,r.jsx)("span",{children:"Meus Projetos"})]}),(0,r.jsxs)(j,{onClick:()=>k("/favoritos"),className:"cursor-pointer",children:[(0,r.jsx)(R.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,r.jsx)("span",{children:"Favoritos"})]})]}),(0,r.jsx)(N,{}),(0,r.jsx)(v,{children:(0,r.jsxs)(j,{onClick:()=>window.open("https://docs.supabase.com","_blank"),className:"cursor-pointer",children:[(0,r.jsx)(L.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,r.jsx)("span",{children:"Documenta\xe7\xe3o"})]})}),(0,r.jsx)(N,{}),(0,r.jsxs)(j,{onClick:h,className:"cursor-pointer text-destructive focus:text-destructive",disabled:u,children:[(0,r.jsx)(U.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,r.jsx)("span",{children:u?"Saindo...":"Sair"})]})]})]})}var W=a(3109),Z=a(8500),q=a(3904);function G(){let[e,t]=(0,i.useState)({portfolioBalance:623098.17,availableFunds:122912.5,isLoading:!1,trend:"up",changePercent:2.34,lastUpdated:new Date}),[a,s]=(0,i.useState)(!1),n=(0,i.useMemo)(()=>{let e=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2});return t=>e.format(t)},[]),l=async()=>{s(!0),await new Promise(e=>setTimeout(e,1e3)),t(e=>({...e,portfolioBalance:e.portfolioBalance+(Math.random()-.5)*2e3,availableFunds:e.availableFunds+(Math.random()-.5)*1e3,changePercent:(Math.random()-.5)*8,trend:Math.random()>.5?"up":"down",lastUpdated:new Date})),s(!1)};return((0,i.useEffect)(()=>{let e=setInterval(()=>{a||t(e=>({...e,portfolioBalance:e.portfolioBalance+(Math.random()-.5)*500,availableFunds:e.availableFunds+(Math.random()-.5)*200,changePercent:e.changePercent+(Math.random()-.5)*.5,trend:Math.random()>.6?Math.random()>.5?"up":"down":e.trend,lastUpdated:new Date}))},6e4);return()=>clearInterval(e)},[a]),e.isLoading)?(0,r.jsxs)("div",{className:"flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg border animate-pulse",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsx)("div",{className:"h-3 bg-muted rounded w-20"}),(0,r.jsx)("div",{className:"h-5 bg-muted rounded w-28"})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-border"}),(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsx)("div",{className:"h-3 bg-muted rounded w-24"}),(0,r.jsx)("div",{className:"h-5 bg-muted rounded w-24"})]})]}):(0,r.jsxs)("div",{className:"flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg  hover:bg-card/50 transition-all duration-200 group",children:[(0,r.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-1 mb-0.5",children:(0,r.jsx)("span",{className:"text-xs  text-muted-foreground  tracking-wider",children:"Saldo Inicial"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm  text-foreground tabular-nums truncate",children:n(e.portfolioBalance)}),(0,r.jsxs)("div",{className:"flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs font-medium transition-colors ".concat("up"===e.trend?"text-primary  dark:text-primary ":"text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900/30"),children:["up"===e.trend?(0,r.jsx)(W.A,{size:10}):(0,r.jsx)(Z.A,{size:10}),(0,r.jsxs)("span",{children:[Math.abs(e.changePercent).toFixed(2),"%"]})]})]})]}),(0,r.jsx)("div",{className:"h-8 w-px bg-border/60"}),(0,r.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-1 mb-0.5",children:(0,r.jsx)("span",{className:"text-xs  text-muted-foreground  tracking-wider",children:"Saldo Atual"})}),(0,r.jsx)("span",{className:"text-sm  text-foreground tabular-nums truncate",children:n(e.availableFunds)})]}),(0,r.jsx)("div",{className:"flex items-center gap-2 ml-2",children:(0,r.jsx)(x,{variant:"ghost",size:"sm",onClick:l,disabled:a,className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,r.jsx)(q.A,{size:14,className:"".concat(a?"animate-spin":""," text-muted-foreground hover:text-foreground")})})})]})}var Y=a(3509),Q=a(2098),X=a(968);function K(e){let{className:t,...a}=e;return(0,r.jsx)(X.b,{"data-slot":"label",className:(0,u.cn)("text-foreground text-sm leading-4 font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}var $=a(239);function ee(e){let{className:t,...a}=e;return(0,r.jsx)($.bL,{"data-slot":"switch",className:(0,u.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:ring-ring/50 inline-flex h-6 w-10 shrink-0 items-center rounded-full border-2 border-transparent transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)($.zi,{"data-slot":"switch-thumb",className:(0,u.cn)("bg-background pointer-events-none block size-5 rounded-full shadow-xs ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0 data-[state=checked]:rtl:-translate-x-4")})})}function et(){let e=(0,i.useId)(),{toggleTheme:t,isDark:a,isInitialized:s}=function(){let[e,t]=(0,i.useState)("dark"),[a,r]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{let e=localStorage.getItem("theme");e&&("dark"===e||"light"===e)?(t(e),document.documentElement.className=e):(document.documentElement.className="dark",localStorage.setItem("theme","dark")),r(!0)},[]),{theme:e,toggleTheme:()=>{if(!a)return;let r="dark"===e?"light":"dark";t(r),document.documentElement.className=r,localStorage.setItem("theme",r)},setTheme:e=>{a&&(t(e),document.documentElement.className=e,localStorage.setItem("theme",e))},isDark:"dark"===e,isLight:"light"===e,isInitialized:a}}();return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"relative inline-grid h-9 grid-cols-[1fr_1fr] items-center text-sm font-medium",children:[(0,r.jsx)(ee,{id:e,checked:a,onCheckedChange:t,disabled:!s,className:"peer data-[state=checked]:bg-input/50 data-[state=unchecked]:bg-input/50 absolute inset-0 h-[inherit] w-auto [&_span]:h-full [&_span]:w-1/2 [&_span]:transition-transform [&_span]:duration-300 [&_span]:ease-[cubic-bezier(0.16,1,0.3,1)] [&_span]:data-[state=checked]:translate-x-full [&_span]:data-[state=checked]:rtl:-translate-x-full"}),(0,r.jsx)("span",{className:"peer-data-[state=checked]:text-muted-foreground/70 pointer-events-none relative ms-0.5 flex min-w-8 items-center justify-center text-center",children:(0,r.jsx)(Y.A,{size:16,"aria-hidden":"true"})}),(0,r.jsx)("span",{className:"peer-data-[state=unchecked]:text-muted-foreground/70 pointer-events-none relative me-0.5 flex min-w-8 items-center justify-center text-center",children:(0,r.jsx)(Q.A,{size:16,"aria-hidden":"true"})})]}),(0,r.jsx)(K,{htmlFor:e,className:"sr-only",children:"Alternar tema entre claro e escuro"})]})}function ea(){return(0,r.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsx)("div",{className:"container mx-auto px-4 md:px-6",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between gap-4",children:[(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsx)(G,{})}),(0,r.jsx)("div",{className:"flex-1"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(et,{}),(0,r.jsx)(I,{}),(0,r.jsx)(w,{}),(0,r.jsx)(T,{})]})]})})})}},2099:(e,t,a)=>{a.d(t,{N:()=>r});let r=(0,a(5647).UU)("https://dpofnwutgpbwylwtbgnv.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.lE40lk7gBuq77mqJUezZYxUmHSeRSC6kOTVjwvP2hLw")},2261:(e,t,a)=>{a.d(t,{SidebarProvider:()=>l,c:()=>o});var r=a(5155),s=a(2115);let n=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[a,l]=(0,s.useState)(!1),[o,i]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=localStorage.getItem("sidebar-collapsed");null!==e&&l(JSON.parse(e)),i(!0)},[]),(0,s.useEffect)(()=>{o&&localStorage.setItem("sidebar-collapsed",JSON.stringify(a))},[a,o]),(0,r.jsx)(n.Provider,{value:{isCollapsed:a,toggleSidebar:()=>{l(e=>!e)},setSidebarCollapsed:e=>{l(e)}},children:t})}function o(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useSidebar deve ser usado dentro de um SidebarProvider");return e}},6064:(e,t,a)=>{a.d(t,{H:()=>i,x:()=>d});var r=a(5155),s=a(2115),n=a(2099),l=a(6681);let o=(0,s.createContext)(void 0),i=e=>{let{children:t}=e,{user:a}=(0,l.A)(),[i,d]=(0,s.useState)(null),[c,u]=(0,s.useState)(!0),[m,x]=(0,s.useState)(null),[h,f]=(0,s.useState)(!1),p=(0,s.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!a){d(null),u(!1),f(!1);return}if(!e&&i&&h)return void u(!1);try{u(!0),x(null);let{data:e,error:s}=await n.N.from("profiles").select("*").eq("id",a.id).single();if(s&&"PGRST116"!==s.code)throw s;if(e)d(e);else{var t,r;let e={id:a.id,email:a.email||"",full_name:(null==(t=a.user_metadata)?void 0:t.full_name)||"",avatar_url:(null==(r=a.user_metadata)?void 0:r.avatar_url)||"",created_at:a.created_at},{data:s,error:l}=await n.N.from("profiles").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(l)throw l;d(s)}f(!0)}catch(e){console.error("Erro ao carregar perfil:",e),x("Erro ao carregar perfil do usu\xe1rio"),f(!1)}finally{u(!1)}},[a,i,h]),g=async()=>{await p(!0)},v=async e=>{if(!a||!i)throw Error("Usu\xe1rio n\xe3o autenticado ou perfil n\xe3o carregado");try{x(null);let t={...e,updated_at:new Date().toISOString()},{data:r,error:s}=await n.N.from("profiles").update(t).eq("id",a.id).select().single();if(s)throw s;return d(r),{success:!0,data:r}}catch(t){console.error("Erro ao atualizar perfil:",t);let e="Erro ao salvar altera\xe7\xf5es";return x(e),{success:!1,error:e}}},j=async e=>{if(!a)throw Error("Usu\xe1rio n\xe3o autenticado");try{if(x(null),!e.type.startsWith("image/"))throw Error("Por favor, selecione apenas arquivos de imagem");if(e.size>5242880)throw Error("A imagem deve ter no m\xe1ximo 5MB");let t=e.name.split(".").pop(),r="".concat(a.id,"-").concat(Date.now(),".").concat(t),s="avatars/".concat(r);if(null==i?void 0:i.avatar_url){let e=i.avatar_url.split("/").pop();e&&await n.N.storage.from("avatars").remove(["avatars/".concat(e)])}let{error:l}=await n.N.storage.from("avatars").upload(s,e);if(l)throw l;let{data:{publicUrl:o}}=n.N.storage.from("avatars").getPublicUrl(s),d=await v({avatar_url:o});if(d.success)return{success:!0,url:o};throw Error(d.error)}catch(t){console.error("Erro ao fazer upload do avatar:",t);let e=t instanceof Error?t.message:"Erro ao fazer upload da imagem";return x(e),{success:!1,error:e}}},b=async()=>{if(a&&(null==i?void 0:i.avatar_url))try{x(null);let e=i.avatar_url.split("/").pop();return e&&await n.N.storage.from("avatars").remove(["avatars/".concat(e)]),await v({avatar_url:""})}catch(t){console.error("Erro ao remover avatar:",t);let e="Erro ao remover avatar";return x(e),{success:!1,error:e}}};return(0,s.useEffect)(()=>{a?(f(!1),p()):(d(null),u(!1),f(!1))},[null==a?void 0:a.id,a,p]),(0,r.jsx)(o.Provider,{value:{profile:i,isLoading:c,error:m,updateProfile:v,uploadAvatar:j,removeAvatar:b,clearError:()=>x(null),refreshProfile:g},children:t})},d=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useProfile deve ser usado dentro de um ProfileProvider");return e}},6681:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(2115),s=a(2099);let n=()=>{let[e,t]=(0,r.useState)(null),[a,n]=(0,r.useState)(null),[l,o]=(0,r.useState)(!0),[i,d]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async()=>{let{data:{session:e},error:a}=await s.N.auth.getSession();if(a)d({message:a.message});else{var r;n(e),t(null!=(r=null==e?void 0:e.user)?r:null)}o(!1)})();let{data:{subscription:e}}=s.N.auth.onAuthStateChange(async(e,a)=>{var r;n(a),t(null!=(r=null==a?void 0:a.user)?r:null),o(!1),"SIGNED_OUT"===e&&d(null)});return()=>e.unsubscribe()},[]),{user:e,session:a,isLoading:l,error:i,signInWithEmail:async(e,t)=>{o(!0),d(null);let{data:a,error:r}=await s.N.auth.signInWithPassword({email:e,password:t});return r?(d({message:r.message}),o(!1),{success:!1,error:r}):(o(!1),{success:!0,data:a})},signUpWithEmail:async(e,t)=>{o(!0),d(null);let{data:a,error:r}=await s.N.auth.signUp({email:e,password:t});return r?(d({message:r.message}),o(!1),{success:!1,error:r}):(o(!1),{success:!0,data:a})},signInWithGoogle:async()=>{o(!0),d(null);let{data:e,error:t}=await s.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/dashboard")}});return t?(d({message:t.message}),o(!1),{success:!1,error:t}):{success:!0,data:e}},signInWithOTP:async e=>{o(!0),d(null);let{data:t,error:a}=await s.N.auth.signInWithOtp({email:e,options:{shouldCreateUser:!0}});return a?(d({message:a.message}),o(!1),{success:!1,error:a}):(o(!1),{success:!0,data:t})},verifyOTP:async(e,t)=>{o(!0),d(null);let{data:a,error:r}=await s.N.auth.verifyOtp({email:e,token:t,type:"email"});return r?(d({message:r.message}),o(!1),{success:!1,error:r}):(o(!1),{success:!0,data:a})},signOut:async()=>{o(!0);let{error:e}=await s.N.auth.signOut();return e&&d({message:e.message}),o(!1),{success:!e,error:e}}}}},6839:(e,t,a)=>{a.d(t,{default:()=>b});var r=a(5155),s=a(2115),n=a(6874),l=a.n(n),o=a(6766),i=a(2261),d=a(3921),c=a(9434);function u(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(d.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function m(e){let{...t}=e;return(0,r.jsx)(u,{children:(0,r.jsx)(d.bL,{"data-slot":"tooltip",...t})})}function x(e){let{...t}=e;return(0,r.jsx)(d.l9,{"data-slot":"tooltip-trigger",...t})}function h(e){let{className:t,sideOffset:a=4,showArrow:s=!1,children:n,...l}=e;return(0,r.jsx)(d.ZL,{children:(0,r.jsxs)(d.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,c.cn)("bg-popover text-popover-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-w-70 rounded-md border px-3 py-1.5 text-sm",t),...l,children:[n,s&&(0,r.jsx)(d.i3,{className:"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]"})]})})}let f=()=>(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})}),p=()=>(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"})}),g=()=>(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"})}),v=()=>(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})}),j=()=>(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"})}),b=()=>{let[e,t]=(0,s.useState)(null),{isCollapsed:a,toggleSidebar:n}=(0,i.c)(),d=(0,s.useCallback)(e=>{t(t=>t===e?null:e)},[]);return(0,r.jsx)(u,{delayDuration:0,children:(0,r.jsxs)("div",{className:"".concat(a?"w-16":"w-[270px]"," h-full bg-card border-r border-border flex flex-col transition-all duration-300 ease-in-out"),children:[(0,r.jsx)("div",{className:"p-4 border-b border-border",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)("button",{onClick:n,className:"flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors",children:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 4l8 8-8 8"})})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Expandir sidebar"})]}):(0,r.jsx)("button",{onClick:n,className:"flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors",children:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 4l-8 8 8 8"})})})})}),(0,r.jsx)("div",{className:"p-4 border-b border-border",children:(0,r.jsx)(l(),{href:"/dashboard",className:"flex items-center gap-3",children:(0,r.jsx)(o.default,{src:a?"/icon-logo.svg":"/logo.svg",alt:"Profit Growth",width:32,height:32,className:"h-8 w-auto"})})}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,r.jsxs)("div",{className:"p-4",children:[!a&&(0,r.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"PRINCIPAL"}),(0,r.jsxs)("nav",{className:"space-y-1",children:[a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(l(),{href:"/",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg bg-primary text-primary-foreground",children:(0,r.jsx)(f,{})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"In\xedcio"})]}):(0,r.jsxs)(l(),{href:"/",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg bg-primary text-primary-foreground",children:[(0,r.jsx)(f,{}),"In\xedcio",(0,r.jsx)("span",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full"})]}),a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(l(),{href:"/dashboard",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,r.jsx)(p,{})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Dashboard"})]}):(0,r.jsxs)(l(),{href:"/dashboard",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,r.jsx)(p,{}),"Dashboard"]}),a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(l(),{href:"/analytics",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,r.jsx)(g,{})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Analytics"})]}):(0,r.jsxs)(l(),{href:"/analytics",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,r.jsx)(g,{}),"Analytics"]})]})]}),(0,r.jsxs)("div",{className:"p-4",children:[!a&&(0,r.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"APLICA\xc7\xd5ES"}),(0,r.jsxs)("nav",{className:"space-y-1",children:[a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(l(),{href:"/chat",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,r.jsx)(v,{})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Chat"})]}):(0,r.jsxs)(l(),{href:"/chat",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,r.jsx)(v,{}),"Chat"]}),a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(l(),{href:"/calendar",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,r.jsx)(j,{})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Calend\xe1rio"})]}):(0,r.jsxs)(l(),{href:"/calendar",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,r.jsx)(j,{}),"Calend\xe1rio"]})]})]}),(0,r.jsxs)("div",{className:"p-4",children:[!a&&(0,r.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"OUTROS"}),(0,r.jsxs)("nav",{className:"space-y-1",children:[a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)("button",{className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Menu Multin\xedvel"})]}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>d("multilevel"),className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full text-left",children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})}),"Menu Multin\xedvel",(0,r.jsx)("svg",{className:"w-4 h-4 ml-auto transition-transform ".concat("multilevel"===e?"rotate-90":""),fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]}),"multilevel"===e&&(0,r.jsxs)("div",{className:"ml-6 mt-1 space-y-1",children:[(0,r.jsx)(l(),{href:"/posts",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:"Posts"}),(0,r.jsx)(l(),{href:"/details",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:"Detalhes"})]})]}),a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)(l(),{href:"/sobre",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Sobre"})]}):(0,r.jsxs)(l(),{href:"/sobre",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),"Sobre"]}),a?(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsx)("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})})}),(0,r.jsx)(h,{side:"right",className:"px-2 py-1 text-xs",children:"Link Externo"})]}):(0,r.jsxs)("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Link Externo",(0,r.jsx)("svg",{className:"w-4 h-4 ml-auto",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})]})]})]})})}},9434:(e,t,a)=>{a.d(t,{cn:()=>n});var r=a(2596),s=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}}]);