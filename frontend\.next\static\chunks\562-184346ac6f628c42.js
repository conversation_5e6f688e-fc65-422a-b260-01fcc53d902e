"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[562],{620:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,l=o>>>1;r<l;){var a=2*(r+1)-1,s=e[a],c=a+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[a]=n,r=a);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l,a=performance;t.unstable_now=function(){return a.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],p=1,d=null,v=3,h=!1,b=!1,m=!1,y="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function C(e){if(m=!1,j(e),!b)if(null!==r(u))b=!0,O();else{var t=r(f);null!==t&&I(C,t.startTime-e)}}var x=!1,P=-1,_=5,E=-1;function k(){return!(t.unstable_now()-E<_)}function M(){if(x){var e=t.unstable_now();E=e;var n=!0;try{e:{b=!1,m&&(m=!1,g(P),P=-1),h=!0;var i=v;try{t:{for(j(e),d=r(u);null!==d&&!(d.expirationTime>e&&k());){var a=d.callback;if("function"==typeof a){d.callback=null,v=d.priorityLevel;var s=a(d.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){d.callback=s,j(e),n=!0;break t}d===r(u)&&o(u),j(e)}else o(u);d=r(u)}if(null!==d)n=!0;else{var c=r(f);null!==c&&I(C,c.startTime-e),n=!1}}break e}finally{d=null,v=i,h=!1}}}finally{n?l():x=!1}}}if("function"==typeof w)l=function(){w(M)};else if("undefined"!=typeof MessageChannel){var S=new MessageChannel,T=S.port2;S.port1.onmessage=M,l=function(){T.postMessage(null)}}else l=function(){y(M,0)};function O(){x||(x=!0,l())}function I(e,n){P=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||h||(b=!0,O())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return v},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(v){case 1:case 2:case 3:var t=3;break;default:t=v}var n=v;v=t;try{return e()}finally{v=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=v;v=e;try{return t()}finally{v=n}},t.unstable_scheduleCallback=function(e,o,i){var l=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?l+i:l,e){case 1:var a=-1;break;case 2:a=250;break;case 5:a=0x3fffffff;break;case 4:a=1e4;break;default:a=5e3}return a=i+a,e={id:p++,callback:o,priorityLevel:e,startTime:i,expirationTime:a,sortIndex:-1},i>l?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(m?(g(P),P=-1):m=!0,I(C,i-l))):(e.sortIndex=a,n(u,e),b||h||(b=!0,O())),e},t.unstable_shouldYield=k,t.unstable_wrapCallback=function(e){var t=v;return function(){var n=v;v=t;try{return e.apply(this,arguments)}finally{v=n}}}},1933:(e,t,n)=>{e.exports=n(6500)},1949:(e,t,n)=>{let r,o,i,l,a;n.d(t,{B:()=>O,C:()=>ee,D:()=>et,E:()=>I,a:()=>S,b:()=>M,c:()=>eP,d:()=>eE,e:()=>ec,f:()=>eD,i:()=>E,u:()=>T});var s=n(3264),c=n(7431),u=n(2115),f=n.t(u,2),p=n(1933),d=n(5643);let v=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>l,subscribe:e=>(n.add(e),()=>n.delete(e))},l=t=e(r,o,i);return i},{useSyncExternalStoreWithSelector:h}=d,b=(e,t)=>{let n=(e=>e?v(e):v)(e),r=(e,r=t)=>(function(e,t=e=>e,n){let r=h(e.subscribe,e.getState,e.getInitialState,t,n);return u.useDebugValue(r),r})(n,e,r);return Object.assign(r,n),r};var m=n(5220),y=n.n(m),g=n(4342);let w=[];function j(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let o=0;o<r;o++)if(!n(e[o],t[o]))return!1;return!0}function C(e,t=null,n=!1,r={}){for(let o of(null===t&&(t=[e]),w))if(j(t,o.keys,o.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(o,"error"))throw o.error;if(Object.prototype.hasOwnProperty.call(o,"response"))return r.lifespan&&r.lifespan>0&&(o.timeout&&clearTimeout(o.timeout),o.timeout=setTimeout(o.remove,r.lifespan)),o.response;if(!n)throw o.promise}let o={keys:t,equal:r.equal,remove:()=>{let e=w.indexOf(o);-1!==e&&w.splice(e,1)},promise:("object"==typeof e&&"function"==typeof e.then?e:e(...t)).then(e=>{o.response=e,r.lifespan&&r.lifespan>0&&(o.timeout=setTimeout(o.remove,r.lifespan))}).catch(e=>o.error=e)};if(w.push(o),!n)throw o.promise}var x=n(5155),P=n(6354);function _(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}n(9509),f.act;let E=e=>e&&e.hasOwnProperty("current"),k=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),M=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?u.useLayoutEffect:u.useEffect;function S(e){let t=u.useRef(e);return M(()=>void(t.current=e),[e]),t}function T(){let e=(0,P.u5)(),t=(0,P.y3)();return u.useMemo(()=>n=>{let{children:r}=n,o=(0,P.Nz)(e,!0,e=>e.type===u.StrictMode)?u.StrictMode:u.Fragment;return(0,x.jsx)(o,{children:(0,x.jsx)(t,{children:r})})},[e,t])}function O(e){let{set:t}=e;return M(()=>(t(new Promise(()=>null)),()=>t(!1)),[t]),null}let I=(e=>((e=class extends u.Component{componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}constructor(...e){super(...e),this.state={error:!1}}}).getDerivedStateFromError=()=>({error:!0}),e))();function A(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}function L(e){var t;return null==(t=e.__r3f)?void 0:t.root.getState()}let z={obj:e=>e===Object(e)&&!z.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t){let n,{arrays:r="shallow",objects:o="reference",strict:i=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(typeof e!=typeof t||!!e!=!!t)return!1;if(z.str(e)||z.num(e)||z.boo(e))return e===t;let l=z.obj(e);if(l&&"reference"===o)return e===t;let a=z.arr(e);if(a&&"reference"===r)return e===t;if((a||l)&&e===t)return!0;for(n in e)if(!(n in t))return!1;if(l&&"shallow"===r&&"shallow"===o){for(n in i?t:e)if(!z.equ(e[n],t[n],{strict:i,objects:"reference"}))return!1}else for(n in i?t:e)if(e[n]!==t[n])return!1;if(z.und(n)){if(a&&0===e.length&&0===t.length||l&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}},R=["children","key","ref"];function H(e,t,n,r){let o=null==e?void 0:e.__r3f;return!o&&(o={root:t,type:n,parent:null,children:[],props:function(e){let t={};for(let n in e)R.includes(n)||(t[n]=e[n]);return t}(r),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=o)),o}function F(e,t){let n=e[t];if(!t.includes("-"))return{root:e,key:t,target:n};for(let o of(n=e,t.split("-"))){var r;t=o,e=n,n=null==(r=n)?void 0:r[t]}return{root:e,key:t,target:n}}let D=/-\d+$/;function q(e,t){if(z.str(t.props.attach)){if(D.test(t.props.attach)){let n=t.props.attach.replace(D,""),{root:r,key:o}=F(e.object,n);Array.isArray(r[o])||(r[o]=[])}let{root:n,key:r}=F(e.object,t.props.attach);t.previousAttach=n[r],n[r]=t.object}else z.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function N(e,t){if(z.str(t.props.attach)){let{root:n,key:r}=F(e.object,t.props.attach),o=t.previousAttach;void 0===o?delete n[r]:n[r]=o}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let B=[...R,"args","dispose","attach","object","onUpdate","dispose"],U=new Map,W=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],V=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function Y(e,t){var n,r;let o=e.__r3f,i=o&&_(o).getState(),l=null==o?void 0:o.eventCount;for(let n in t){let l=t[n];if(B.includes(n))continue;if(o&&V.test(n)){"function"==typeof l?o.handlers[n]=l:delete o.handlers[n],o.eventCount=Object.keys(o.handlers).length;continue}if(void 0===l)continue;let{root:a,key:c,target:u}=F(e,n);u instanceof s.zgK&&l instanceof s.zgK?u.mask=l.mask:u instanceof s.Q1f&&k(l)?u.set(l):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"function"==typeof u.copy&&null!=l&&l.constructor&&u.constructor===l.constructor?u.copy(l):null!==u&&"object"==typeof u&&"function"==typeof u.set&&Array.isArray(l)?"function"==typeof u.fromArray?u.fromArray(l):u.set(...l):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"number"==typeof l?"function"==typeof u.setScalar?u.setScalar(l):u.set(l):(a[c]=l,i&&!i.linear&&W.includes(c)&&null!=(r=a[c])&&r.isTexture&&a[c].format===s.GWd&&a[c].type===s.OUM&&(a[c].colorSpace=s.er$))}if(null!=o&&o.parent&&null!=i&&i.internal&&null!=(n=o.object)&&n.isObject3D&&l!==o.eventCount){let e=o.object,t=i.internal.interaction.indexOf(e);t>-1&&i.internal.interaction.splice(t,1),o.eventCount&&null!==e.raycast&&i.internal.interaction.push(e)}return o&&void 0===o.props.attach&&(o.object.isBufferGeometry?o.props.attach="geometry":o.object.isMaterial&&(o.props.attach="material")),o&&$(o),e}function $(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let n=null==(t=e.root)||null==t.getState?void 0:t.getState();n&&0===n.internal.frames&&n.invalidate()}let X=e=>null==e?void 0:e.isObject3D;function G(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function Z(e,t,n,r){let o=n.get(t);o&&(n.delete(t),0===n.size&&(e.delete(r),o.target.releasePointerCapture(r)))}let K=e=>!!(null!=e&&e.render),Q=u.createContext(null);function J(){let e=u.useContext(Q);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function ee(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e=>e,t=arguments.length>1?arguments[1]:void 0;return J()(e,t)}function et(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=J(),r=n.getState().internal.subscribe,o=S(e);return M(()=>r(o,t,n),[t,r,n]),null}let en=new WeakMap;function er(e,t){return function(n){let r;for(var o,i=arguments.length,l=Array(i>1?i-1:0),a=1;a<i;a++)l[a-1]=arguments[a];return"function"==typeof n&&(null==n||null==(o=n.prototype)?void 0:o.constructor)===n?(r=en.get(n))||(r=new n,en.set(n,r)):r=n,e&&e(r),Promise.all(l.map(e=>new Promise((n,o)=>r.load(e,e=>{X(null==e?void 0:e.scene)&&Object.assign(e,function(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}(e.scene)),n(e)},t,t=>o(Error("Could not load ".concat(e,": ").concat(null==t?void 0:t.message)))))))}}function eo(e,t,n,r){let o=Array.isArray(t)?t:[t],i=C(er(n,r),[e,...o],!1,{equal:z.equ});return Array.isArray(t)?i:i[0]}eo.preload=function(e,t,n){let r,o=Array.isArray(t)?t:[t];C(er(n),[e,...o],!0,r)},eo.clear=function(e,t){var n=[e,...Array.isArray(t)?t:[t]];if(void 0===n||0===n.length)w.splice(0,w.length);else{let e=w.find(e=>j(n,e.keys,e.equal));e&&e.remove()}};let ei={},el=/^three(?=[A-Z])/,ea=e=>"".concat(e[0].toUpperCase()).concat(e.slice(1)),es=0;function ec(e){if("function"==typeof e){let t="".concat(es++);return ei[t]=e,t}Object.assign(ei,e)}function eu(e,t){let n=ea(e),r=ei[n];if("primitive"!==e&&!r)throw Error("R3F: ".concat(n," is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively"));if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function ef(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?q(e.parent,e):X(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,$(e)}}function ep(e,t,n){let r=t.root.getState();if(e.parent||e.object===r.scene){if(!t.object){var o,i;let e=ei[ea(t.type)];t.object=null!=(o=t.props.object)?o:new e(...null!=(i=t.props.args)?i:[]),t.object.__r3f=t}if(Y(t.object,t.props),t.props.attach)q(e,t);else if(X(t.object)&&X(e.object)){let r=e.object.children.indexOf(null==n?void 0:n.object);if(n&&-1!==r){let n=e.object.children.indexOf(t.object);-1!==n?(e.object.children.splice(n,1),e.object.children.splice(n<r?r-1:r,0,t.object)):(t.object.parent=e.object,e.object.children.splice(r,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)ep(t,e);$(t)}}function ed(e,t){t&&(t.parent=e,e.children.push(t),ep(e,t))}function ev(e,t,n){if(!t||!n)return;t.parent=e;let r=e.children.indexOf(n);-1!==r?e.children.splice(r,0,t):e.children.push(t),ep(e,t,n)}function eh(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch(e){}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,g.unstable_scheduleCallback)(g.unstable_IdlePriority,t)}}function eb(e,t,n){if(!t)return;t.parent=null;let r=e.children.indexOf(t);-1!==r&&e.children.splice(r,1),t.props.attach?N(e,t):X(t.object)&&X(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{Z(n.capturedMap,t,e,r)})}(_(t),t.object));let o=null!==t.props.dispose&&!1!==n;for(let e=t.children.length-1;e>=0;e--){let n=t.children[e];eb(t,n,o)}t.children.length=0,delete t.object.__r3f,o&&"primitive"!==t.type&&"Scene"!==t.object.type&&eh(t.object),void 0===n&&$(t)}let em=[],ey=()=>{},eg={},ew=0,ej=function(e){let t=y()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:u.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,n){var r;return eu(e=ea(e)in ei?e:e.replace(el,""),t),"primitive"===e&&null!=(r=t.object)&&r.__r3f&&delete t.object.__r3f,H(t.object,n,e,t)},removeChild:eb,appendChild:ed,appendInitialChild:ed,insertBefore:ev,appendChildToContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&ed(n,t)},removeChildFromContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eb(n,t)},insertInContainerBefore(e,t,n){let r=e.getState().scene.__r3f;t&&n&&r&&ev(r,t,n)},getRootHostContext:()=>eg,getChildHostContext:()=>eg,commitUpdate(e,t,n,r,o){var i,l,a;eu(t,r);let s=!1;if("primitive"===e.type&&n.object!==r.object||(null==(i=r.args)?void 0:i.length)!==(null==(l=n.args)?void 0:l.length)?s=!0:null!=(a=r.args)&&a.some((e,t)=>{var r;return e!==(null==(r=n.args)?void 0:r[t])})&&(s=!0),s)em.push([e,{...r},o]);else{let t=function(e,t){let n={};for(let r in t)if(!B.includes(r)&&!z.equ(t[r],e.props[r]))for(let e in n[r]=t[r],t)e.startsWith("".concat(r,"-"))&&(n[e]=t[e]);for(let r in e.props){if(B.includes(r)||t.hasOwnProperty(r))continue;let{root:o,key:i}=F(e.object,r);if(o.constructor&&0===o.constructor.length){let e=function(e){let t=U.get(e.constructor);try{t||(t=new e.constructor,U.set(e.constructor,t))}catch(e){}return t}(o);z.und(e)||(n[i]=e[i])}else n[i]=0}return n}(e,r);Object.keys(t).length&&(Object.assign(e.props,t),Y(e.object,t))}(null===o.sibling||(4&o.flags)==0)&&function(){for(let[e]of em){let t=e.parent;if(t)for(let n of(e.props.attach?N(t,e):X(e.object)&&X(t.object)&&t.object.remove(e.object),e.children))n.props.attach?N(e,n):X(n.object)&&X(e.object)&&e.object.remove(n.object);e.isHidden&&ef(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&eh(e.object)}for(let[r,o,i]of em){r.props=o;let l=r.parent;if(l){let o=ei[ea(r.type)];r.object=null!=(e=r.props.object)?e:new o(...null!=(t=r.props.args)?t:[]),r.object.__r3f=r;var e,t,n=r.object;for(let e of[i,i.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(n);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=n);for(let e of(Y(r.object,r.props),r.props.attach?q(l,r):X(r.object)&&X(l.object)&&l.object.add(r.object),r.children))e.props.attach?q(r,e):X(e.object)&&X(r.object)&&r.object.add(e.object);$(r)}}em.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>H(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?N(e.parent,e):X(e.object)&&(e.object.visible=!1),e.isHidden=!0,$(e)}},unhideInstance:ef,createTextInstance:ey,hideTextInstance:ey,unhideTextInstance:ey,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:u.createContext(null),setCurrentUpdatePriority(e){ew=e},getCurrentUpdatePriority:()=>ew,resolveUpdatePriority(){var e;if(0!==ew)return ew;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return p.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return p.ContinuousEventPriority;default:return p.DefaultEventPriority}},resetFormInstance(){}}),eC=new Map,ex={objects:"shallow",strict:!1};function eP(e){let t,n,r=eC.get(e),o=null==r?void 0:r.fiber,i=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let l="function"==typeof reportError?reportError:console.error,a=i||((e,t)=>{let n,r,o=(n=(n,r)=>{let o,i=new s.Pq0,l=new s.Pq0,a=new s.Pq0;function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r().camera,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r().size,{width:o,height:s,top:c,left:u}=n,f=o/s;t.isVector3?a.copy(t):a.set(...t);let p=e.getWorldPosition(i).distanceTo(a);if(e&&e.isOrthographicCamera)return{width:o/e.zoom,height:s/e.zoom,top:c,left:u,factor:1,distance:p,aspect:f};{let t=2*Math.tan(e.fov*Math.PI/180/2)*p,n=o/s*t;return{width:n,height:t,top:c,left:u,factor:o/n,distance:p,aspect:f}}}let f=e=>n(t=>({performance:{...t.performance,current:e}})),p=new s.I9Y;return{set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return e(r(),t)},advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new s.zD7,pointer:p,mouse:p,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();o&&clearTimeout(o),e.performance.current!==e.performance.min&&f(e.performance.min),o=setTimeout(()=>f(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:c},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=r().camera,s={width:e,height:t,top:o,left:i};n(e=>({size:s,viewport:{...e.viewport,...c(a,l,s)}}))},setDpr:e=>n(t=>{let n=A(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"always",t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:u.createRef(),active:!1,frames:0,priority:0,subscribe:(e,t,n)=>{let o=r().internal;return o.priority=o.priority+ +(t>0),o.subscribers.push({ref:e,priority:t,store:n}),o.subscribers=o.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}}})?b(n,r):b,i=o.getState(),l=i.size,a=i.viewport.dpr,c=i.camera;return o.subscribe(()=>{let{camera:e,size:t,viewport:n,gl:r,set:i}=o.getState();if(t.width!==l.width||t.height!==l.height||n.dpr!==a){l=t,a=n.dpr,function(e,t){!e.manual&&(e&&e.isOrthographicCamera?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}(e,t),n.dpr>0&&r.setPixelRatio(n.dpr);let o="undefined"!=typeof HTMLCanvasElement&&r.domElement instanceof HTMLCanvasElement;r.setSize(t.width,t.height,o)}e!==c&&(c=e,i(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),o.subscribe(t=>e(t)),o})(eR,eH),f=o||ej.createContainer(a,p.ConcurrentRoot,null,!1,null,"",l,l,l,null);r||eC.set(e,{fiber:f,store:a});let d=!1,v=null;return{async configure(){var r,o;let i,l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v=new Promise(e=>i=e);let{gl:u,size:f,scene:p,events:h,onCreated:b,shadows:m=!1,linear:y=!1,flat:g=!1,legacy:w=!1,orthographic:j=!1,frameloop:C="always",dpr:x=[1,2],performance:P,raycaster:_,camera:E,onPointerMissed:k}=l,M=a.getState(),S=M.gl;if(!M.gl){let t={canvas:e,powerPreference:"high-performance",antialias:!0,alpha:!0},n="function"==typeof u?await u(t):u;S=K(n)?n:new c.WebGLRenderer({...t,...u}),M.set({gl:S})}let T=M.raycaster;T||M.set({raycaster:T=new s.tBo});let{params:O,...I}=_||{};if(z.equ(I,T,ex)||Y(T,{...I}),z.equ(O,T.params,ex)||Y(T,{params:{...T.params,...O}}),!M.camera||M.camera===n&&!z.equ(n,E,ex)){n=E;let e=null==E?void 0:E.isCamera,t=e?E:j?new s.qUd(0,0,0,0,.1,1e3):new s.ubm(75,0,.1,1e3);!e&&(t.position.z=5,E&&(Y(t,E),!t.manual&&("aspect"in E||"left"in E||"right"in E||"bottom"in E||"top"in E)&&(t.manual=!0,t.updateProjectionMatrix())),M.camera||null!=E&&E.rotation||t.lookAt(0,0,0)),M.set({camera:t}),T.camera=t}if(!M.scene){let e;null!=p&&p.isScene?H(e=p,a,"",{}):(H(e=new s.Z58,a,"",{}),p&&Y(e,p)),M.set({scene:e})}h&&!M.events.handlers&&M.set({events:h(a)});let L=function(e,t){if(!t&&"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:n,top:r,left:o}=e.parentElement.getBoundingClientRect();return{width:t,height:n,top:r,left:o}}return!t&&"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...t}}(e,f);if(z.equ(L,M.size,ex)||M.setSize(L.width,L.height,L.top,L.left),x&&M.viewport.dpr!==A(x)&&M.setDpr(x),M.frameloop!==C&&M.setFrameloop(C),M.onPointerMissed||M.set({onPointerMissed:k}),P&&!z.equ(P,M.performance,ex)&&M.set(e=>({performance:{...e.performance,...P}})),!M.xr){let e=(e,t)=>{let n=a.getState();"never"!==n.frameloop&&eH(e,!0,n,t)},t=()=>{let t=a.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||eR(t)},n={connect(){let e=a.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=a.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(r=S.xr)?void 0:r.addEventListener)&&n.connect(),M.set({xr:n})}if(S.shadowMap){let e=S.shadowMap.enabled,t=S.shadowMap.type;if(S.shadowMap.enabled=!!m,z.boo(m))S.shadowMap.type=s.Wk7;else if(z.str(m)){let e={basic:s.bTm,percentage:s.QP0,soft:s.Wk7,variance:s.RyA};S.shadowMap.type=null!=(o=e[m])?o:s.Wk7}else z.obj(m)&&Object.assign(S.shadowMap,m);(e!==S.shadowMap.enabled||t!==S.shadowMap.type)&&(S.shadowMap.needsUpdate=!0)}return s.ppV.enabled=!w,d||(S.outputColorSpace=y?s.Zr2:s.er$,S.toneMapping=g?s.y_p:s.FV),M.legacy!==w&&M.set(()=>({legacy:w})),M.linear!==y&&M.set(()=>({linear:y})),M.flat!==g&&M.set(()=>({flat:g})),!u||z.fun(u)||K(u)||z.equ(u,S,ex)||Y(S,u),t=b,d=!0,i(),this},render(n){return d||v||this.configure(),v.then(()=>{ej.updateContainer((0,x.jsx)(e_,{store:a,children:n,onCreated:t,rootElement:e}),f,null,()=>void 0)}),a},unmount(){eE(e)}}}function e_(e){let{store:t,children:n,onCreated:r,rootElement:o}=e;return M(()=>{let e=t.getState();e.set(e=>({internal:{...e.internal,active:!0}})),r&&r(e),t.getState().events.connected||null==e.events.connect||e.events.connect(o)},[]),(0,x.jsx)(Q.Provider,{value:t,children:n})}function eE(e,t){let n=eC.get(e),r=null==n?void 0:n.fiber;if(r){let o=null==n?void 0:n.store.getState();o&&(o.internal.active=!1),ej.updateContainer(null,r,null,()=>{o&&setTimeout(()=>{try{null==o.events.disconnect||o.events.disconnect(),null==(n=o.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(i=o.gl)||null==i.forceContextLoss||i.forceContextLoss(),null!=(l=o.gl)&&l.xr&&o.xr.disconnect();var n,r,i,l,a=o.scene;for(let e in"Scene"!==a.type&&(null==a.dispose||a.dispose()),a){let t=a[e];(null==t?void 0:t.type)!=="Scene"&&(null==t||null==t.dispose||t.dispose())}eC.delete(e),t&&t(e)}catch(e){}},500)})}}let ek=new Set,eM=new Set,eS=new Set;function eT(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function eO(e,t){switch(e){case"before":return eT(ek,t);case"after":return eT(eM,t);case"tail":return eT(eS,t)}}function eI(e,t,n){let i=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(i=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),r=t.internal.subscribers;for(let e=0;e<r.length;e++)(o=r[e]).ref.current(o.store.getState(),i,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let eA=!1,eL=!1;function ez(e){for(let n of(l=requestAnimationFrame(ez),eA=!0,i=0,eO("before",e),eL=!0,eC.values())){var t;(a=n.store.getState()).internal.active&&("always"===a.frameloop||a.internal.frames>0)&&!(null!=(t=a.gl.xr)&&t.isPresenting)&&(i+=eI(e,a))}if(eL=!1,eO("after",e),0===i)return eO("tail",e),eA=!1,cancelAnimationFrame(l)}function eR(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(!e)return eC.forEach(e=>eR(e.store.getState(),n));(null==(t=e.gl.xr)||!t.isPresenting)&&e.internal.active&&"never"!==e.frameloop&&(n>1?e.internal.frames=Math.min(60,e.internal.frames+n):eL?e.internal.frames=2:e.internal.frames=1,eA||(eA=!0,requestAnimationFrame(ez)))}function eH(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;if(t&&eO("before",e),n)eI(e,n,r);else for(let t of eC.values())eI(e,t.store.getState());t&&eO("after",e)}let eF={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eD(e){let{handlePointer:t}=function(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject.__r3f;if(n.hovered.delete(G(e)),null!=r&&r.eventCount){let n=r.handlers,o={...e,intersections:t};null==n.onPointerOut||n.onPointerOut(o),null==n.onPointerLeave||n.onPointerLeave(o)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(o){switch(o){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(i){let{onPointerMissed:l,internal:a}=e.getState();a.lastEvent.current=i;let c="onPointerMove"===o,u="onClick"===o||"onContextMenu"===o||"onDoubleClick"===o,f=function(t,n){let r=e.getState(),o=new Set,i=[],l=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<l.length;e++){let t=L(l[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let a=l.flatMap(function(e){let n=L(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=L(e.object),r=L(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=G(e);return!o.has(t)&&(o.add(t),!0)});for(let e of(r.events.filter&&(a=r.events.filter(a,r)),a)){let t=e.object;for(;t;){var s;null!=(s=t.__r3f)&&s.eventCount&&i.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())o.has(G(e.intersection))||i.push(e.intersection);return i}(i,c?t:void 0),p=u?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],o=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+o*o))}(i):0;"onPointerDown"===o&&(a.initialClick=[i.offsetX,i.offsetY],a.initialHits=f.map(e=>e.eventObject)),u&&!f.length&&p<=2&&(r(i,a.interaction),l&&l(i)),c&&n(f),!function(e,t,r,o){if(e.length){let i={stopped:!1};for(let l of e){let a=L(l.object);if(a||l.object.traverseAncestors(e=>{let t=L(e);if(t)return a=t,!1}),a){let{raycaster:c,pointer:u,camera:f,internal:p}=a,d=new s.Pq0(u.x,u.y,0).unproject(f),v=e=>{var t,n;return null!=(t=null==(n=p.capturedMap.get(e))?void 0:n.has(l.eventObject))&&t},h=e=>{let n={intersection:l,target:t.target};p.capturedMap.has(e)?p.capturedMap.get(e).set(l.eventObject,n):p.capturedMap.set(e,new Map([[l.eventObject,n]])),t.target.setPointerCapture(e)},b=e=>{let t=p.capturedMap.get(e);t&&Z(p.capturedMap,l.eventObject,t,e)},m={};for(let e in t){let n=t[e];"function"!=typeof n&&(m[e]=n)}let y={...l,...m,pointer:u,intersections:e,stopped:i.stopped,delta:r,unprojectedPoint:d,ray:c.ray,camera:f,stopPropagation(){let r="pointerId"in t&&p.capturedMap.get(t.pointerId);(!r||r.has(l.eventObject))&&(y.stopped=i.stopped=!0,p.hovered.size&&Array.from(p.hovered.values()).find(e=>e.eventObject===l.eventObject)&&n([...e.slice(0,e.indexOf(l)),l]))},target:{hasPointerCapture:v,setPointerCapture:h,releasePointerCapture:b},currentTarget:{hasPointerCapture:v,setPointerCapture:h,releasePointerCapture:b},nativeEvent:t};if(o(y),!0===i.stopped)break}}}}(f,i,p,function(e){let t=e.eventObject,n=t.__r3f;if(!(null!=n&&n.eventCount))return;let l=n.handlers;if(c){if(l.onPointerOver||l.onPointerEnter||l.onPointerOut||l.onPointerLeave){let t=G(e),n=a.hovered.get(t);n?n.stopped&&e.stopPropagation():(a.hovered.set(t,e),null==l.onPointerOver||l.onPointerOver(e),null==l.onPointerEnter||l.onPointerEnter(e))}null==l.onPointerMove||l.onPointerMove(e)}else{let n=l[o];n?(!u||a.initialHits.includes(t))&&(r(i,a.interaction.filter(e=>!a.initialHits.includes(e))),n(e)):u&&a.initialHits.includes(t)&&r(i,a.interaction.filter(e=>!a.initialHits.includes(e)))}})}}}}(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(eF).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{let{set:n,events:r}=e.getState();if(null==r.disconnect||r.disconnect(),n(e=>({events:{...e.events,connected:t}})),r.handlers)for(let e in r.handlers){let n=r.handlers[e],[o,i]=eF[e];t.addEventListener(o,n,{passive:i})}},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){if(n.handlers)for(let e in n.handlers){let t=n.handlers[e],[r]=eF[e];n.connected.removeEventListener(r,t)}t(e=>({events:{...e.events,connected:void 0}}))}}}}},4342:(e,t,n)=>{e.exports=n(7319)},5220:(e,t,n)=>{e.exports=n(1724)},5643:(e,t,n)=>{e.exports=n(6115)},6115:(e,t,n)=>{var r=n(2115),o=n(1414),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=o.useSyncExternalStore,a=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var f=a(null);if(null===f.current){var p={hasValue:!1,value:null};f.current=p}else p=f.current;var d=l(e,(f=c(function(){function e(e){if(!s){if(s=!0,l=e,e=r(e),void 0!==o&&p.hasValue){var t=p.value;if(o(t,e))return a=t}return a=e}if(t=a,i(l,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(l=e,t):(l=e,a=n)}var l,a,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],f[1]);return s(function(){p.hasValue=!0,p.value=d},[d]),u(d),d}},6354:(e,t,n)=>{n.d(t,{Af:()=>a,Nz:()=>o,u5:()=>s,y3:()=>f});var r=n(2115);function o(e,t,n){if(!e)return;if(!0===n(e))return e;let r=t?e.return:e.child;for(;r;){let e=o(r,t,n);if(e)return e;r=t?null:r.sibling}}function i(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?r.useLayoutEffect:r.useEffect;let l=i(r.createContext(null));class a extends r.Component{render(){return r.createElement(l.Provider,{value:this._reactInternals},this.props.children)}}function s(){let e=r.useContext(l);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=r.useId();return r.useMemo(()=>{for(let n of[e,null==e?void 0:e.alternate]){if(!n)continue;let e=o(n,!1,e=>{let n=e.memoizedState;for(;n;){if(n.memoizedState===t)return!0;n=n.next}});if(e)return e}},[e,t])}let c=Symbol.for("react.context"),u=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===c;function f(){let e=function(){let e=s(),[t]=r.useState(()=>new Map);t.clear();let n=e;for(;n;){let e=n.type;u(e)&&e!==l&&!t.has(e)&&t.set(e,r.use(i(e))),n=n.return}return t}();return r.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>o=>r.createElement(t,null,r.createElement(n.Provider,{...o,value:e.get(n)})),e=>r.createElement(a,{...e})),[e])}},6500:(e,t)=>{t.ConcurrentRoot=1,t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},7319:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,l=o>>>1;r<l;){var a=2*(r+1)-1,s=e[a],c=a+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[a]=n,r=a);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l,a=performance;t.unstable_now=function(){return a.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],p=1,d=null,v=3,h=!1,b=!1,m=!1,y="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function C(e){if(m=!1,j(e),!b)if(null!==r(u))b=!0,O();else{var t=r(f);null!==t&&I(C,t.startTime-e)}}var x=!1,P=-1,_=5,E=-1;function k(){return!(t.unstable_now()-E<_)}function M(){if(x){var e=t.unstable_now();E=e;var n=!0;try{e:{b=!1,m&&(m=!1,g(P),P=-1),h=!0;var i=v;try{t:{for(j(e),d=r(u);null!==d&&!(d.expirationTime>e&&k());){var a=d.callback;if("function"==typeof a){d.callback=null,v=d.priorityLevel;var s=a(d.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){d.callback=s,j(e),n=!0;break t}d===r(u)&&o(u),j(e)}else o(u);d=r(u)}if(null!==d)n=!0;else{var c=r(f);null!==c&&I(C,c.startTime-e),n=!1}}break e}finally{d=null,v=i,h=!1}}}finally{n?l():x=!1}}}if("function"==typeof w)l=function(){w(M)};else if("undefined"!=typeof MessageChannel){var S=new MessageChannel,T=S.port2;S.port1.onmessage=M,l=function(){T.postMessage(null)}}else l=function(){y(M,0)};function O(){x||(x=!0,l())}function I(e,n){P=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||h||(b=!0,O())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return v},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(v){case 1:case 2:case 3:var t=3;break;default:t=v}var n=v;v=t;try{return e()}finally{v=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=v;v=e;try{return t()}finally{v=n}},t.unstable_scheduleCallback=function(e,o,i){var l=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?l+i:l,e){case 1:var a=-1;break;case 2:a=250;break;case 5:a=0x3fffffff;break;case 4:a=1e4;break;default:a=5e3}return a=i+a,e={id:p++,callback:o,priorityLevel:e,startTime:i,expirationTime:a,sortIndex:-1},i>l?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(m?(g(P),P=-1):m=!0,I(C,i-l))):(e.sortIndex=a,n(u,e),b||h||(b=!0,O())),e},t.unstable_shouldYield=k,t.unstable_wrapCallback=function(e){var t=v;return function(){var n=v;v=t;try{return e.apply(this,arguments)}finally{v=n}}}},7558:(e,t,n)=>{n.d(t,{Hl:()=>f});var r=n(1949),o=n(2115),i=n(7431);function l(e,t){let n;return(...r)=>{window.clearTimeout(n),n=window.setTimeout(()=>e(...r),t)}}let a=["x","y","top","bottom","left","right","width","height"];var s=n(6354),c=n(5155);function u(e){let{ref:t,children:n,fallback:s,resize:u,style:f,gl:p,events:d=r.f,eventSource:v,eventPrefix:h,shadows:b,linear:m,flat:y,legacy:g,orthographic:w,frameloop:j,dpr:C,performance:x,raycaster:P,camera:_,scene:E,onPointerMissed:k,onCreated:M,...S}=e;o.useMemo(()=>(0,r.e)(i),[]);let T=(0,r.u)(),[O,I]=function({debounce:e,scroll:t,polyfill:n,offsetSize:r}={debounce:0,scroll:!1,offsetSize:!1}){var i,s,c;let u=n||("undefined"==typeof window?class{}:window.ResizeObserver);if(!u)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[f,p]=(0,o.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),d=(0,o.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:f,orientationHandler:null}),v=e?"number"==typeof e?e:e.scroll:null,h=e?"number"==typeof e?e:e.resize:null,b=(0,o.useRef)(!1);(0,o.useEffect)(()=>(b.current=!0,()=>void(b.current=!1)));let[m,y,g]=(0,o.useMemo)(()=>{let e=()=>{let e,t;if(!d.current.element)return;let{left:n,top:o,width:i,height:l,bottom:s,right:c,x:u,y:f}=d.current.element.getBoundingClientRect(),v={left:n,top:o,width:i,height:l,bottom:s,right:c,x:u,y:f};d.current.element instanceof HTMLElement&&r&&(v.height=d.current.element.offsetHeight,v.width=d.current.element.offsetWidth),Object.freeze(v),b.current&&(e=d.current.lastBounds,t=v,!a.every(n=>e[n]===t[n]))&&p(d.current.lastBounds=v)};return[e,h?l(e,h):e,v?l(e,v):e]},[p,r,v,h]);function w(){d.current.scrollContainers&&(d.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",g,!0)),d.current.scrollContainers=null),d.current.resizeObserver&&(d.current.resizeObserver.disconnect(),d.current.resizeObserver=null),d.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",d.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",d.current.orientationHandler))}function j(){d.current.element&&(d.current.resizeObserver=new u(g),d.current.resizeObserver.observe(d.current.element),t&&d.current.scrollContainers&&d.current.scrollContainers.forEach(e=>e.addEventListener("scroll",g,{capture:!0,passive:!0})),d.current.orientationHandler=()=>{g()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",d.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",d.current.orientationHandler))}return i=g,s=!!t,(0,o.useEffect)(()=>{if(s)return window.addEventListener("scroll",i,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",i,!0)},[i,s]),c=y,(0,o.useEffect)(()=>(window.addEventListener("resize",c),()=>void window.removeEventListener("resize",c)),[c]),(0,o.useEffect)(()=>{w(),j()},[t,g,y]),(0,o.useEffect)(()=>w,[]),[e=>{e&&e!==d.current.element&&(w(),d.current.element=e,d.current.scrollContainers=function e(t){let n=[];if(!t||t===document.body)return n;let{overflow:r,overflowX:o,overflowY:i}=window.getComputedStyle(t);return[r,o,i].some(e=>"auto"===e||"scroll"===e)&&n.push(t),[...n,...e(t.parentElement)]}(e),j())},f,m]}({scroll:!0,debounce:{scroll:50,resize:0},...u}),A=o.useRef(null),L=o.useRef(null);o.useImperativeHandle(t,()=>A.current);let z=(0,r.a)(k),[R,H]=o.useState(!1),[F,D]=o.useState(!1);if(R)throw R;if(F)throw F;let q=o.useRef(null);(0,r.b)(()=>{let e=A.current;I.width>0&&I.height>0&&e&&(q.current||(q.current=(0,r.c)(e)),async function(){await q.current.configure({gl:p,scene:E,events:d,shadows:b,linear:m,flat:y,legacy:g,orthographic:w,frameloop:j,dpr:C,performance:x,raycaster:P,camera:_,size:I,onPointerMissed:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return null==z.current?void 0:z.current(...t)},onCreated:e=>{null==e.events.connect||e.events.connect(v?(0,r.i)(v)?v.current:v:L.current),h&&e.setEvents({compute:(e,t)=>{let n=e[h+"X"],r=e[h+"Y"];t.pointer.set(n/t.size.width*2-1,-(2*(r/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==M||M(e)}}),q.current.render((0,c.jsx)(T,{children:(0,c.jsx)(r.E,{set:D,children:(0,c.jsx)(o.Suspense,{fallback:(0,c.jsx)(r.B,{set:H}),children:null!=n?n:null})})}))}())}),o.useEffect(()=>{let e=A.current;if(e)return()=>(0,r.d)(e)},[]);let N=v?"none":"auto";return(0,c.jsx)("div",{ref:L,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:N,...f},...S,children:(0,c.jsx)("div",{ref:O,style:{width:"100%",height:"100%"},children:(0,c.jsx)("canvas",{ref:A,style:{display:"block"},children:s})})})}function f(e){return(0,c.jsx)(s.Af,{children:(0,c.jsx)(u,{...e})})}n(1933),n(5220),n(4342)},8247:(e,t,n)=>{e.exports=n(620)}}]);