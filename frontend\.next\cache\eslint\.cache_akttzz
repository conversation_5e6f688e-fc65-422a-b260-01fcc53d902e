[{"C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\analytics\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\dashboard\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\login\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\profile\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\AppSidebar.tsx": "9", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "10", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\SupabaseSignIn.tsx": "11", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Button.tsx": "12", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-183.tsx": "13", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-356.tsx": "14", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-581.tsx": "15", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\debug\\SupabaseDebug.tsx": "16", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\info-menu.tsx": "17", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\layout\\ConditionalLayout.tsx": "18", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\LoginDemo.tsx": "19", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\logo.tsx": "20", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Navbar.tsx": "21", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\notification-menu.tsx": "22", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\PortfolioBalance.tsx": "23", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ThemeToggle.tsx": "24", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar-upload.tsx": "25", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar.tsx": "26", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\button.tsx": "27", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\dropdown-menu.tsx": "28", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\label.tsx": "29", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\navigation-menu.tsx": "30", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\popover.tsx": "31", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\sign-in-flow-1.tsx": "32", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\switch.tsx": "33", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\tooltip.tsx": "34", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\user-menu.tsx": "35", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\ProfileContext.tsx": "36", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\SidebarContext.tsx": "37", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useAuth.ts": "38", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useLocalStorage.ts": "39", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useProfile.ts": "40", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useTheme.ts": "41", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\config.ts": "42", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\supabase.ts": "43", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\utils.ts": "44", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\types\\index.ts": "45", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\utils\\index.ts": "46"}, {"size": 579, "mtime": 1753427506429, "results": "47", "hashOfConfig": "48"}, {"size": 1136, "mtime": 1753427525524, "results": "49", "hashOfConfig": "48"}, {"size": 526, "mtime": 1753425362112, "results": "50", "hashOfConfig": "48"}, {"size": 411, "mtime": 1753438838504, "results": "51", "hashOfConfig": "48"}, {"size": 1271, "mtime": 1753430644195, "results": "52", "hashOfConfig": "48"}, {"size": 223, "mtime": 1753428522935, "results": "53", "hashOfConfig": "48"}, {"size": 102, "mtime": 1753427531887, "results": "54", "hashOfConfig": "48"}, {"size": 14656, "mtime": 1753435011916, "results": "55", "hashOfConfig": "48"}, {"size": 16647, "mtime": 1753438949298, "results": "56", "hashOfConfig": "48"}, {"size": 1089, "mtime": 1753429458940, "results": "57", "hashOfConfig": "48"}, {"size": 24842, "mtime": 1753437863099, "results": "58", "hashOfConfig": "48"}, {"size": 1917, "mtime": 1753418806563, "results": "59", "hashOfConfig": "48"}, {"size": 1492, "mtime": 1753420281523, "results": "60", "hashOfConfig": "48"}, {"size": 594, "mtime": 1753424177462, "results": "61", "hashOfConfig": "48"}, {"size": 4444, "mtime": 1753419151447, "results": "62", "hashOfConfig": "48"}, {"size": 0, "mtime": 1753433329752, "results": "63", "hashOfConfig": "48"}, {"size": 2012, "mtime": 1753429650737, "results": "64", "hashOfConfig": "48"}, {"size": 2616, "mtime": 1753433736749, "results": "65", "hashOfConfig": "48"}, {"size": 242, "mtime": 1753427369704, "results": "66", "hashOfConfig": "48"}, {"size": 293, "mtime": 1753420738208, "results": "67", "hashOfConfig": "48"}, {"size": 1363, "mtime": 1753438821653, "results": "68", "hashOfConfig": "48"}, {"size": 4733, "mtime": 1753429671725, "results": "69", "hashOfConfig": "48"}, {"size": 6097, "mtime": 1753457316734, "results": "70", "hashOfConfig": "48"}, {"size": 1589, "mtime": 1753434084613, "results": "71", "hashOfConfig": "48"}, {"size": 5404, "mtime": 1753457177590, "results": "72", "hashOfConfig": "48"}, {"size": 1109, "mtime": 1753419126418, "results": "73", "hashOfConfig": "48"}, {"size": 1867, "mtime": 1753419126431, "results": "74", "hashOfConfig": "48"}, {"size": 9326, "mtime": 1753419126465, "results": "75", "hashOfConfig": "48"}, {"size": 595, "mtime": 1753420281548, "results": "76", "hashOfConfig": "48"}, {"size": 6606, "mtime": 1753419126498, "results": "77", "hashOfConfig": "48"}, {"size": 1827, "mtime": 1753419126503, "results": "78", "hashOfConfig": "48"}, {"size": 32007, "mtime": 1753457299287, "results": "79", "hashOfConfig": "48"}, {"size": 1036, "mtime": 1753420281543, "results": "80", "hashOfConfig": "48"}, {"size": 1891, "mtime": 1753424177494, "results": "81", "hashOfConfig": "48"}, {"size": 6583, "mtime": 1753438885270, "results": "82", "hashOfConfig": "48"}, {"size": 7716, "mtime": 1753456614510, "results": "83", "hashOfConfig": "48"}, {"size": 1580, "mtime": 1753425131816, "results": "84", "hashOfConfig": "48"}, {"size": 4148, "mtime": 1753456651697, "results": "85", "hashOfConfig": "48"}, {"size": 1081, "mtime": 1753455879431, "results": "86", "hashOfConfig": "48"}, {"size": 214, "mtime": 1753433704921, "results": "87", "hashOfConfig": "48"}, {"size": 1375, "mtime": 1753424805039, "results": "88", "hashOfConfig": "48"}, {"size": 495, "mtime": 1753418795255, "results": "89", "hashOfConfig": "48"}, {"size": 435, "mtime": 1753428387847, "results": "90", "hashOfConfig": "48"}, {"size": 166, "mtime": 1753419075615, "results": "91", "hashOfConfig": "48"}, {"size": 296, "mtime": 1753418781941, "results": "92", "hashOfConfig": "48"}, {"size": 964, "mtime": 1753456634130, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1pczg4f", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\AppSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\SupabaseSignIn.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-183.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-356.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-581.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\debug\\SupabaseDebug.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\info-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\layout\\ConditionalLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\LoginDemo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\logo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\notification-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\PortfolioBalance.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar-upload.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\sign-in-flow-1.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\user-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\ProfileContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\SidebarContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useLocalStorage.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useProfile.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useTheme.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\utils\\index.ts", [], []]