(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1824:(e,t,a)=>{"use strict";a.d(t,{x:()=>s.x});var s=a(6064)},2099:(e,t,a)=>{"use strict";a.d(t,{N:()=>s});let s=(0,a(5647).UU)("https://dpofnwutgpbwylwtbgnv.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwb2Zud3V0Z3Bid3lsd3RiZ252Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNDQyOTMsImV4cCI6MjA2ODgyMDI5M30.lE40lk7gBuq77mqJUezZYxUmHSeRSC6kOTVjwvP2hLw")},5876:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(5155),r=a(2115),n=a(5695),l=a(6681);function i(e){let{children:t}=e,{user:a,isLoading:i}=(0,l.A)(),o=(0,n.useRouter)();return((0,r.useEffect)(()=>{i||a||o.push("/login")},[a,i,o]),i)?(0,s.jsx)("div",{className:"h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):a?(0,s.jsx)(s.Fragment,{children:t}):null}},6064:(e,t,a)=>{"use strict";a.d(t,{H:()=>o,x:()=>d});var s=a(5155),r=a(2115),n=a(2099),l=a(6681);let i=(0,r.createContext)(void 0),o=e=>{let{children:t}=e,{user:a}=(0,l.A)(),[o,d]=(0,r.useState)(null),[c,u]=(0,r.useState)(!0),[x,m]=(0,r.useState)(null),[h,f]=(0,r.useState)(!1),p=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!a){d(null),u(!1),f(!1);return}if(!e&&o&&h)return void u(!1);try{u(!0),m(null);let{data:e,error:r}=await n.N.from("profiles").select("*").eq("id",a.id).single();if(r&&"PGRST116"!==r.code)throw r;if(e)d(e);else{var t,s;let e={id:a.id,email:a.email||"",full_name:(null==(t=a.user_metadata)?void 0:t.full_name)||"",avatar_url:(null==(s=a.user_metadata)?void 0:s.avatar_url)||"",created_at:a.created_at},{data:r,error:l}=await n.N.from("profiles").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(l)throw l;d(r)}f(!0)}catch(e){console.error("Erro ao carregar perfil:",e),m("Erro ao carregar perfil do usu\xe1rio"),f(!1)}finally{u(!1)}},[a,o,h]),g=async()=>{await p(!0)},v=async e=>{if(!a||!o)throw Error("Usu\xe1rio n\xe3o autenticado ou perfil n\xe3o carregado");try{m(null);let t={...e,updated_at:new Date().toISOString()},{data:s,error:r}=await n.N.from("profiles").update(t).eq("id",a.id).select().single();if(r)throw r;return d(s),{success:!0,data:s}}catch(t){console.error("Erro ao atualizar perfil:",t);let e="Erro ao salvar altera\xe7\xf5es";return m(e),{success:!1,error:e}}},j=async e=>{if(!a)throw Error("Usu\xe1rio n\xe3o autenticado");try{if(m(null),!e.type.startsWith("image/"))throw Error("Por favor, selecione apenas arquivos de imagem");if(e.size>5242880)throw Error("A imagem deve ter no m\xe1ximo 5MB");let t=e.name.split(".").pop(),s="".concat(a.id,"-").concat(Date.now(),".").concat(t),r="avatars/".concat(s);if(null==o?void 0:o.avatar_url){let e=o.avatar_url.split("/").pop();e&&await n.N.storage.from("avatars").remove(["avatars/".concat(e)])}let{error:l}=await n.N.storage.from("avatars").upload(r,e);if(l)throw l;let{data:{publicUrl:i}}=n.N.storage.from("avatars").getPublicUrl(r),d=await v({avatar_url:i});if(d.success)return{success:!0,url:i};throw Error(d.error)}catch(t){console.error("Erro ao fazer upload do avatar:",t);let e=t instanceof Error?t.message:"Erro ao fazer upload da imagem";return m(e),{success:!1,error:e}}},b=async()=>{if(a&&(null==o?void 0:o.avatar_url))try{m(null);let e=o.avatar_url.split("/").pop();return e&&await n.N.storage.from("avatars").remove(["avatars/".concat(e)]),await v({avatar_url:""})}catch(t){console.error("Erro ao remover avatar:",t);let e="Erro ao remover avatar";return m(e),{success:!1,error:e}}};return(0,r.useEffect)(()=>{a?(f(!1),p()):(d(null),u(!1),f(!1))},[null==a?void 0:a.id,a,p]),(0,s.jsx)(i.Provider,{value:{profile:o,isLoading:c,error:x,updateProfile:v,uploadAvatar:j,removeAvatar:b,clearError:()=>m(null),refreshProfile:g},children:t})},d=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useProfile deve ser usado dentro de um ProfileProvider");return e}},6234:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,2734,23)),Promise.resolve().then(a.t.bind(a,3259,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,9389))},6681:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(2115),r=a(2099);let n=()=>{let[e,t]=(0,s.useState)(null),[a,n]=(0,s.useState)(null),[l,i]=(0,s.useState)(!0),[o,d]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{let{data:{session:e},error:a}=await r.N.auth.getSession();if(a)d({message:a.message});else{var s;n(e),t(null!=(s=null==e?void 0:e.user)?s:null)}i(!1)})();let{data:{subscription:e}}=r.N.auth.onAuthStateChange(async(e,a)=>{var s;n(a),t(null!=(s=null==a?void 0:a.user)?s:null),i(!1),"SIGNED_OUT"===e&&d(null)});return()=>e.unsubscribe()},[]),{user:e,session:a,isLoading:l,error:o,signInWithEmail:async(e,t)=>{i(!0),d(null);let{data:a,error:s}=await r.N.auth.signInWithPassword({email:e,password:t});return s?(d({message:s.message}),i(!1),{success:!1,error:s}):(i(!1),{success:!0,data:a})},signUpWithEmail:async(e,t)=>{i(!0),d(null);let{data:a,error:s}=await r.N.auth.signUp({email:e,password:t});return s?(d({message:s.message}),i(!1),{success:!1,error:s}):(i(!1),{success:!0,data:a})},signInWithGoogle:async()=>{i(!0),d(null);let{data:e,error:t}=await r.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/dashboard")}});return t?(d({message:t.message}),i(!1),{success:!1,error:t}):{success:!0,data:e}},signInWithOTP:async e=>{i(!0),d(null);let{data:t,error:a}=await r.N.auth.signInWithOtp({email:e,options:{shouldCreateUser:!0}});return a?(d({message:a.message}),i(!1),{success:!1,error:a}):(i(!1),{success:!0,data:t})},verifyOTP:async(e,t)=>{i(!0),d(null);let{data:a,error:s}=await r.N.auth.verifyOtp({email:e,token:t,type:"email"});return s?(d({message:s.message}),i(!1),{success:!1,error:s}):(i(!1),{success:!0,data:a})},signOut:async()=>{i(!0);let{error:e}=await r.N.auth.signOut();return e&&d({message:e.message}),i(!1),{success:!e,error:e}}}}},9389:(e,t,a)=>{"use strict";a.d(t,{default:()=>ey});var s=a(5155),r=a(5695),n=a(2115);let l=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[a,r]=(0,n.useState)(!1),[i,o]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=localStorage.getItem("sidebar-collapsed");null!==e&&r(JSON.parse(e)),o(!0)},[]),(0,n.useEffect)(()=>{i&&localStorage.setItem("sidebar-collapsed",JSON.stringify(a))},[a,i]),(0,s.jsx)(l.Provider,{value:{isCollapsed:a,toggleSidebar:()=>{r(e=>!e)},setSidebarCollapsed:e=>{r(e)}},children:t})}var o=a(6064),d=a(6874),c=a.n(d),u=a(6766),x=a(3921),m=a(9434);function h(e){let{delayDuration:t=0,...a}=e;return(0,s.jsx)(x.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function f(e){let{...t}=e;return(0,s.jsx)(h,{children:(0,s.jsx)(x.bL,{"data-slot":"tooltip",...t})})}function p(e){let{...t}=e;return(0,s.jsx)(x.l9,{"data-slot":"tooltip-trigger",...t})}function g(e){let{className:t,sideOffset:a=4,showArrow:r=!1,children:n,...l}=e;return(0,s.jsx)(x.ZL,{children:(0,s.jsxs)(x.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,m.cn)("bg-popover text-popover-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-w-70 rounded-md border px-3 py-1.5 text-sm",t),...l,children:[n,r&&(0,s.jsx)(x.i3,{className:"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]"})]})})}let v=()=>(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})}),j=()=>(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"})}),b=()=>(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"})}),N=()=>(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})}),w=()=>(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"})}),y=()=>{let[e,t]=(0,n.useState)(null),{isCollapsed:a,toggleSidebar:r}=function(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useSidebar deve ser usado dentro de um SidebarProvider");return e}(),i=(0,n.useCallback)(e=>{t(t=>t===e?null:e)},[]);return(0,s.jsx)(h,{delayDuration:0,children:(0,s.jsxs)("div",{className:"".concat(a?"w-16":"w-[270px]"," h-full bg-card border-r border-border flex flex-col transition-all duration-300 ease-in-out"),children:[(0,s.jsx)("div",{className:"p-4 border-b border-border",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)("button",{onClick:r,className:"flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 4l8 8-8 8"})})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Expandir sidebar"})]}):(0,s.jsx)("button",{onClick:r,className:"flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 4l-8 8 8 8"})})})})}),(0,s.jsx)("div",{className:"p-4 border-b border-border",children:(0,s.jsx)(c(),{href:"/dashboard",className:"flex items-center gap-3",children:(0,s.jsx)(u.default,{src:a?"/icon-logo.svg":"/logo.svg",alt:"Profit Growth",width:32,height:32,className:"h-8 w-auto"})})}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"p-4",children:[!a&&(0,s.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"PRINCIPAL"}),(0,s.jsxs)("nav",{className:"space-y-1",children:[a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)(c(),{href:"/",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg bg-primary text-primary-foreground",children:(0,s.jsx)(v,{})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"In\xedcio"})]}):(0,s.jsxs)(c(),{href:"/",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg bg-primary text-primary-foreground",children:[(0,s.jsx)(v,{}),"In\xedcio",(0,s.jsx)("span",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full"})]}),a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)(c(),{href:"/dashboard",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,s.jsx)(j,{})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Dashboard"})]}):(0,s.jsxs)(c(),{href:"/dashboard",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,s.jsx)(j,{}),"Dashboard"]}),a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)(c(),{href:"/analytics",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,s.jsx)(b,{})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Analytics"})]}):(0,s.jsxs)(c(),{href:"/analytics",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,s.jsx)(b,{}),"Analytics"]})]})]}),(0,s.jsxs)("div",{className:"p-4",children:[!a&&(0,s.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"APLICA\xc7\xd5ES"}),(0,s.jsxs)("nav",{className:"space-y-1",children:[a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)(c(),{href:"/chat",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,s.jsx)(N,{})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Chat"})]}):(0,s.jsxs)(c(),{href:"/chat",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,s.jsx)(N,{}),"Chat"]}),a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)(c(),{href:"/calendar",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,s.jsx)(w,{})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Calend\xe1rio"})]}):(0,s.jsxs)(c(),{href:"/calendar",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,s.jsx)(w,{}),"Calend\xe1rio"]})]})]}),(0,s.jsxs)("div",{className:"p-4",children:[!a&&(0,s.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"OUTROS"}),(0,s.jsxs)("nav",{className:"space-y-1",children:[a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)("button",{className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Menu Multin\xedvel"})]}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("button",{onClick:()=>i("multilevel"),className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full text-left",children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})}),"Menu Multin\xedvel",(0,s.jsx)("svg",{className:"w-4 h-4 ml-auto transition-transform ".concat("multilevel"===e?"rotate-90":""),fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]}),"multilevel"===e&&(0,s.jsxs)("div",{className:"ml-6 mt-1 space-y-1",children:[(0,s.jsx)(c(),{href:"/posts",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:"Posts"}),(0,s.jsx)(c(),{href:"/details",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:"Detalhes"})]})]}),a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)(c(),{href:"/sobre",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Sobre"})]}):(0,s.jsxs)(c(),{href:"/sobre",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),"Sobre"]}),a?(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})})}),(0,s.jsx)(g,{side:"right",className:"px-2 py-1 text-xs",children:"Link Externo"})]}):(0,s.jsxs)("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Link Externo",(0,s.jsx)("svg",{className:"w-4 h-4 ml-auto",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})]})]})]})})};var k=a(1284),C=a(9245),z=a(5560),_=a(1788),S=a(2085),M=a(9708);let I=(0,S.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function E(e){let{className:t,variant:a,size:r,asChild:n=!1,...l}=e,i=n?M.bL:"button";return(0,s.jsx)(i,{"data-slot":"button",className:(0,m.cn)(I({variant:a,size:r,className:t})),...l})}var A=a(8578);function P(e){let{...t}=e;return(0,s.jsx)(A.bL,{"data-slot":"dropdown-menu",...t})}function R(e){let{...t}=e;return(0,s.jsx)(A.l9,{"data-slot":"dropdown-menu-trigger",...t})}function L(e){let{className:t,sideOffset:a=4,onPointerDown:r,onPointerDownOutside:l,onCloseAutoFocus:i,...o}=e,d=n.useRef(!1),c=n.useCallback(e=>{d.current=!0,null==r||r(e)},[r]),u=n.useCallback(e=>{d.current=!0,null==l||l(e)},[l]),x=n.useCallback(e=>{if(i)return i(e);d.current&&(e.preventDefault(),d.current=!1)},[i]);return(0,s.jsx)(A.ZL,{children:(0,s.jsx)(A.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,m.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-40 overflow-hidden rounded-md border p-1 shadow-lg",t),onPointerDown:c,onPointerDownOutside:u,onCloseAutoFocus:x,...o})})}function O(e){let{...t}=e;return(0,s.jsx)(A.YJ,{"data-slot":"dropdown-menu-group",...t})}function U(e){let{className:t,inset:a,variant:r="default",...n}=e;return(0,s.jsx)(A.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,m.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),...n})}function B(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(A.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,m.cn)("text-muted-foreground px-2 py-1.5 text-xs font-medium data-[inset]:pl-8",t),...r})}function H(e){let{className:t,...a}=e;return(0,s.jsx)(A.wv,{"data-slot":"dropdown-menu-separator",className:(0,m.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function D(){return(0,s.jsxs)(P,{children:[(0,s.jsx)(R,{asChild:!0,children:(0,s.jsx)(E,{size:"icon",variant:"ghost",className:"size-8 rounded-full shadow-none","aria-label":"Open edit menu",children:(0,s.jsx)(k.A,{className:"text-muted-foreground",size:16,"aria-hidden":"true"})})}),(0,s.jsxs)(L,{className:"pb-2",children:[(0,s.jsx)(B,{children:"Precisa de ajuda?"}),(0,s.jsx)(U,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,s.jsxs)("a",{href:"https://docs.supabase.com",target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(C.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Documenta\xe7\xe3o"]})}),(0,s.jsx)(U,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,s.jsxs)("a",{href:"#",onClick:()=>alert("Em breve!"),children:[(0,s.jsx)(z.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Suporte"]})}),(0,s.jsx)(U,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,s.jsxs)("a",{href:"#",onClick:()=>alert("Em breve!"),children:[(0,s.jsx)(_.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Contato"]})})]})]})}var F=a(3861),V=a(547);function J(e){let{...t}=e;return(0,s.jsx)(V.bL,{"data-slot":"popover",...t})}function T(e){let{...t}=e;return(0,s.jsx)(V.l9,{"data-slot":"popover-trigger",...t})}function W(e){let{className:t,align:a="center",sideOffset:r=4,showArrow:n=!1,...l}=e;return(0,s.jsx)(V.ZL,{children:(0,s.jsxs)(V.UC,{"data-slot":"popover-content",align:a,sideOffset:r,className:(0,m.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 rounded-md border p-4 shadow-md outline-hidden",t),...l,children:[l.children,n&&(0,s.jsx)(V.i3,{className:"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]"})]})})}let Z=[{id:1,user:"Carlos Silva",action:"solicitou revis\xe3o em",target:"PR #42: Implementa\xe7\xe3o de funcionalidade",timestamp:"h\xe1 15 minutos",unread:!0},{id:2,user:"Ana Santos",action:"compartilhou",target:"Nova biblioteca de componentes",timestamp:"h\xe1 45 minutos",unread:!0},{id:3,user:"Jo\xe3o Oliveira",action:"atribuiu voc\xea para",target:"Tarefa de integra\xe7\xe3o da API",timestamp:"h\xe1 4 horas",unread:!1},{id:4,user:"Maria Costa",action:"respondeu ao seu coment\xe1rio em",target:"Fluxo de autentica\xe7\xe3o",timestamp:"h\xe1 12 horas",unread:!1},{id:5,user:"Pedro Lima",action:"comentou em",target:"Redesign do dashboard",timestamp:"h\xe1 2 dias",unread:!1},{id:6,user:"Lucia Ferreira",action:"mencionou voc\xea em",target:"Imagem do projeto Supabase",timestamp:"h\xe1 2 semanas",unread:!1}];function q(e){let{className:t}=e;return(0,s.jsx)("svg",{width:"6",height:"6",fill:"currentColor",viewBox:"0 0 6 6",xmlns:"http://www.w3.org/2000/svg",className:t,"aria-hidden":"true",children:(0,s.jsx)("circle",{cx:"3",cy:"3",r:"3"})})}function G(){let[e,t]=(0,n.useState)(Z),a=e.filter(e=>e.unread).length;return(0,s.jsxs)(J,{children:[(0,s.jsx)(T,{asChild:!0,children:(0,s.jsxs)(E,{size:"icon",variant:"ghost",className:"text-muted-foreground relative size-8 rounded-full shadow-none","aria-label":"Open notifications",children:[(0,s.jsx)(F.A,{size:16,"aria-hidden":"true"}),a>0&&(0,s.jsx)("div",{"aria-hidden":"true",className:"bg-primary absolute top-0.5 right-0.5 size-1 rounded-full"})]})}),(0,s.jsxs)(W,{className:"w-80 p-1",children:[(0,s.jsxs)("div",{className:"flex items-baseline justify-between gap-4 px-3 py-2",children:[(0,s.jsx)("div",{className:"text-sm font-semibold",children:"Notifica\xe7\xf5es"}),a>0&&(0,s.jsx)("button",{className:"text-xs font-medium hover:underline",onClick:()=>{t(e.map(e=>({...e,unread:!1})))},children:"Marcar todas como lidas"})]}),(0,s.jsx)("div",{role:"separator","aria-orientation":"horizontal",className:"bg-border -mx-1 my-1 h-px"}),e.map(a=>(0,s.jsx)("div",{className:"hover:bg-accent rounded-md px-3 py-2 text-sm transition-colors",children:(0,s.jsxs)("div",{className:"relative flex items-start pe-3",children:[(0,s.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,s.jsxs)("button",{className:"text-foreground/80 text-left after:absolute after:inset-0",onClick:()=>{var s;return s=a.id,void t(e.map(e=>e.id===s?{...e,unread:!1}:e))},children:[(0,s.jsx)("span",{className:"text-foreground font-medium hover:underline",children:a.user})," ",a.action," ",(0,s.jsx)("span",{className:"text-foreground font-medium hover:underline",children:a.target}),"."]}),(0,s.jsx)("div",{className:"text-muted-foreground text-xs",children:a.timestamp})]}),a.unread&&(0,s.jsxs)("div",{className:"absolute end-0 self-center",children:[(0,s.jsx)("span",{className:"sr-only",children:"Unread"}),(0,s.jsx)(q,{})]})]})},a.id))]})]})}var Y=a(1007),Q=a(381),X=a(6521),K=a(9963),$=a(5040),ee=a(4835),et=a(6681),ea=a(1824),es=a(4011);function er(e){let{className:t,...a}=e;return(0,s.jsx)(es.bL,{"data-slot":"avatar",className:(0,m.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function en(e){let{className:t,...a}=e;return(0,s.jsx)(es._V,{"data-slot":"avatar-image",className:(0,m.cn)("aspect-square size-full",t),...a})}function el(e){let{className:t,...a}=e;return(0,s.jsx)(es.H4,{"data-slot":"avatar-fallback",className:(0,m.cn)("bg-secondary flex size-full items-center justify-center rounded-[inherit] text-xs",t),...a})}function ei(){var e,t,a,l;let{user:i,signOut:o,isLoading:d}=(0,et.A)(),{profile:c}=(0,ea.x)(),u=(0,r.useRouter)(),[x,m]=(0,n.useState)(!1),h=async()=>{try{m(!0);let e=await o();e.success?u.push("/login"):console.error("Erro ao fazer logout:",e.error)}catch(e){console.error("Erro inesperado ao fazer logout:",e)}finally{m(!1)}},f=e=>{if(!e)return"US";let t=e.split("@")[0].split(".");return t.length>=2?(t[0][0]+t[1][0]).toUpperCase():e.substring(0,2).toUpperCase()},p=()=>{var e,t;if(null==c?void 0:c.full_name)return c.full_name;if(null==i||null==(e=i.user_metadata)?void 0:e.full_name)return i.user_metadata.full_name;if(null==i||null==(t=i.user_metadata)?void 0:t.name)return i.user_metadata.name;if(null==i?void 0:i.email){let e=i.email.split("@")[0];return e.charAt(0).toUpperCase()+e.slice(1)}return"Usu\xe1rio"},g=e=>{u.push(e)};return d||!i?null:(0,s.jsxs)(P,{children:[(0,s.jsx)(R,{asChild:!0,children:(0,s.jsx)(E,{variant:"ghost",className:"h-auto p-2 hover:bg-accent focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg",disabled:x,children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(er,{className:"h-8 w-8",children:[(0,s.jsx)(en,{src:(null==c?void 0:c.avatar_url)||(null==(e=i.user_metadata)?void 0:e.avatar_url)||(null==(t=i.user_metadata)?void 0:t.picture),alt:"Imagem do perfil"}),(0,s.jsx)(el,{className:"bg-primary/10 text-primary font-medium",children:f(i.email||"US")})]}),(0,s.jsxs)("div",{className:"flex flex-col items-start min-w-0",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-foreground truncate max-w-[150px]",children:p()}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground truncate max-w-[150px]",children:i.email})]})]})})}),(0,s.jsxs)(L,{className:"w-64",align:"end",sideOffset:8,children:[(0,s.jsx)(B,{className:"flex min-w-0 flex-col p-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(er,{className:"h-10 w-10",children:[(0,s.jsx)(en,{src:(null==c?void 0:c.avatar_url)||(null==(a=i.user_metadata)?void 0:a.avatar_url)||(null==(l=i.user_metadata)?void 0:l.picture),alt:"Imagem do perfil"}),(0,s.jsx)(el,{className:"bg-primary/10 text-primary font-medium",children:f(i.email||"US")})]}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-foreground truncate text-sm font-medium",children:p()}),(0,s.jsx)("p",{className:"text-muted-foreground truncate text-xs font-normal",children:i.email})]})]})}),(0,s.jsx)(H,{}),(0,s.jsxs)(O,{children:[(0,s.jsxs)(U,{onClick:()=>g("/profile"),className:"cursor-pointer",children:[(0,s.jsx)(Y.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Meu Perfil"})]}),(0,s.jsxs)(U,{onClick:()=>g("/configuracoes"),className:"cursor-pointer",children:[(0,s.jsx)(Q.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Configura\xe7\xf5es"})]})]}),(0,s.jsx)(H,{}),(0,s.jsxs)(O,{children:[(0,s.jsxs)(U,{onClick:()=>g("/projetos"),className:"cursor-pointer",children:[(0,s.jsx)(X.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Meus Projetos"})]}),(0,s.jsxs)(U,{onClick:()=>g("/favoritos"),className:"cursor-pointer",children:[(0,s.jsx)(K.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Favoritos"})]})]}),(0,s.jsx)(H,{}),(0,s.jsx)(O,{children:(0,s.jsxs)(U,{onClick:()=>window.open("https://docs.supabase.com","_blank"),className:"cursor-pointer",children:[(0,s.jsx)($.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,s.jsx)("span",{children:"Documenta\xe7\xe3o"})]})}),(0,s.jsx)(H,{}),(0,s.jsxs)(U,{onClick:h,className:"cursor-pointer text-destructive focus:text-destructive",disabled:x,children:[(0,s.jsx)(ee.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,s.jsx)("span",{children:x?"Saindo...":"Sair"})]})]})]})}var eo=a(3109),ed=a(8500),ec=a(3904);function eu(){let[e,t]=(0,n.useState)({portfolioBalance:623098.17,availableFunds:122912.5,isLoading:!1,trend:"up",changePercent:2.34,lastUpdated:new Date}),[a,r]=(0,n.useState)(!1),l=(0,n.useMemo)(()=>{let e=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2});return t=>e.format(t)},[]),i=async()=>{r(!0),await new Promise(e=>setTimeout(e,1e3)),t(e=>({...e,portfolioBalance:e.portfolioBalance+(Math.random()-.5)*2e3,availableFunds:e.availableFunds+(Math.random()-.5)*1e3,changePercent:(Math.random()-.5)*8,trend:Math.random()>.5?"up":"down",lastUpdated:new Date})),r(!1)};return((0,n.useEffect)(()=>{let e=setInterval(()=>{a||t(e=>({...e,portfolioBalance:e.portfolioBalance+(Math.random()-.5)*500,availableFunds:e.availableFunds+(Math.random()-.5)*200,changePercent:e.changePercent+(Math.random()-.5)*.5,trend:Math.random()>.6?Math.random()>.5?"up":"down":e.trend,lastUpdated:new Date}))},6e4);return()=>clearInterval(e)},[a]),e.isLoading)?(0,s.jsxs)("div",{className:"flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg border animate-pulse",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,s.jsx)("div",{className:"h-3 bg-muted rounded w-20"}),(0,s.jsx)("div",{className:"h-5 bg-muted rounded w-28"})]}),(0,s.jsx)("div",{className:"h-6 w-px bg-border"}),(0,s.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,s.jsx)("div",{className:"h-3 bg-muted rounded w-24"}),(0,s.jsx)("div",{className:"h-5 bg-muted rounded w-24"})]})]}):(0,s.jsxs)("div",{className:"flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg  hover:bg-card/50 transition-all duration-200 group",children:[(0,s.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,s.jsx)("div",{className:"flex items-center gap-1 mb-0.5",children:(0,s.jsx)("span",{className:"text-xs  text-muted-foreground  tracking-wider",children:"Saldo Inicial"})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm  text-foreground tabular-nums truncate",children:l(e.portfolioBalance)}),(0,s.jsxs)("div",{className:"flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs font-medium transition-colors ".concat("up"===e.trend?"text-primary  dark:text-primary ":"text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900/30"),children:["up"===e.trend?(0,s.jsx)(eo.A,{size:10}):(0,s.jsx)(ed.A,{size:10}),(0,s.jsxs)("span",{children:[Math.abs(e.changePercent).toFixed(2),"%"]})]})]})]}),(0,s.jsx)("div",{className:"h-8 w-px bg-border/60"}),(0,s.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,s.jsx)("div",{className:"flex items-center gap-1 mb-0.5",children:(0,s.jsx)("span",{className:"text-xs  text-muted-foreground  tracking-wider",children:"Saldo Atual"})}),(0,s.jsx)("span",{className:"text-sm  text-foreground tabular-nums truncate",children:l(e.availableFunds)})]}),(0,s.jsx)("div",{className:"flex items-center gap-2 ml-2",children:(0,s.jsx)(E,{variant:"ghost",size:"sm",onClick:i,disabled:a,className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,s.jsx)(ec.A,{size:14,className:"".concat(a?"animate-spin":""," text-muted-foreground hover:text-foreground")})})})]})}var ex=a(3509),em=a(2098),eh=a(968);function ef(e){let{className:t,...a}=e;return(0,s.jsx)(eh.b,{"data-slot":"label",className:(0,m.cn)("text-foreground text-sm leading-4 font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}var ep=a(239);function eg(e){let{className:t,...a}=e;return(0,s.jsx)(ep.bL,{"data-slot":"switch",className:(0,m.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:ring-ring/50 inline-flex h-6 w-10 shrink-0 items-center rounded-full border-2 border-transparent transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(ep.zi,{"data-slot":"switch-thumb",className:(0,m.cn)("bg-background pointer-events-none block size-5 rounded-full shadow-xs ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0 data-[state=checked]:rtl:-translate-x-4")})})}function ev(){let e=(0,n.useId)(),{toggleTheme:t,isDark:a,isInitialized:r}=function(){let[e,t]=(0,n.useState)("dark"),[a,s]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=localStorage.getItem("theme");e&&("dark"===e||"light"===e)?(t(e),document.documentElement.className=e):(document.documentElement.className="dark",localStorage.setItem("theme","dark")),s(!0)},[]),{theme:e,toggleTheme:()=>{if(!a)return;let s="dark"===e?"light":"dark";t(s),document.documentElement.className=s,localStorage.setItem("theme",s)},setTheme:e=>{a&&(t(e),document.documentElement.className=e,localStorage.setItem("theme",e))},isDark:"dark"===e,isLight:"light"===e,isInitialized:a}}();return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"relative inline-grid h-9 grid-cols-[1fr_1fr] items-center text-sm font-medium",children:[(0,s.jsx)(eg,{id:e,checked:a,onCheckedChange:t,disabled:!r,className:"peer data-[state=checked]:bg-input/50 data-[state=unchecked]:bg-input/50 absolute inset-0 h-[inherit] w-auto [&_span]:h-full [&_span]:w-1/2 [&_span]:transition-transform [&_span]:duration-300 [&_span]:ease-[cubic-bezier(0.16,1,0.3,1)] [&_span]:data-[state=checked]:translate-x-full [&_span]:data-[state=checked]:rtl:-translate-x-full"}),(0,s.jsx)("span",{className:"peer-data-[state=checked]:text-muted-foreground/70 pointer-events-none relative ms-0.5 flex min-w-8 items-center justify-center text-center",children:(0,s.jsx)(ex.A,{size:16,"aria-hidden":"true"})}),(0,s.jsx)("span",{className:"peer-data-[state=unchecked]:text-muted-foreground/70 pointer-events-none relative me-0.5 flex min-w-8 items-center justify-center text-center",children:(0,s.jsx)(em.A,{size:16,"aria-hidden":"true"})})]}),(0,s.jsx)(ef,{htmlFor:e,className:"sr-only",children:"Alternar tema entre claro e escuro"})]})}function ej(){return(0,s.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsx)("div",{className:"container mx-auto px-4 md:px-6",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between gap-4",children:[(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsx)(eu,{})}),(0,s.jsx)("div",{className:"flex-1"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ev,{}),(0,s.jsx)(G,{}),(0,s.jsx)(D,{}),(0,s.jsx)(ei,{})]})]})})})}var eb=a(5876);let eN=["/login","/"],ew=["/dashboard","/analytics","/chat","/calendar","/profile","/perfil"];function ey(e){let{children:t}=e,a=(0,r.usePathname)(),n=eN.includes(a),l=ew.some(e=>a.startsWith(e));return n?(0,s.jsx)(s.Fragment,{children:t}):l?(0,s.jsx)(eb.A,{children:(0,s.jsx)(o.H,{children:(0,s.jsx)(i,{children:(0,s.jsxs)("div",{className:"h-screen flex bg-background",children:[(0,s.jsx)(y,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)(ej,{}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto",children:t})]})]})})})}):(0,s.jsx)(o.H,{children:(0,s.jsx)(i,{children:(0,s.jsxs)("div",{className:"h-screen flex bg-background",children:[(0,s.jsx)(y,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)(ej,{}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto",children:t})]})]})})})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{e.O(0,[893,302,926,240,441,964,358],()=>e(e.s=6234)),_N_E=e.O()}]);