{"version": 3, "sources": ["../../../src/server/request/draft-mode.ts"], "sourcesContent": ["import {\n  getDraftModeProviderForCacheScope,\n  throwForMissingRequestStore,\n} from '../app-render/work-unit-async-storage.external'\n\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `draftMode()` returns a Promise however you can still reference the properties of the underlying draftMode object\n * synchronously to facilitate migration. The `UnsafeUnwrappedDraftMode` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `draftMode()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedDraftMode` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `draftMode()` value can be awaited or you should call `draftMode()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedDraftMode` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `draftMode()` will only return a Promise and you will not be able to access the underlying draftMode object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedDraftMode` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedDraftMode = DraftMode\n\nexport function draftMode(): Promise<DraftMode> {\n  const callingExpression = 'draftMode'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workStore || !workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'request':\n      return createOrGetCachedDraftMode(workUnitStore.draftMode, workStore)\n\n    case 'cache':\n    case 'unstable-cache':\n      // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n      // the outmost work unit store is a request store, and if draft mode is\n      // enabled.\n      const draftModeProvider = getDraftModeProviderForCacheScope(\n        workStore,\n        workUnitStore\n      )\n\n      if (draftModeProvider) {\n        return createOrGetCachedDraftMode(draftModeProvider, workStore)\n      }\n\n    // Otherwise, we fall through to providing an empty draft mode.\n    // eslint-disable-next-line no-fallthrough\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // Return empty draft mode\n      return createOrGetCachedDraftMode(null, workStore)\n\n    default:\n      const _exhaustiveCheck: never = workUnitStore\n      return _exhaustiveCheck\n  }\n}\n\nfunction createOrGetCachedDraftMode(\n  draftModeProvider: DraftModeProvider | null,\n  workStore: WorkStore | undefined\n): Promise<DraftMode> {\n  const cacheKey = draftModeProvider ?? NullDraftMode\n  const cachedDraftMode = CachedDraftModes.get(cacheKey)\n\n  if (cachedDraftMode) {\n    return cachedDraftMode\n  }\n\n  let promise: Promise<DraftMode>\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    const route = workStore?.route\n\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return createDraftModeWithDevWarnings(draftModeProvider, route)\n    }\n\n    promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route)\n  } else {\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return Promise.resolve(new DraftMode(draftModeProvider))\n    }\n\n    promise = createExoticDraftMode(draftModeProvider)\n  }\n\n  CachedDraftModes.set(cacheKey, promise)\n\n  return promise\n}\n\ninterface CacheLifetime {}\nconst NullDraftMode = {}\nconst CachedDraftModes = new WeakMap<CacheLifetime, Promise<DraftMode>>()\n\nfunction createExoticDraftMode(\n  underlyingProvider: null | DraftModeProvider\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      return instance.isEnabled\n    },\n    enumerable: true,\n    configurable: true,\n  })\n  ;(promise as any).enable = instance.enable.bind(instance)\n  ;(promise as any).disable = instance.disable.bind(instance)\n\n  return promise\n}\n\nfunction createExoticDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      const expression = '`draftMode().isEnabled`'\n      syncIODev(route, expression)\n      return instance.isEnabled\n    },\n    enumerable: true,\n    configurable: true,\n  })\n\n  Object.defineProperty(promise, 'enable', {\n    value: function get() {\n      const expression = '`draftMode().enable()`'\n      syncIODev(route, expression)\n      return instance.enable.apply(instance, arguments as any)\n    },\n  })\n\n  Object.defineProperty(promise, 'disable', {\n    value: function get() {\n      const expression = '`draftMode().disable()`'\n      syncIODev(route, expression)\n      return instance.disable.apply(instance, arguments as any)\n    },\n  })\n\n  return promise\n}\n\n// Similar to `createExoticDraftModeWithDevWarnings`, but just logging the sync\n// access without actually defining the draftMode properties on the promise.\nfunction createDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'isEnabled':\n          warnForSyncAccess(route, `\\`draftMode().${prop}\\``)\n          break\n        case 'enable':\n        case 'disable': {\n          warnForSyncAccess(route, `\\`draftMode().${prop}()\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the draftMode object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  return proxiedPromise\n}\n\nclass DraftMode {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _provider: null | DraftModeProvider\n\n  constructor(provider: null | DraftModeProvider) {\n    this._provider = provider\n  }\n  get isEnabled() {\n    if (this._provider !== null) {\n      return this._provider.isEnabled\n    }\n    return false\n  }\n  public enable() {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    trackDynamicDraftMode('draftMode().enable()')\n    if (this._provider !== null) {\n      this._provider.enable()\n    }\n  }\n  public disable() {\n    trackDynamicDraftMode('draftMode().disable()')\n    if (this._provider !== null) {\n      this._provider.disable()\n    }\n  }\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createDraftModeAccessError\n)\n\nfunction createDraftModeAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`draftMode()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction trackDynamicDraftMode(expression: string) {\n  const store = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (store) {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      } else if (workUnitStore.phase === 'after') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`\n        )\n      }\n    }\n\n    if (store.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n          // dynamicIO Prerender\n          const error = new Error(\n            `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`\n          )\n          abortAndThrowOnSynchronousRequestDataAccess(\n            store.route,\n            expression,\n            error,\n            workUnitStore\n          )\n          break\n        case 'prerender-client':\n          const exportName = '`draftMode`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // PPR Prerender\n          postponeWithTracking(\n            store.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n          break\n        case 'prerender-legacy':\n          // legacy Prerender\n          workUnitStore.revalidate = 0\n\n          const err = new DynamicServerError(\n            `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n          )\n          store.dynamicUsageDescription = expression\n          store.dynamicUsageStack = err.stack\n\n          throw err\n        case 'request':\n          if (process.env.NODE_ENV === 'development') {\n            workUnitStore.usedDynamic = true\n          }\n          break\n        default:\n        // fallthrough\n      }\n    }\n  }\n}\n"], "names": ["draftMode", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "throwForMissingRequestStore", "type", "createOrGetCachedDraftMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDraftModeProviderForCacheScope", "_exhaustiveCheck", "cache<PERSON>ey", "NullDraftMode", "cachedDraftMode", "CachedDraftModes", "get", "promise", "process", "env", "NODE_ENV", "isPrefetchRequest", "route", "__NEXT_DYNAMIC_IO", "createDraftModeWithDevWarnings", "createExoticDraftModeWithDevWarnings", "Promise", "resolve", "DraftMode", "createExoticDraftMode", "set", "WeakMap", "underlyingProvider", "instance", "Object", "defineProperty", "isEnabled", "enumerable", "configurable", "enable", "bind", "disable", "expression", "syncIODev", "value", "apply", "arguments", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "ReflectAdapter", "constructor", "provider", "_provider", "trackDynamicDraftMode", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "createDedupedByCallsiteServerErrorLoggerDev", "createDraftModeAccessError", "prefix", "Error", "store", "phase", "dynamicShouldError", "StaticGenBailoutError", "error", "abortAndThrowOnSynchronousRequestDataAccess", "exportName", "InvariantError", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "usedDynamic"], "mappings": ";;;;+BA8CgBA;;;eAAAA;;;8CA3CT;0CAOA;kCAMA;0DACqD;yCACtB;oCACH;gCACJ;yBACA;AAyBxB,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAI,CAACF,aAAa,CAACG,eAAe;QAChCE,IAAAA,yDAA2B,EAACN;IAC9B;IAEA,OAAQI,cAAcG,IAAI;QACxB,KAAK;YACH,OAAOC,2BAA2BJ,cAAcL,SAAS,EAAEE;QAE7D,KAAK;QACL,KAAK;YACH,0EAA0E;YAC1E,uEAAuE;YACvE,WAAW;YACX,MAAMQ,oBAAoBC,IAAAA,+DAAiC,EACzDT,WACAG;YAGF,IAAIK,mBAAmB;gBACrB,OAAOD,2BAA2BC,mBAAmBR;YACvD;QAEF,+DAA+D;QAC/D,0CAA0C;QAC1C,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,0BAA0B;YAC1B,OAAOO,2BAA2B,MAAMP;QAE1C;YACE,MAAMU,mBAA0BP;YAChC,OAAOO;IACX;AACF;AAEA,SAASH,2BACPC,iBAA2C,EAC3CR,SAAgC;IAEhC,MAAMW,WAAWH,qBAAqBI;IACtC,MAAMC,kBAAkBC,iBAAiBC,GAAG,CAACJ;IAE7C,IAAIE,iBAAiB;QACnB,OAAOA;IACT;IAEA,IAAIG;IAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAACnB,6BAAAA,UAAWoB,iBAAiB,GAAE;QAC3E,MAAMC,QAAQrB,6BAAAA,UAAWqB,KAAK;QAE9B,IAAIJ,QAAQC,GAAG,CAACI,iBAAiB,EAAE;YACjC,OAAOC,+BAA+Bf,mBAAmBa;QAC3D;QAEAL,UAAUQ,qCAAqChB,mBAAmBa;IACpE,OAAO;QACL,IAAIJ,QAAQC,GAAG,CAACI,iBAAiB,EAAE;YACjC,OAAOG,QAAQC,OAAO,CAAC,IAAIC,UAAUnB;QACvC;QAEAQ,UAAUY,sBAAsBpB;IAClC;IAEAM,iBAAiBe,GAAG,CAAClB,UAAUK;IAE/B,OAAOA;AACT;AAGA,MAAMJ,gBAAgB,CAAC;AACvB,MAAME,mBAAmB,IAAIgB;AAE7B,SAASF,sBACPG,kBAA4C;IAE5C,MAAMC,WAAW,IAAIL,UAAUI;IAC/B,MAAMf,UAAUS,QAAQC,OAAO,CAACM;IAEhCC,OAAOC,cAAc,CAAClB,SAAS,aAAa;QAC1CD;YACE,OAAOiB,SAASG,SAAS;QAC3B;QACAC,YAAY;QACZC,cAAc;IAChB;IACErB,QAAgBsB,MAAM,GAAGN,SAASM,MAAM,CAACC,IAAI,CAACP;IAC9ChB,QAAgBwB,OAAO,GAAGR,SAASQ,OAAO,CAACD,IAAI,CAACP;IAElD,OAAOhB;AACT;AAEA,SAASQ,qCACPO,kBAA4C,EAC5CV,KAAyB;IAEzB,MAAMW,WAAW,IAAIL,UAAUI;IAC/B,MAAMf,UAAUS,QAAQC,OAAO,CAACM;IAEhCC,OAAOC,cAAc,CAAClB,SAAS,aAAa;QAC1CD;YACE,MAAM0B,aAAa;YACnBC,UAAUrB,OAAOoB;YACjB,OAAOT,SAASG,SAAS;QAC3B;QACAC,YAAY;QACZC,cAAc;IAChB;IAEAJ,OAAOC,cAAc,CAAClB,SAAS,UAAU;QACvC2B,OAAO,SAAS5B;YACd,MAAM0B,aAAa;YACnBC,UAAUrB,OAAOoB;YACjB,OAAOT,SAASM,MAAM,CAACM,KAAK,CAACZ,UAAUa;QACzC;IACF;IAEAZ,OAAOC,cAAc,CAAClB,SAAS,WAAW;QACxC2B,OAAO,SAAS5B;YACd,MAAM0B,aAAa;YACnBC,UAAUrB,OAAOoB;YACjB,OAAOT,SAASQ,OAAO,CAACI,KAAK,CAACZ,UAAUa;QAC1C;IACF;IAEA,OAAO7B;AACT;AAEA,+EAA+E;AAC/E,4EAA4E;AAC5E,SAASO,+BACPQ,kBAA4C,EAC5CV,KAAyB;IAEzB,MAAMW,WAAW,IAAIL,UAAUI;IAC/B,MAAMf,UAAUS,QAAQC,OAAO,CAACM;IAEhC,MAAMc,iBAAiB,IAAIC,MAAM/B,SAAS;QACxCD,KAAIiC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAK;oBACHE,kBAAkB9B,OAAO,CAAC,cAAc,EAAE4B,KAAK,EAAE,CAAC;oBAClD;gBACF,KAAK;gBACL,KAAK;oBAAW;wBACdE,kBAAkB9B,OAAO,CAAC,cAAc,EAAE4B,KAAK,IAAI,CAAC;wBACpD;oBACF;gBACA;oBAAS;oBACP,oEAAoE;oBACtE;YACF;YAEA,OAAOG,uBAAc,CAACrC,GAAG,CAACiC,QAAQC,MAAMC;QAC1C;IACF;IAEA,OAAOJ;AACT;AAEA,MAAMnB;IAMJ0B,YAAYC,QAAkC,CAAE;QAC9C,IAAI,CAACC,SAAS,GAAGD;IACnB;IACA,IAAInB,YAAY;QACd,IAAI,IAAI,CAACoB,SAAS,KAAK,MAAM;YAC3B,OAAO,IAAI,CAACA,SAAS,CAACpB,SAAS;QACjC;QACA,OAAO;IACT;IACOG,SAAS;QACd,oEAAoE;QACpE,+DAA+D;QAC/DkB,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACjB,MAAM;QACvB;IACF;IACOE,UAAU;QACfgB,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACf,OAAO;QACxB;IACF;AACF;AAEA,SAASE,UAAUrB,KAAyB,EAAEoB,UAAkB;IAC9D,MAAMtC,gBAAgBC,kDAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAcsD,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAevD;QACrBwD,IAAAA,wDAAsC,EAACD;IACzC;IACA,gCAAgC;IAChCP,kBAAkB9B,OAAOoB;AAC3B;AAEA,MAAMU,oBAAoBS,IAAAA,qFAA2C,EACnEC;AAGF,SAASA,2BACPxC,KAAyB,EACzBoB,UAAkB;IAElB,MAAMqB,SAASzC,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAI0C,MACT,GAAGD,OAAO,KAAK,EAAErB,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASe,sBAAsBf,UAAkB;IAC/C,MAAMuB,QAAQ/D,0CAAgB,CAACC,QAAQ;IACvC,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IACnD,IAAI8D,OAAO;QACT,oEAAoE;QACpE,+DAA+D;QAC/D,IAAI7D,eAAe;YACjB,IAAIA,cAAcG,IAAI,KAAK,SAAS;gBAClC,MAAM,qBAEL,CAFK,IAAIyD,MACR,CAAC,MAAM,EAAEC,MAAM3C,KAAK,CAAC,OAAO,EAAEoB,WAAW,uNAAuN,CAAC,GAD7P,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAItC,cAAcG,IAAI,KAAK,kBAAkB;gBAClD,MAAM,qBAEL,CAFK,IAAIyD,MACR,CAAC,MAAM,EAAEC,MAAM3C,KAAK,CAAC,OAAO,EAAEoB,WAAW,gQAAgQ,CAAC,GADtS,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAItC,cAAc8D,KAAK,KAAK,SAAS;gBAC1C,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEC,MAAM3C,KAAK,CAAC,OAAO,EAAEoB,WAAW,0MAA0M,CAAC,GADhP,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAIuB,MAAME,kBAAkB,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEH,MAAM3C,KAAK,CAAC,8EAA8E,EAAEoB,WAAW,4HAA4H,CAAC,GADzO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAItC,eAAe;YACjB,OAAQA,cAAcG,IAAI;gBACxB,KAAK;oBACH,sBAAsB;oBACtB,MAAM8D,QAAQ,qBAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAEC,MAAM3C,KAAK,CAAC,MAAM,EAAEoB,WAAW,+HAA+H,CAAC,GAD5J,qBAAA;+BAAA;oCAAA;sCAAA;oBAEd;oBACA4B,IAAAA,6DAA2C,EACzCL,MAAM3C,KAAK,EACXoB,YACA2B,OACAjE;oBAEF;gBACF,KAAK;oBACH,MAAMmE,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,GAAGD,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,gBAAgB;oBAChBE,IAAAA,sCAAoB,EAClBR,MAAM3C,KAAK,EACXoB,YACAtC,cAAcsE,eAAe;oBAE/B;gBACF,KAAK;oBACH,mBAAmB;oBACnBtE,cAAcuE,UAAU,GAAG;oBAE3B,MAAMC,MAAM,qBAEX,CAFW,IAAIC,sCAAkB,CAChC,CAAC,MAAM,EAAEZ,MAAM3C,KAAK,CAAC,mDAAmD,EAAEoB,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEZ;oBACAuB,MAAMa,uBAAuB,GAAGpC;oBAChCuB,MAAMc,iBAAiB,GAAGH,IAAII,KAAK;oBAEnC,MAAMJ;gBACR,KAAK;oBACH,IAAI1D,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;wBAC1ChB,cAAc6E,WAAW,GAAG;oBAC9B;oBACA;gBACF;YAEF;QACF;IACF;AACF", "ignoreList": [0]}