{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/entrypoints.ts"], "sourcesContent": ["import * as React from 'react'\nimport * as ReactD<PERSON> from 'react-dom'\nimport * as ReactJsxDevRuntime from 'react/jsx-dev-runtime'\nimport * as ReactJsxRuntime from 'react/jsx-runtime'\nimport * as ReactCompilerRuntime from 'react/compiler-runtime'\n\nfunction getAltProxyForBindingsDEV(\n  type: 'Turbopack' | 'Webpack',\n  pkg:\n    | 'react-server-dom-turbopack/server'\n    | 'react-server-dom-turbopack/static'\n    | 'react-server-dom-webpack/server'\n    | 'react-server-dom-webpack/static'\n) {\n  if (process.env.NODE_ENV === 'development') {\n    const altType = type === 'Turbopack' ? 'Webpack' : 'Turbopack'\n    const altPkg = pkg.replace(new RegExp(type, 'gi'), altType.toLowerCase())\n\n    return new Proxy(\n      {},\n      {\n        get(_, prop: string) {\n          throw new Error(\n            `Expected to use ${type} bindings (${pkg}) for React but the current process is referencing '${prop}' from the ${altType} bindings (${altPkg}). This is likely a bug in our integration of the Next.js server runtime.`\n          )\n        },\n      }\n    )\n  }\n}\n\nlet ReactServerDOMTurbopackServer, ReactServerDOMWebpackServer\nlet ReactServerDOMTurbopackStatic, ReactServerDOMWebpackStatic\n\nif (process.env.TURBOPACK) {\n  ReactServerDOMTurbopackServer =\n    // @ts-expect-error -- TODO: Add types\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-turbopack/server') as typeof import('react-server-dom-turbopack/server')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackServer = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/server'\n    )\n  }\n  ReactServerDOMTurbopackStatic =\n    // @ts-expect-error -- TODO: Add types\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-turbopack/static') as typeof import('react-server-dom-turbopack/static')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackStatic = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/static'\n    )\n  }\n} else {\n  ReactServerDOMWebpackServer =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/server') as typeof import('react-server-dom-webpack/server')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackServer = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/server'\n    )\n  }\n  ReactServerDOMWebpackStatic =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/static') as typeof import('react-server-dom-webpack/static')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackStatic = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/static'\n    )\n  }\n}\n\nexport {\n  React,\n  ReactJsxDevRuntime,\n  ReactJsxRuntime,\n  ReactCompilerRuntime,\n  ReactDOM,\n  ReactServerDOMTurbopackServer,\n  ReactServerDOMTurbopackStatic,\n  ReactServerDOMWebpackServer,\n  ReactServerDOMWebpackStatic,\n}\n"], "names": ["React", "ReactCompilerRuntime", "ReactDOM", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactServerDOMTurbopackServer", "ReactServerDOMTurbopackStatic", "ReactServerDOMWebpackServer", "ReactServerDOMWebpackStatic", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "TURBOPACK", "require"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IA6EEA,KAAK;eAALA;;IAGAC,oBAAoB;eAApBA;;IACAC,QAAQ;eAARA;;IAHAC,kBAAkB;eAAlBA;;IACAC,eAAe;eAAfA;;IAGAC,6BAA6B;eAA7BA;;IACAC,6BAA6B;eAA7BA;;IACAC,2BAA2B;eAA3BA;;IACAC,2BAA2B;eAA3BA;;;+DArFqB;kEACG;uEACU;oEACH;yEACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,SAASC,0BACPC,IAA6B,EAC7BC,GAIqC;IAErC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC,GADnN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IAEJ;AACF;AAEA,IAAIX,+BAA+BE;AACnC,IAAID,+BAA+BE;AAEnC,IAAII,QAAQC,GAAG,CAACY,SAAS,EAAE;IACzBpB,gCACE,sCAAsC;IACtC,6DAA6D;IAC7DqB,QAAQ;IACV,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CP,8BAA8BE,0BAC5B,aACA;IAEJ;IACAH,gCACE,sCAAsC;IACtC,6DAA6D;IAC7DoB,QAAQ;IACV,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CN,8BAA8BC,0BAC5B,aACA;IAEJ;AACF,OAAO;IACLF,8BACE,6DAA6D;IAC7DmB,QAAQ;IACV,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CT,gCAAgCI,0BAC9B,WACA;IAEJ;IACAD,8BACE,6DAA6D;IAC7DkB,QAAQ;IACV,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CR,gCAAgCG,0BAC9B,WACA;IAEJ;AACF", "ignoreList": [0]}