{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/ssr/react-dom-server.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOMServer\n"], "names": ["module", "exports", "require", "vendored", "ReactDOMServer"], "mappings": ";AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,cAAc", "ignoreList": [0]}