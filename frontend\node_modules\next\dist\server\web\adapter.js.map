{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "sourcesContent": ["import type { RequestD<PERSON>, FetchEventResult } from './types'\nimport type { RequestInit } from './spec-extension/request'\nimport { PageSignatureError } from './error'\nimport { fromNodeOutgoingHttpHeaders, normalizeNextQueryParam } from './utils'\nimport {\n  NextFetchEvent,\n  getWaitUntilPromiseFromEvent,\n} from './spec-extension/fetch-event'\nimport { NextRequest } from './spec-extension/request'\nimport { NextResponse } from './spec-extension/response'\nimport {\n  parseRelativeURL,\n  getRelativeURL,\n} from '../../shared/lib/router/utils/relativize-url'\nimport { NextURL } from './next-url'\nimport { stripInternalSearchParams } from '../internal-utils'\nimport { normalizeRscURL } from '../../shared/lib/router/utils/app-paths'\nimport {\n  FLIGHT_HEADERS,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  RSC_HEADER,\n} from '../../client/components/app-router-headers'\nimport { ensureInstrumentationRegistered } from './globals'\nimport { createRequestStoreForAPI } from '../async-storage/request-store'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { createWorkStore } from '../async-storage/work-store'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { NEXT_ROUTER_PREFETCH_HEADER } from '../../client/components/app-router-headers'\nimport { getTracer } from '../lib/trace/tracer'\nimport type { TextMapGetter } from 'next/dist/compiled/@opentelemetry/api'\nimport { MiddlewareSpan } from '../lib/trace/constants'\nimport { CloseController } from './web-on-close'\nimport { getEdgePreviewProps } from './get-edge-preview-props'\nimport { getBuiltinRequestContext } from '../after/builtin-request-context'\nimport { getImplicitTags } from '../lib/implicit-tags'\n\nexport class NextRequestHint extends NextRequest {\n  sourcePage: string\n  fetchMetrics: FetchEventResult['fetchMetrics'] | undefined\n\n  constructor(params: {\n    init: RequestInit\n    input: Request | string\n    page: string\n  }) {\n    super(params.input, params.init)\n    this.sourcePage = params.page\n  }\n\n  get request() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  respondWith() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  waitUntil() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n}\n\nconst headersGetter: TextMapGetter<Headers> = {\n  keys: (headers) => Array.from(headers.keys()),\n  get: (headers, key) => headers.get(key) ?? undefined,\n}\n\nexport type AdapterOptions = {\n  handler: (req: NextRequestHint, event: NextFetchEvent) => Promise<Response>\n  page: string\n  request: RequestData\n  IncrementalCache?: typeof import('../lib/incremental-cache').IncrementalCache\n  incrementalCacheHandler?: typeof import('../lib/incremental-cache').CacheHandler\n  bypassNextUrl?: boolean\n}\n\nlet propagator: <T>(request: NextRequestHint, fn: () => T) => T = (\n  request,\n  fn\n) => {\n  const tracer = getTracer()\n  return tracer.withPropagatedContext(request.headers, fn, headersGetter)\n}\n\nlet testApisIntercepted = false\n\nfunction ensureTestApisIntercepted() {\n  if (!testApisIntercepted) {\n    testApisIntercepted = true\n    if (process.env.NEXT_PRIVATE_TEST_PROXY === 'true') {\n      const { interceptTestApis, wrapRequestHandler } =\n        // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n        require('next/dist/experimental/testmode/server-edge') as typeof import('../../experimental/testmode/server-edge')\n      interceptTestApis()\n      propagator = wrapRequestHandler(propagator)\n    }\n  }\n}\n\nexport async function adapter(\n  params: AdapterOptions\n): Promise<FetchEventResult> {\n  ensureTestApisIntercepted()\n  await ensureInstrumentationRegistered()\n\n  // TODO-APP: use explicit marker for this\n  const isEdgeRendering =\n    typeof (globalThis as any).__BUILD_MANIFEST !== 'undefined'\n\n  params.request.url = normalizeRscURL(params.request.url)\n\n  const requestURL = params.bypassNextUrl\n    ? new URL(params.request.url)\n    : new NextURL(params.request.url, {\n        headers: params.request.headers,\n        nextConfig: params.request.nextConfig,\n      })\n\n  // Iterator uses an index to keep track of the current iteration. Because of deleting and appending below we can't just use the iterator.\n  // Instead we use the keys before iteration.\n  const keys = [...requestURL.searchParams.keys()]\n  for (const key of keys) {\n    const value = requestURL.searchParams.getAll(key)\n\n    const normalizedKey = normalizeNextQueryParam(key)\n    if (normalizedKey) {\n      requestURL.searchParams.delete(normalizedKey)\n      for (const val of value) {\n        requestURL.searchParams.append(normalizedKey, val)\n      }\n      requestURL.searchParams.delete(key)\n    }\n  }\n\n  // Ensure users only see page requests, never data requests.\n  let buildId = process.env.__NEXT_BUILD_ID || ''\n  if ('buildId' in requestURL) {\n    buildId = (requestURL as NextURL).buildId || ''\n    requestURL.buildId = ''\n  }\n\n  const requestHeaders = fromNodeOutgoingHttpHeaders(params.request.headers)\n  const isNextDataRequest = requestHeaders.has('x-nextjs-data')\n  const isRSCRequest = requestHeaders.get(RSC_HEADER) === '1'\n\n  if (isNextDataRequest && requestURL.pathname === '/index') {\n    requestURL.pathname = '/'\n  }\n\n  const flightHeaders = new Map()\n\n  // Headers should only be stripped for middleware\n  if (!isEdgeRendering) {\n    for (const header of FLIGHT_HEADERS) {\n      const key = header.toLowerCase()\n      const value = requestHeaders.get(key)\n      if (value !== null) {\n        flightHeaders.set(key, value)\n        requestHeaders.delete(key)\n      }\n    }\n  }\n\n  const normalizeURL = process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n    ? new URL(params.request.url)\n    : requestURL\n\n  const request = new NextRequestHint({\n    page: params.page,\n    // Strip internal query parameters off the request.\n    input: stripInternalSearchParams(normalizeURL).toString(),\n    init: {\n      body: params.request.body,\n      headers: requestHeaders,\n      method: params.request.method,\n      nextConfig: params.request.nextConfig,\n      signal: params.request.signal,\n    },\n  })\n\n  /**\n   * This allows to identify the request as a data request. The user doesn't\n   * need to know about this property neither use it. We add it for testing\n   * purposes.\n   */\n  if (isNextDataRequest) {\n    Object.defineProperty(request, '__isData', {\n      enumerable: false,\n      value: true,\n    })\n  }\n\n  if (\n    // If we are inside of the next start sandbox\n    // leverage the shared instance if not we need\n    // to create a fresh cache instance each time\n    !(globalThis as any).__incrementalCacheShared &&\n    (params as any).IncrementalCache\n  ) {\n    ;(globalThis as any).__incrementalCache = new (\n      params as {\n        IncrementalCache: typeof import('../lib/incremental-cache').IncrementalCache\n      }\n    ).IncrementalCache({\n      CurCacheHandler: params.incrementalCacheHandler,\n      minimalMode: process.env.NODE_ENV !== 'development',\n      fetchCacheKeyPrefix: process.env.__NEXT_FETCH_CACHE_KEY_PREFIX,\n      dev: process.env.NODE_ENV === 'development',\n      requestHeaders: params.request.headers as any,\n\n      getPrerenderManifest: () => {\n        return {\n          version: -1 as any, // letting us know this doesn't conform to spec\n          routes: {},\n          dynamicRoutes: {},\n          notFoundRoutes: [],\n          preview: getEdgePreviewProps(),\n        }\n      },\n    })\n  }\n\n  // if we're in an edge runtime sandbox, we should use the waitUntil\n  // that we receive from the enclosing NextServer\n  const outerWaitUntil =\n    params.request.waitUntil ?? getBuiltinRequestContext()?.waitUntil\n\n  const event = new NextFetchEvent({\n    request,\n    page: params.page,\n    context: outerWaitUntil ? { waitUntil: outerWaitUntil } : undefined,\n  })\n  let response\n  let cookiesFromResponse\n\n  response = await propagator(request, () => {\n    // we only care to make async storage available for middleware\n    const isMiddleware =\n      params.page === '/middleware' || params.page === '/src/middleware'\n\n    if (isMiddleware) {\n      // if we're in an edge function, we only get a subset of `nextConfig` (no `experimental`),\n      // so we have to inject it via DefinePlugin.\n      // in `next start` this will be passed normally (see `NextNodeServer.runMiddleware`).\n\n      const waitUntil = event.waitUntil.bind(event)\n      const closeController = new CloseController()\n\n      return getTracer().trace(\n        MiddlewareSpan.execute,\n        {\n          spanName: `middleware ${request.method} ${request.nextUrl.pathname}`,\n          attributes: {\n            'http.target': request.nextUrl.pathname,\n            'http.method': request.method,\n          },\n        },\n        async () => {\n          try {\n            const onUpdateCookies = (cookies: Array<string>) => {\n              cookiesFromResponse = cookies\n            }\n            const previewProps = getEdgePreviewProps()\n            const page = '/' // Fake Work\n            const fallbackRouteParams = null\n\n            const implicitTags = await getImplicitTags(\n              page,\n              request.nextUrl,\n              fallbackRouteParams\n            )\n\n            const requestStore = createRequestStoreForAPI(\n              request,\n              request.nextUrl,\n              implicitTags,\n              onUpdateCookies,\n              previewProps\n            )\n\n            const workStore = createWorkStore({\n              page,\n              fallbackRouteParams,\n              renderOpts: {\n                cacheLifeProfiles:\n                  params.request.nextConfig?.experimental?.cacheLife,\n                experimental: {\n                  isRoutePPREnabled: false,\n                  dynamicIO: false,\n                  authInterrupts:\n                    !!params.request.nextConfig?.experimental?.authInterrupts,\n                },\n                supportsDynamicResponse: true,\n                waitUntil,\n                onClose: closeController.onClose.bind(closeController),\n                onAfterTaskError: undefined,\n              },\n              requestEndedState: { ended: false },\n              isPrefetchRequest: request.headers.has(\n                NEXT_ROUTER_PREFETCH_HEADER\n              ),\n              buildId: buildId ?? '',\n              previouslyRevalidatedTags: [],\n            })\n\n            return await workAsyncStorage.run(workStore, () =>\n              workUnitAsyncStorage.run(\n                requestStore,\n                params.handler,\n                request,\n                event\n              )\n            )\n          } finally {\n            // middleware cannot stream, so we can consider the response closed\n            // as soon as the handler returns.\n            // we can delay running it until a bit later --\n            // if it's needed, we'll have a `waitUntil` lock anyway.\n            setTimeout(() => {\n              closeController.dispatchClose()\n            }, 0)\n          }\n        }\n      )\n    }\n    return params.handler(request, event)\n  })\n\n  // check if response is a Response object\n  if (response && !(response instanceof Response)) {\n    throw new TypeError('Expected an instance of Response to be returned')\n  }\n\n  if (response && cookiesFromResponse) {\n    response.headers.set('set-cookie', cookiesFromResponse)\n  }\n\n  /**\n   * For rewrites we must always include the locale in the final pathname\n   * so we re-create the NextURL forcing it to include it when the it is\n   * an internal rewrite. Also we make sure the outgoing rewrite URL is\n   * a data URL if the request was a data request.\n   */\n  const rewrite = response?.headers.get('x-middleware-rewrite')\n  if (response && rewrite && (isRSCRequest || !isEdgeRendering)) {\n    const destination = new NextURL(rewrite, {\n      forceLocale: true,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE && !isEdgeRendering) {\n      if (destination.host === request.nextUrl.host) {\n        destination.buildId = buildId || destination.buildId\n        response.headers.set('x-middleware-rewrite', String(destination))\n      }\n    }\n\n    /**\n     * When the request is a data request we must show if there was a rewrite\n     * with an internal header so the client knows which component to load\n     * from the data request.\n     */\n    const { url: relativeDestination, isRelative } = parseRelativeURL(\n      destination.toString(),\n      requestURL.toString()\n    )\n\n    if (\n      !isEdgeRendering &&\n      isNextDataRequest &&\n      // if the rewrite is external and external rewrite\n      // resolving config is enabled don't add this header\n      // so the upstream app can set it instead\n      !(\n        process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE &&\n        relativeDestination.match(/http(s)?:\\/\\//)\n      )\n    ) {\n      response.headers.set('x-nextjs-rewrite', relativeDestination)\n    }\n\n    // If this is an RSC request, and the pathname or search has changed, and\n    // this isn't an external rewrite, we need to set the rewritten pathname and\n    // query headers.\n    if (isRSCRequest && isRelative) {\n      if (requestURL.pathname !== destination.pathname) {\n        response.headers.set(NEXT_REWRITTEN_PATH_HEADER, destination.pathname)\n      }\n      if (requestURL.search !== destination.search) {\n        response.headers.set(\n          NEXT_REWRITTEN_QUERY_HEADER,\n          // remove the leading ? from the search string\n          destination.search.slice(1)\n        )\n      }\n    }\n  }\n\n  /**\n   * For redirects we will not include the locale in case when it is the\n   * default and we must also make sure the outgoing URL is a data one if\n   * the incoming request was a data request.\n   */\n  const redirect = response?.headers.get('Location')\n  if (response && redirect && !isEdgeRendering) {\n    const redirectURL = new NextURL(redirect, {\n      forceLocale: false,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    /**\n     * Responses created from redirects have immutable headers so we have\n     * to clone the response to be able to modify it.\n     */\n    response = new Response(response.body, response)\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE) {\n      if (redirectURL.host === requestURL.host) {\n        redirectURL.buildId = buildId || redirectURL.buildId\n        response.headers.set('Location', redirectURL.toString())\n      }\n    }\n\n    /**\n     * When the request is a data request we can't use the location header as\n     * it may end up with CORS error. Instead we map to an internal header so\n     * the client knows the destination.\n     */\n    if (isNextDataRequest) {\n      response.headers.delete('Location')\n      response.headers.set(\n        'x-nextjs-redirect',\n        getRelativeURL(redirectURL.toString(), requestURL.toString())\n      )\n    }\n  }\n\n  const finalResponse = response ? response : NextResponse.next()\n\n  // Flight headers are not overridable / removable so they are applied at the end.\n  const middlewareOverrideHeaders = finalResponse.headers.get(\n    'x-middleware-override-headers'\n  )\n  const overwrittenHeaders: string[] = []\n  if (middlewareOverrideHeaders) {\n    for (const [key, value] of flightHeaders) {\n      finalResponse.headers.set(`x-middleware-request-${key}`, value)\n      overwrittenHeaders.push(key)\n    }\n\n    if (overwrittenHeaders.length > 0) {\n      finalResponse.headers.set(\n        'x-middleware-override-headers',\n        middlewareOverrideHeaders + ',' + overwrittenHeaders.join(',')\n      )\n    }\n  }\n\n  return {\n    response: finalResponse,\n    waitUntil: getWaitUntilPromiseFromEvent(event) ?? Promise.resolve(),\n    fetchMetrics: request.fetchMetrics,\n  }\n}\n"], "names": ["NextRequestHint", "adapter", "NextRequest", "constructor", "params", "input", "init", "sourcePage", "page", "request", "PageSignatureError", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "getTracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "getBuiltinRequestContext", "ensureInstrumentationRegistered", "isEdgeRendering", "globalThis", "__BUILD_MANIFEST", "url", "normalizeRscURL", "requestURL", "bypassNextUrl", "URL", "NextURL", "nextConfig", "searchParams", "value", "getAll", "normalizedKey", "normalizeNextQueryParam", "delete", "val", "append", "buildId", "__NEXT_BUILD_ID", "requestHeaders", "fromNodeOutgoingHttpHeaders", "isNextDataRequest", "has", "isRSCRequest", "RSC_HEADER", "pathname", "flightHeaders", "Map", "header", "FLIGHT_HEADERS", "toLowerCase", "set", "normalizeURL", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "stripInternalSearchParams", "toString", "body", "method", "signal", "Object", "defineProperty", "enumerable", "__incrementalCacheShared", "IncrementalCache", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "getEdgePreviewProps", "outerWaitUntil", "event", "NextFetchEvent", "context", "response", "cookiesFromResponse", "isMiddleware", "bind", "closeController", "CloseController", "trace", "MiddlewareSpan", "execute", "spanName", "nextUrl", "attributes", "onUpdateCookies", "cookies", "previewProps", "fallbackRouteParams", "implicitTags", "getImplicitTags", "requestStore", "createRequestStoreForAPI", "workStore", "createWorkStore", "renderOpts", "cacheLifeProfiles", "experimental", "cacheLife", "isRoutePPREnabled", "dynamicIO", "authInterrupts", "supportsDynamicResponse", "onClose", "onAfterTaskError", "requestEndedState", "ended", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "previouslyRevalidatedTags", "workAsyncStorage", "run", "workUnitAsyncStorage", "handler", "setTimeout", "dispatchClose", "Response", "TypeError", "rewrite", "destination", "forceLocale", "host", "String", "relativeDestination", "isRelative", "parseRelativeURL", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "NEXT_REWRITTEN_PATH_HEADER", "search", "NEXT_REWRITTEN_QUERY_HEADER", "slice", "redirect", "redirectURL", "getRelativeURL", "finalResponse", "NextResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "length", "join", "getWaitUntilPromiseFromEvent", "Promise", "resolve", "fetchMetrics"], "mappings": ";;;;;;;;;;;;;;;IAqCaA,eAAe;eAAfA;;IA+DSC,OAAO;eAAPA;;;uBAlGa;uBACkC;4BAI9D;yBACqB;0BACC;+BAItB;yBACiB;+BACkB;0BACV;kCAMzB;yBACyC;8BACP;8CACJ;2BACL;0CACC;wBAEP;2BAEK;4BACC;qCACI;uCACK;8BACT;AAEzB,MAAMD,wBAAwBE,oBAAW;IAI9CC,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,qBAAiD,CAAjD,IAAIC,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAI,cAAc;QACZ,MAAM,qBAAiD,CAAjD,IAAID,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAK,YAAY;QACV,MAAM,qBAAiD,CAAjD,IAAIF,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;AACF;AAEA,MAAMM,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AAWA,IAAIC,aAA8D,CAChEZ,SACAa;IAEA,MAAMC,SAASC,IAAAA,iBAAS;IACxB,OAAOD,OAAOE,qBAAqB,CAAChB,QAAQM,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIa,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAE,GAC7C,sHAAsH;YACtHC,QAAQ;YACVF;YACAV,aAAaW,mBAAmBX;QAClC;IACF;AACF;AAEO,eAAepB,QACpBG,MAAsB;QA6HQ8B;IA3H9BP;IACA,MAAMQ,IAAAA,wCAA+B;IAErC,yCAAyC;IACzC,MAAMC,kBACJ,OAAO,AAACC,WAAmBC,gBAAgB,KAAK;IAElDlC,OAAOK,OAAO,CAAC8B,GAAG,GAAGC,IAAAA,yBAAe,EAACpC,OAAOK,OAAO,CAAC8B,GAAG;IAEvD,MAAME,aAAarC,OAAOsC,aAAa,GACnC,IAAIC,IAAIvC,OAAOK,OAAO,CAAC8B,GAAG,IAC1B,IAAIK,gBAAO,CAACxC,OAAOK,OAAO,CAAC8B,GAAG,EAAE;QAC9BxB,SAASX,OAAOK,OAAO,CAACM,OAAO;QAC/B8B,YAAYzC,OAAOK,OAAO,CAACoC,UAAU;IACvC;IAEJ,yIAAyI;IACzI,4CAA4C;IAC5C,MAAM/B,OAAO;WAAI2B,WAAWK,YAAY,CAAChC,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAMiC,QAAQN,WAAWK,YAAY,CAACE,MAAM,CAAC7B;QAE7C,MAAM8B,gBAAgBC,IAAAA,8BAAuB,EAAC/B;QAC9C,IAAI8B,eAAe;YACjBR,WAAWK,YAAY,CAACK,MAAM,CAACF;YAC/B,KAAK,MAAMG,OAAOL,MAAO;gBACvBN,WAAWK,YAAY,CAACO,MAAM,CAACJ,eAAeG;YAChD;YACAX,WAAWK,YAAY,CAACK,MAAM,CAAChC;QACjC;IACF;IAEA,4DAA4D;IAC5D,IAAImC,UAAU1B,QAAQC,GAAG,CAAC0B,eAAe,IAAI;IAC7C,IAAI,aAAad,YAAY;QAC3Ba,UAAU,AAACb,WAAuBa,OAAO,IAAI;QAC7Cb,WAAWa,OAAO,GAAG;IACvB;IAEA,MAAME,iBAAiBC,IAAAA,kCAA2B,EAACrD,OAAOK,OAAO,CAACM,OAAO;IACzE,MAAM2C,oBAAoBF,eAAeG,GAAG,CAAC;IAC7C,MAAMC,eAAeJ,eAAetC,GAAG,CAAC2C,4BAAU,MAAM;IAExD,IAAIH,qBAAqBjB,WAAWqB,QAAQ,KAAK,UAAU;QACzDrB,WAAWqB,QAAQ,GAAG;IACxB;IAEA,MAAMC,gBAAgB,IAAIC;IAE1B,iDAAiD;IACjD,IAAI,CAAC5B,iBAAiB;QACpB,KAAK,MAAM6B,UAAUC,gCAAc,CAAE;YACnC,MAAM/C,MAAM8C,OAAOE,WAAW;YAC9B,MAAMpB,QAAQS,eAAetC,GAAG,CAACC;YACjC,IAAI4B,UAAU,MAAM;gBAClBgB,cAAcK,GAAG,CAACjD,KAAK4B;gBACvBS,eAAeL,MAAM,CAAChC;YACxB;QACF;IACF;IAEA,MAAMkD,eAAezC,QAAQC,GAAG,CAACyC,kCAAkC,GAC/D,IAAI3B,IAAIvC,OAAOK,OAAO,CAAC8B,GAAG,IAC1BE;IAEJ,MAAMhC,UAAU,IAAIT,gBAAgB;QAClCQ,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOkE,IAAAA,wCAAyB,EAACF,cAAcG,QAAQ;QACvDlE,MAAM;YACJmE,MAAMrE,OAAOK,OAAO,CAACgE,IAAI;YACzB1D,SAASyC;YACTkB,QAAQtE,OAAOK,OAAO,CAACiE,MAAM;YAC7B7B,YAAYzC,OAAOK,OAAO,CAACoC,UAAU;YACrC8B,QAAQvE,OAAOK,OAAO,CAACkE,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIjB,mBAAmB;QACrBkB,OAAOC,cAAc,CAACpE,SAAS,YAAY;YACzCqE,YAAY;YACZ/B,OAAO;QACT;IACF;IAEA,IACE,6CAA6C;IAC7C,8CAA8C;IAC9C,6CAA6C;IAC7C,CAAC,AAACV,WAAmB0C,wBAAwB,IAC7C,AAAC3E,OAAe4E,gBAAgB,EAChC;;QACE3C,WAAmB4C,kBAAkB,GAAG,IAAI,AAC5C7E,OAGA4E,gBAAgB,CAAC;YACjBE,iBAAiB9E,OAAO+E,uBAAuB;YAC/CC,aAAaxD,QAAQC,GAAG,CAACwD,QAAQ,KAAK;YACtCC,qBAAqB1D,QAAQC,GAAG,CAAC0D,6BAA6B;YAC9DC,KAAK5D,QAAQC,GAAG,CAACwD,QAAQ,KAAK;YAC9B7B,gBAAgBpD,OAAOK,OAAO,CAACM,OAAO;YAEtC0E,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASC,IAAAA,wCAAmB;gBAC9B;YACF;QACF;IACF;IAEA,mEAAmE;IACnE,gDAAgD;IAChD,MAAMC,iBACJ5F,OAAOK,OAAO,CAACG,SAAS,MAAIsB,4BAAAA,IAAAA,+CAAwB,wBAAxBA,0BAA4BtB,SAAS;IAEnE,MAAMqF,QAAQ,IAAIC,0BAAc,CAAC;QAC/BzF;QACAD,MAAMJ,OAAOI,IAAI;QACjB2F,SAASH,iBAAiB;YAAEpF,WAAWoF;QAAe,IAAI5E;IAC5D;IACA,IAAIgF;IACJ,IAAIC;IAEJD,WAAW,MAAM/E,WAAWZ,SAAS;QACnC,8DAA8D;QAC9D,MAAM6F,eACJlG,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QAEnD,IAAI8F,cAAc;YAChB,0FAA0F;YAC1F,4CAA4C;YAC5C,qFAAqF;YAErF,MAAM1F,YAAYqF,MAAMrF,SAAS,CAAC2F,IAAI,CAACN;YACvC,MAAMO,kBAAkB,IAAIC,2BAAe;YAE3C,OAAOjF,IAAAA,iBAAS,IAAGkF,KAAK,CACtBC,yBAAc,CAACC,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAEpG,QAAQiE,MAAM,CAAC,CAAC,EAAEjE,QAAQqG,OAAO,CAAChD,QAAQ,EAAE;gBACpEiD,YAAY;oBACV,eAAetG,QAAQqG,OAAO,CAAChD,QAAQ;oBACvC,eAAerD,QAAQiE,MAAM;gBAC/B;YACF,GACA;gBACE,IAAI;wBA2BItE,yCAAAA,4BAKIA,0CAAAA;oBA/BV,MAAM4G,kBAAkB,CAACC;wBACvBZ,sBAAsBY;oBACxB;oBACA,MAAMC,eAAenB,IAAAA,wCAAmB;oBACxC,MAAMvF,OAAO,IAAI,YAAY;;oBAC7B,MAAM2G,sBAAsB;oBAE5B,MAAMC,eAAe,MAAMC,IAAAA,6BAAe,EACxC7G,MACAC,QAAQqG,OAAO,EACfK;oBAGF,MAAMG,eAAeC,IAAAA,sCAAwB,EAC3C9G,SACAA,QAAQqG,OAAO,EACfM,cACAJ,iBACAE;oBAGF,MAAMM,YAAYC,IAAAA,0BAAe,EAAC;wBAChCjH;wBACA2G;wBACAO,YAAY;4BACVC,iBAAiB,GACfvH,6BAAAA,OAAOK,OAAO,CAACoC,UAAU,sBAAzBzC,0CAAAA,2BAA2BwH,YAAY,qBAAvCxH,wCAAyCyH,SAAS;4BACpDD,cAAc;gCACZE,mBAAmB;gCACnBC,WAAW;gCACXC,gBACE,CAAC,GAAC5H,8BAAAA,OAAOK,OAAO,CAACoC,UAAU,sBAAzBzC,2CAAAA,4BAA2BwH,YAAY,qBAAvCxH,yCAAyC4H,cAAc;4BAC7D;4BACAC,yBAAyB;4BACzBrH;4BACAsH,SAAS1B,gBAAgB0B,OAAO,CAAC3B,IAAI,CAACC;4BACtC2B,kBAAkB/G;wBACpB;wBACAgH,mBAAmB;4BAAEC,OAAO;wBAAM;wBAClCC,mBAAmB7H,QAAQM,OAAO,CAAC4C,GAAG,CACpC4E,6CAA2B;wBAE7BjF,SAASA,WAAW;wBACpBkF,2BAA2B,EAAE;oBAC/B;oBAEA,OAAO,MAAMC,0CAAgB,CAACC,GAAG,CAAClB,WAAW,IAC3CmB,kDAAoB,CAACD,GAAG,CACtBpB,cACAlH,OAAOwI,OAAO,EACdnI,SACAwF;gBAGN,SAAU;oBACR,mEAAmE;oBACnE,kCAAkC;oBAClC,+CAA+C;oBAC/C,wDAAwD;oBACxD4C,WAAW;wBACTrC,gBAAgBsC,aAAa;oBAC/B,GAAG;gBACL;YACF;QAEJ;QACA,OAAO1I,OAAOwI,OAAO,CAACnI,SAASwF;IACjC;IAEA,yCAAyC;IACzC,IAAIG,YAAY,CAAEA,CAAAA,oBAAoB2C,QAAO,GAAI;QAC/C,MAAM,qBAAgE,CAAhE,IAAIC,UAAU,oDAAd,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,IAAI5C,YAAYC,qBAAqB;QACnCD,SAASrF,OAAO,CAACqD,GAAG,CAAC,cAAciC;IACrC;IAEA;;;;;GAKC,GACD,MAAM4C,UAAU7C,4BAAAA,SAAUrF,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIkF,YAAY6C,WAAYrF,CAAAA,gBAAgB,CAACxB,eAAc,GAAI;QAC7D,MAAM8G,cAAc,IAAItG,gBAAO,CAACqG,SAAS;YACvCE,aAAa;YACbpI,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B8B,YAAYzC,OAAOK,OAAO,CAACoC,UAAU;QACvC;QAEA,IAAI,CAACjB,QAAQC,GAAG,CAACyC,kCAAkC,IAAI,CAAClC,iBAAiB;YACvE,IAAI8G,YAAYE,IAAI,KAAK3I,QAAQqG,OAAO,CAACsC,IAAI,EAAE;gBAC7CF,YAAY5F,OAAO,GAAGA,WAAW4F,YAAY5F,OAAO;gBACpD8C,SAASrF,OAAO,CAACqD,GAAG,CAAC,wBAAwBiF,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAM,EAAE3G,KAAK+G,mBAAmB,EAAEC,UAAU,EAAE,GAAGC,IAAAA,+BAAgB,EAC/DN,YAAY1E,QAAQ,IACpB/B,WAAW+B,QAAQ;QAGrB,IACE,CAACpC,mBACDsB,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACE9B,CAAAA,QAAQC,GAAG,CAAC4H,0CAA0C,IACtDH,oBAAoBI,KAAK,CAAC,gBAAe,GAE3C;YACAtD,SAASrF,OAAO,CAACqD,GAAG,CAAC,oBAAoBkF;QAC3C;QAEA,yEAAyE;QACzE,4EAA4E;QAC5E,iBAAiB;QACjB,IAAI1F,gBAAgB2F,YAAY;YAC9B,IAAI9G,WAAWqB,QAAQ,KAAKoF,YAAYpF,QAAQ,EAAE;gBAChDsC,SAASrF,OAAO,CAACqD,GAAG,CAACuF,4CAA0B,EAAET,YAAYpF,QAAQ;YACvE;YACA,IAAIrB,WAAWmH,MAAM,KAAKV,YAAYU,MAAM,EAAE;gBAC5CxD,SAASrF,OAAO,CAACqD,GAAG,CAClByF,6CAA2B,EAC3B,8CAA8C;gBAC9CX,YAAYU,MAAM,CAACE,KAAK,CAAC;YAE7B;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMC,WAAW3D,4BAAAA,SAAUrF,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIkF,YAAY2D,YAAY,CAAC3H,iBAAiB;QAC5C,MAAM4H,cAAc,IAAIpH,gBAAO,CAACmH,UAAU;YACxCZ,aAAa;YACbpI,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B8B,YAAYzC,OAAOK,OAAO,CAACoC,UAAU;QACvC;QAEA;;;KAGC,GACDuD,WAAW,IAAI2C,SAAS3C,SAAS3B,IAAI,EAAE2B;QAEvC,IAAI,CAACxE,QAAQC,GAAG,CAACyC,kCAAkC,EAAE;YACnD,IAAI0F,YAAYZ,IAAI,KAAK3G,WAAW2G,IAAI,EAAE;gBACxCY,YAAY1G,OAAO,GAAGA,WAAW0G,YAAY1G,OAAO;gBACpD8C,SAASrF,OAAO,CAACqD,GAAG,CAAC,YAAY4F,YAAYxF,QAAQ;YACvD;QACF;QAEA;;;;KAIC,GACD,IAAId,mBAAmB;YACrB0C,SAASrF,OAAO,CAACoC,MAAM,CAAC;YACxBiD,SAASrF,OAAO,CAACqD,GAAG,CAClB,qBACA6F,IAAAA,6BAAc,EAACD,YAAYxF,QAAQ,IAAI/B,WAAW+B,QAAQ;QAE9D;IACF;IAEA,MAAM0F,gBAAgB9D,WAAWA,WAAW+D,sBAAY,CAACC,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BH,cAAcnJ,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMoJ,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAAClJ,KAAK4B,MAAM,IAAIgB,cAAe;YACxCmG,cAAcnJ,OAAO,CAACqD,GAAG,CAAC,CAAC,qBAAqB,EAAEjD,KAAK,EAAE4B;YACzDuH,mBAAmBC,IAAI,CAACpJ;QAC1B;QAEA,IAAImJ,mBAAmBE,MAAM,GAAG,GAAG;YACjCN,cAAcnJ,OAAO,CAACqD,GAAG,CACvB,iCACAiG,4BAA4B,MAAMC,mBAAmBG,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLrE,UAAU8D;QACVtJ,WAAW8J,IAAAA,wCAA4B,EAACzE,UAAU0E,QAAQC,OAAO;QACjEC,cAAcpK,QAAQoK,YAAY;IACpC;AACF", "ignoreList": [0]}