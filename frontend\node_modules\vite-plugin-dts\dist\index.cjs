'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

const node_path = require('node:path');
const node_fs = require('node:fs');
const promises = require('node:fs/promises');
const node_os = require('node:os');
const languageCore = require('@vue/language-core');
const typescript = require('@volar/typescript');
const ts = require('typescript');
const node_module = require('node:module');
const localPkg = require('local-pkg');
const pluginutils = require('@rollup/pluginutils');
const debug = require('debug');
const kolorist = require('kolorist');
const apiExtractor = require('@microsoft/api-extractor');
const compareVersions = require('compare-versions');
const MagicString = require('magic-string');

var _documentCurrentScript = typeof document !== 'undefined' ? document.currentScript : null;
function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const ts__default = /*#__PURE__*/_interopDefaultCompat(ts);
const debug__default = /*#__PURE__*/_interopDefaultCompat(debug);
const MagicString__default = /*#__PURE__*/_interopDefaultCompat(MagicString);

const windowsSlashRE = /\\+/g;
function slash(p) {
  return p.replace(windowsSlashRE, "/");
}
function resolveConfigDir(path, configDir) {
  return path.replace("${configDir}", configDir);
}
function normalizePath(id) {
  return node_path.posix.normalize(slash(id));
}
function resolve(...paths) {
  return normalizePath(node_path.resolve(...paths));
}
function isNativeObj(value) {
  return Object.prototype.toString.call(value) === "[object Object]";
}
function isRegExp(value) {
  return Object.prototype.toString.call(value) === "[object RegExp]";
}
function isPromise(value) {
  return !!value && (typeof value === "function" || typeof value === "object") && typeof value.then === "function";
}
async function unwrapPromise(maybePromise) {
  return isPromise(maybePromise) ? await maybePromise : maybePromise;
}
function ensureAbsolute(path, root) {
  return normalizePath(path ? node_path.isAbsolute(path) ? path : resolve(root, path) : root);
}
function ensureArray(value) {
  return Array.isArray(value) ? value : value ? [value] : [];
}
async function runParallel(maxConcurrency, source, iteratorFn) {
  const ret = [];
  const executing = [];
  for (const item of source) {
    const p = Promise.resolve().then(() => iteratorFn(item, source));
    ret.push(p);
    if (maxConcurrency <= source.length) {
      const e = p.then(() => executing.splice(executing.indexOf(e), 1));
      executing.push(e);
      if (executing.length >= maxConcurrency) {
        await Promise.race(executing);
      }
    }
  }
  return Promise.all(ret);
}
const speRE = /[\\/]/;
function queryPublicPath(paths) {
  if (paths.length === 0) {
    return "";
  } else if (paths.length === 1) {
    return node_path.dirname(paths[0]);
  }
  let publicPath = node_path.normalize(node_path.dirname(paths[0])) + node_path.sep;
  let publicUnits = publicPath.split(speRE);
  let index = publicUnits.length - 1;
  for (const path of paths.slice(1)) {
    if (!index) {
      return publicPath;
    }
    const dirPath = node_path.normalize(node_path.dirname(path)) + node_path.sep;
    if (dirPath.startsWith(publicPath)) {
      continue;
    }
    const units = dirPath.split(speRE);
    if (units.length < index) {
      publicPath = dirPath;
      publicUnits = units;
      continue;
    }
    for (let i = 0; i <= index; ++i) {
      if (publicUnits[i] !== units[i]) {
        if (!i) {
          return "";
        }
        index = i - 1;
        publicUnits = publicUnits.slice(0, index + 1);
        publicPath = publicUnits.join(node_path.sep) + node_path.sep;
        break;
      }
    }
  }
  return publicPath.slice(0, -1);
}
function removeDirIfEmpty(dir) {
  if (!node_fs.existsSync(dir)) {
    return;
  }
  let onlyHasDir = true;
  for (const file of node_fs.readdirSync(dir)) {
    const abs = resolve(dir, file);
    if (node_fs.lstatSync(abs).isDirectory()) {
      if (!removeDirIfEmpty(abs)) {
        onlyHasDir = false;
      }
    } else {
      onlyHasDir = false;
    }
  }
  if (onlyHasDir) {
    node_fs.rmdirSync(dir);
  }
  return onlyHasDir;
}
function getTsConfig(tsConfigPath, readFileSync) {
  const baseConfig = ts__default.readConfigFile(tsConfigPath, readFileSync).config ?? {};
  const tsConfig = {
    ...baseConfig,
    compilerOptions: {}
  };
  if (tsConfig.extends) {
    ensureArray(tsConfig.extends).forEach((configPath) => {
      const config = getTsConfig(ensureAbsolute(configPath, node_path.dirname(tsConfigPath)), readFileSync);
      Object.assign(tsConfig.compilerOptions, config.compilerOptions);
      if (!tsConfig.include) {
        tsConfig.include = config.include;
      }
      if (!tsConfig.exclude) {
        tsConfig.exclude = config.exclude;
      }
    });
  }
  Object.assign(tsConfig.compilerOptions, baseConfig.compilerOptions);
  return tsConfig;
}
function getTsLibFolder() {
  const libFolder = tryGetPackageInfo("typescript")?.rootPath;
  return libFolder && normalizePath(libFolder);
}
const BASE64_ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");
function base64Encode(number) {
  if (number >= 0 && number < BASE64_ALPHABET.length) {
    return BASE64_ALPHABET[number];
  }
  throw new TypeError("Base64 integer must be between 0 and 63: " + number);
}
const VLQ_BASE_SHIFT = 5;
const VLQ_BASE = 1 << VLQ_BASE_SHIFT;
const VLQ_BASE_MASK = VLQ_BASE - 1;
const VLQ_CONTINUATION_BIT = VLQ_BASE;
function toVLQSigned(number) {
  return number < 0 ? (-number << 1) + 1 : (number << 1) + 0;
}
function base64VLQEncode(numbers) {
  let encoded = "";
  for (const number of numbers) {
    let vlq = toVLQSigned(number);
    let digit;
    do {
      digit = vlq & VLQ_BASE_MASK;
      vlq >>>= VLQ_BASE_SHIFT;
      if (vlq > 0) {
        digit |= VLQ_CONTINUATION_BIT;
      }
      encoded += base64Encode(digit);
    } while (vlq > 0);
  }
  return encoded;
}
const pkgPathCache = /* @__PURE__ */ new Map();
function tryGetPkgPath(beginPath) {
  beginPath = normalizePath(beginPath);
  if (pkgPathCache.has(beginPath)) {
    return pkgPathCache.get(beginPath);
  }
  const pkgPath = resolve(beginPath, "package.json");
  if (node_fs.existsSync(pkgPath)) {
    pkgPathCache.set(beginPath, pkgPath);
    return pkgPath;
  }
  const parentDir = normalizePath(node_path.dirname(beginPath));
  if (!parentDir || parentDir === beginPath) {
    pkgPathCache.set(beginPath, undefined);
    return;
  }
  return tryGetPkgPath(parentDir);
}
function toCapitalCase(value) {
  value = value.trim().replace(/\s+/g, "-");
  value = value.replace(/-+(\w)/g, (_, char) => char ? char.toUpperCase() : "");
  return (value.charAt(0).toLocaleUpperCase() + value.slice(1)).replace(
    /[^\w]/g,
    ""
  );
}
function findTypesPath(...pkgs) {
  let path;
  for (const pkg of pkgs) {
    if (typeof pkg !== "object") continue;
    path = pkg.types || pkg.typings || pkg.exports?.types || pkg.exports?.["."]?.types || pkg.exports?.["./"]?.types;
    if (path) return path;
  }
}
function setModuleResolution(options) {
  if (options.moduleResolution) return;
  const module = typeof options.module === "number" ? options.module : options.target ?? ts__default.ScriptTarget.ES5 >= 2 ? ts__default.ModuleKind.ES2015 : ts__default.ModuleKind.CommonJS;
  let moduleResolution;
  switch (module) {
    case ts__default.ModuleKind.CommonJS:
      moduleResolution = ts__default.ModuleResolutionKind.Node10;
      break;
    case ts__default.ModuleKind.Node16:
      moduleResolution = ts__default.ModuleResolutionKind.Node16;
      break;
    case ts__default.ModuleKind.NodeNext:
      moduleResolution = ts__default.ModuleResolutionKind.NodeNext;
      break;
    default:
      moduleResolution = ts__default.version.startsWith("5") ? ts__default.ModuleResolutionKind.Bundler : ts__default.ModuleResolutionKind.Classic;
      break;
  }
  options.moduleResolution = moduleResolution;
}
function editSourceMapDir(content, fromDir, toDir) {
  const relativeOutDir = node_path.relative(fromDir, toDir);
  if (relativeOutDir) {
    try {
      const sourceMap = JSON.parse(content);
      sourceMap.sources = sourceMap.sources.map((source) => {
        return normalizePath(node_path.relative(relativeOutDir, source));
      });
      return JSON.stringify(sourceMap);
    } catch (e) {
      return false;
    }
  }
  return true;
}
const regexpSymbolRE = /([$.\\+?()[\]!<=|{}^,])/g;
const asteriskRE = /[*]+/g;
function parseTsAliases(basePath, paths) {
  const result = [];
  for (const [pathWithAsterisk, replacements] of Object.entries(paths)) {
    const find = new RegExp(
      `^${pathWithAsterisk.replace(regexpSymbolRE, "\\$1").replace(asteriskRE, "(?!\\.{1,2}\\/)([^*]+)")}$`
    );
    let index = 1;
    result.push({
      find,
      replacement: ensureAbsolute(
        replacements[0].replace(asteriskRE, () => `$${index++}`),
        basePath
      )
    });
  }
  return result;
}
const rootAsteriskImportRE = /^(?!\.{1,2}\/)([^*]+)$/;
function isAliasGlobal(alias) {
  return alias.find.toString() === rootAsteriskImportRE.toString();
}
function importResolves(path) {
  const files = [
    // js
    ".js",
    ".jsx",
    ".mjs",
    ".cjs",
    // ts
    ".ts",
    ".tsx",
    ".mts",
    ".cts",
    ".d.ts",
    // json
    ".json",
    // vue
    ".vue",
    ".vue.d.ts",
    // svelte
    ".svelte"
  ];
  for (const ext of files) {
    if (node_fs.existsSync(path + ext)) {
      return true;
    }
  }
  return false;
}
function tryGetPackageInfo(name) {
  if (process.versions.pnp) {
    const targetRequire = node_module.createRequire((typeof document === 'undefined' ? require('u' + 'rl').pathToFileURL(__filename).href : (_documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === 'SCRIPT' && _documentCurrentScript.src || new URL('index.cjs', document.baseURI).href)));
    try {
      return localPkg.getPackageInfoSync(
        targetRequire.resolve(`${name}/package.json`, { paths: [process.cwd()] })
      );
    } catch (e) {
    }
  }
  try {
    return localPkg.getPackageInfoSync(name) ?? localPkg.getPackageInfoSync(name, { paths: [localPkg.resolveModule(name) || process.cwd()] });
  } catch (e) {
  }
}

const hasVue = !!tryGetPackageInfo("vue");
const createProgram = !hasVue ? ts__default.createProgram : typescript.proxyCreateProgram(ts__default, ts__default.createProgram, (ts2, options) => {
  const { configFilePath } = options.options;
  const vueOptions = typeof configFilePath === "string" ? languageCore.createParsedCommandLine(ts2, ts2.sys, configFilePath.replace(/\\/g, "/")).vueOptions : languageCore.resolveVueCompilerOptions({});
  const vueLanguagePlugin = languageCore.createVueLanguagePlugin(
    ts2,
    options.options,
    vueOptions,
    (id) => id
  );
  return [vueLanguagePlugin];
});

const dtsRE$1 = /\.d\.(m|c)?tsx?$/;
function rollupDeclarationFiles({
  root,
  configPath,
  compilerOptions,
  outDir,
  entryPath,
  fileName,
  libFolder,
  rollupConfig = {},
  rollupOptions = {}
}) {
  const configObjectFullPath = resolve(root, "api-extractor.json");
  if (!dtsRE$1.test(fileName)) {
    fileName += ".d.ts";
  }
  if (/preserve/i.test(compilerOptions.module)) {
    compilerOptions = { ...compilerOptions, module: "ESNext" };
  }
  const extractorConfig = apiExtractor.ExtractorConfig.prepare({
    configObject: {
      ...rollupConfig,
      projectFolder: root,
      mainEntryPointFilePath: entryPath,
      compiler: {
        tsconfigFilePath: configPath,
        overrideTsconfig: {
          $schema: "http://json.schemastore.org/tsconfig",
          compilerOptions
        }
      },
      apiReport: {
        enabled: false,
        reportFileName: "<unscopedPackageName>.api.md",
        ...rollupConfig.apiReport
      },
      docModel: {
        enabled: false,
        ...rollupConfig.docModel
      },
      dtsRollup: {
        enabled: true,
        publicTrimmedFilePath: resolve(outDir, fileName)
      },
      tsdocMetadata: {
        enabled: false,
        ...rollupConfig.tsdocMetadata
      },
      messages: {
        compilerMessageReporting: {
          default: {
            logLevel: "none"
          }
        },
        extractorMessageReporting: {
          default: {
            logLevel: "none"
          }
        },
        ...rollupConfig.messages
      }
    },
    configObjectFullPath,
    packageJsonFullPath: tryGetPkgPath(configObjectFullPath)
  });
  return apiExtractor.Extractor.invoke(extractorConfig, {
    localBuild: false,
    showVerboseMessages: false,
    showDiagnostics: false,
    typescriptCompilerFolder: libFolder,
    ...rollupOptions
  });
}

const jsonRE = /\.json$/;
function JsonResolver() {
  return {
    name: "json",
    supports(id) {
      return jsonRE.test(id);
    },
    transform({ id, root, program }) {
      const sourceFile = program.getSourceFile(id);
      if (!sourceFile) return [];
      return [
        {
          path: node_path.relative(root, `${id}.d.ts`),
          content: `declare const _default: ${sourceFile.text};

export default _default;
`
        }
      ];
    }
  };
}

const svelteRE = /\.svelte$/;
let lowerVersion;
function querySvelteVersion() {
  if (typeof lowerVersion === "boolean") return;
  try {
    const version = tryGetPackageInfo("svelte")?.version;
    lowerVersion = version ? compareVersions.compare(version, "4.0.0", "<") : false;
  } catch (e) {
    lowerVersion = false;
  }
}
function SvelteResolver() {
  return {
    name: "svelte",
    supports(id) {
      return svelteRE.test(id);
    },
    transform({ id, root }) {
      querySvelteVersion();
      return [
        {
          path: node_path.relative(root, `${id}.d.ts`),
          content: `export { ${lowerVersion ? "SvelteComponentTyped" : "SvelteComponent"} as default } from 'svelte';
`
        }
      ];
    }
  };
}

const vueRE = /\.vue$/;
function VueResolver() {
  return {
    name: "vue",
    supports(id) {
      return vueRE.test(id);
    },
    transform({ id, code, program }) {
      const sourceFile = program.getSourceFile(id) || program.getSourceFile(id + ".ts") || program.getSourceFile(id + ".js") || program.getSourceFile(id + ".tsx") || program.getSourceFile(id + ".jsx");
      if (!sourceFile) return [];
      const outputs = [];
      const { emitSkipped, diagnostics } = program.emit(
        sourceFile,
        (path, content) => {
          outputs.push({ path, content });
        },
        undefined,
        true
      );
      if (!program.getCompilerOptions().declarationMap) {
        return {
          outputs,
          emitSkipped,
          diagnostics
        };
      }
      const [beforeScript] = code.split(/\s*<script.*>/);
      const beforeLines = beforeScript.split("\n").length;
      for (const output of outputs) {
        if (output.path.endsWith(".map")) {
          try {
            const sourceMap = JSON.parse(output.content);
            sourceMap.sources = sourceMap.sources.map(
              (source) => source.replace(/\.vue\.ts$/, ".vue")
            );
            if (beforeScript && beforeScript !== code && beforeLines) {
              sourceMap.mappings = `${base64VLQEncode([0, 0, beforeLines, 0])};${sourceMap.mappings}`;
            }
            output.content = JSON.stringify(sourceMap);
          } catch (e) {
          }
        }
      }
      return {
        outputs,
        emitSkipped,
        diagnostics
      };
    }
  };
}

function parseResolvers(resolvers) {
  const nameMap = /* @__PURE__ */ new Map();
  for (const resolver of resolvers) {
    resolver.name && nameMap.set(resolver.name, resolver);
  }
  return Array.from(nameMap.values());
}

const globSuffixRE = /^((?:.*\.[^.]+)|(?:\*+))$/;
function normalizeGlob(path) {
  if (/[\\/]$/.test(path)) {
    return path + "**";
  } else if (!globSuffixRE.test(path.split(/[\\/]/).pop())) {
    return path + "/**";
  }
  return path;
}
function walkSourceFile(sourceFile, callback) {
  function walkNode(node, parent, callback2) {
    if (callback2(node, parent) !== false) {
      node.forEachChild((child) => walkNode(child, node, callback2));
    }
  }
  sourceFile.forEachChild((child) => walkNode(child, sourceFile, callback));
}
function isAliasMatch(alias, importer) {
  if (isRegExp(alias.find)) return alias.find.test(importer);
  if (importer.length < alias.find.length) return false;
  if (importer === alias.find) return true;
  return importer.indexOf(alias.find) === 0 && (alias.find.endsWith("/") || importer.substring(alias.find.length)[0] === "/");
}
function transformAlias(importer, dir, aliases, aliasesExclude) {
  if (aliases?.length && !aliasesExclude.some((e) => isRegExp(e) ? e.test(importer) : String(e) === importer)) {
    const matchedAlias = aliases.find((alias) => isAliasMatch(alias, importer));
    if (matchedAlias) {
      const replacement = node_path.isAbsolute(matchedAlias.replacement) ? normalizePath(node_path.relative(dir, matchedAlias.replacement)) : normalizePath(matchedAlias.replacement);
      const endsWithSlash = typeof matchedAlias.find === "string" ? matchedAlias.find.endsWith("/") : importer.match(matchedAlias.find)[0].endsWith("/");
      const truthPath = importer.replace(
        matchedAlias.find,
        replacement + (endsWithSlash ? "/" : "")
      );
      const absolutePath = node_path.resolve(dir, truthPath);
      const normalizedPath = normalizePath(node_path.relative(dir, absolutePath));
      const resultPath = normalizedPath.startsWith(".") ? normalizedPath : `./${normalizedPath}`;
      if (!isAliasGlobal(matchedAlias)) return resultPath;
      if (importResolves(absolutePath)) return resultPath;
    }
  }
  return importer;
}
const vlsRE = /^_?__VLS_/;
function isVLSNode(node) {
  if (ts__default.isVariableStatement(node)) {
    return node.declarationList.declarations.some(
      (d) => ts__default.isIdentifier(d.name) && vlsRE.test(`${d.name.escapedText}`)
    );
  }
  if (ts__default.isTypeAliasDeclaration(node)) {
    return vlsRE.test(`${node.name.escapedText}`);
  }
  if (ts__default.isFunctionDeclaration(node)) {
    return !!node.name && vlsRE.test(`${node.name.escapedText}`);
  }
  return false;
}
function transformCode(options) {
  const s = new MagicString__default(options.content);
  const ast = ts__default.createSourceFile("a.ts", options.content, ts__default.ScriptTarget.Latest);
  const dir = node_path.dirname(options.filePath);
  const importMap = /* @__PURE__ */ new Map();
  const usedDefault = /* @__PURE__ */ new Map();
  const declareModules = [];
  const toLibName = (origin) => {
    const name = transformAlias(origin, dir, options.aliases, options.aliasesExclude);
    return options.cleanVueFileName ? name.replace(/\.vue$/, "") : name;
  };
  let indexCount = 0;
  let importCount = 0;
  walkSourceFile(ast, (node, parent) => {
    if (ts__default.isImportDeclaration(node)) {
      if (!node.importClause) {
        options.clearPureImport && s.remove(node.pos, node.end);
        ++importCount;
      } else if (ts__default.isStringLiteral(node.moduleSpecifier) && (node.importClause.name || node.importClause.namedBindings && ts__default.isNamedImports(node.importClause.namedBindings))) {
        const libName = toLibName(node.moduleSpecifier.text);
        const importSet = importMap.get(libName) ?? importMap.set(libName, /* @__PURE__ */ new Set()).get(libName);
        if (node.importClause.name && !usedDefault.has(libName)) {
          const usedType = node.importClause.name.escapedText;
          usedDefault.set(libName, usedType);
          importSet.add(`default as ${usedType}`);
        }
        if (node.importClause.namedBindings && ts__default.isNamedImports(node.importClause.namedBindings)) {
          node.importClause.namedBindings.elements.forEach((element) => {
            if (element.propertyName) {
              importSet.add(`${element.propertyName.getText(ast)} as ${element.name.escapedText}`);
            } else {
              importSet.add(element.name.escapedText);
            }
          });
        }
        s.remove(node.pos, node.end);
        ++importCount;
      }
      return false;
    }
    if (ts__default.isImportTypeNode(node) && node.qualifier && ts__default.isLiteralTypeNode(node.argument) && ts__default.isIdentifier(node.qualifier) && ts__default.isStringLiteral(node.argument.literal)) {
      const libName = toLibName(node.argument.literal.text);
      if (!options.staticImport) {
        s.update(node.argument.literal.pos, node.argument.literal.end, `'${libName}'`);
        return !!node.typeArguments;
      }
      const importSet = importMap.get(libName) ?? importMap.set(libName, /* @__PURE__ */ new Set()).get(libName);
      let usedType = node.qualifier.escapedText;
      if (usedType === "default") {
        usedType = usedDefault.get(libName) ?? usedDefault.set(libName, `__DTS_DEFAULT_${indexCount++}__`).get(libName);
        importSet.add(`default as ${usedType}`);
        s.update(node.qualifier.pos, node.qualifier.end, usedType);
      } else {
        importSet.add(usedType);
      }
      if (ts__default.isImportTypeNode(parent) && parent.typeArguments && parent.typeArguments[0] === node) {
        s.remove(node.pos, node.argument.end + 2);
      } else {
        s.update(node.pos, node.argument.end + 2, " ");
      }
      return !!node.typeArguments;
    }
    if (ts__default.isCallExpression(node) && node.expression.kind === ts__default.SyntaxKind.ImportKeyword && ts__default.isStringLiteral(node.arguments[0])) {
      s.update(
        node.arguments[0].pos,
        node.arguments[0].end,
        `'${toLibName(node.arguments[0].text)}'`
      );
      return false;
    }
    if (ts__default.isExportDeclaration(node) && node.moduleSpecifier && ts__default.isStringLiteral(node.moduleSpecifier)) {
      s.update(
        node.moduleSpecifier.pos,
        node.moduleSpecifier.end,
        ` '${toLibName(node.moduleSpecifier.text)}'`
      );
      return false;
    }
    if (ts__default.isModuleDeclaration(node) && node.body && ts__default.isModuleBlock(node.body)) {
      if (ts__default.isIdentifier(node.name) && node.name.escapedText === "global" && node.body.statements.some(isVLSNode)) {
        s.remove(node.pos, node.end);
      } else if (ts__default.isStringLiteral(node.name)) {
        const libName = toLibName(node.name.text);
        if (libName !== node.name.text) {
          s.update(node.name.pos, node.name.end, ` '${libName}'`);
        }
        if (!libName.startsWith(".") && node.modifiers?.[0] && node.modifiers[0].kind === ts__default.SyntaxKind.DeclareKeyword && !node.body.statements.some(
          (s2) => ts__default.isExportAssignment(s2) || ts__default.isExportDeclaration(s2) || ts__default.isImportDeclaration(s2)
        )) {
          declareModules.push(s.slice(node.pos, node.end + 1));
        }
      }
      return false;
    }
  });
  let prependImports = "";
  importMap.forEach((importSet, libName) => {
    prependImports += `import { ${Array.from(importSet).join(", ")} } from '${libName}';
`;
  });
  s.trimStart("\n").prepend(prependImports);
  return {
    content: s.toString(),
    declareModules,
    diffLineCount: importMap.size && importCount < importMap.size ? importMap.size - importCount : null
  };
}
function hasNormalExport(content) {
  const ast = ts__default.createSourceFile("a.ts", content, ts__default.ScriptTarget.Latest);
  let has = false;
  walkSourceFile(ast, (node) => {
    if (ts__default.isExportDeclaration(node)) {
      if (node.exportClause && ts__default.isNamedExports(node.exportClause)) {
        for (const element of node.exportClause.elements) {
          if (element.name.getText(ast) !== "default") {
            has = true;
            break;
          }
        }
      } else {
        has = true;
      }
    } else if ("modifiers" in node && Array.isArray(node.modifiers) && node.modifiers.length > 1) {
      for (let i = 0, len = node.modifiers.length; i < len; ++i) {
        if (node.modifiers[i].kind === ts__default.SyntaxKind.ExportKeyword && node.modifiers[i + 1]?.kind !== ts__default.SyntaxKind.DefaultKeyword) {
          has = true;
          break;
        }
      }
    }
    return false;
  });
  return has;
}
function hasExportDefault(content) {
  const ast = ts__default.createSourceFile("a.ts", content, ts__default.ScriptTarget.Latest);
  let has = false;
  walkSourceFile(ast, (node) => {
    if (ts__default.isExportAssignment(node)) {
      has = true;
    } else if (ts__default.isExportDeclaration(node) && node.exportClause && ts__default.isNamedExports(node.exportClause)) {
      for (const element of node.exportClause.elements) {
        if (element.name.getText(ast) === "default") {
          has = true;
          break;
        }
      }
    } else if ("modifiers" in node && Array.isArray(node.modifiers) && node.modifiers.length > 1) {
      for (let i = 0, len = node.modifiers.length; i < len; ++i) {
        if (node.modifiers[i].kind === ts__default.SyntaxKind.ExportKeyword && node.modifiers[i + 1]?.kind === ts__default.SyntaxKind.DefaultKeyword) {
          has = true;
          break;
        }
      }
    }
    return false;
  });
  return has;
}

const jsRE = /\.(m|c)?jsx?$/;
const tsRE = /\.(m|c)?tsx?$/;
const dtsRE = /\.d\.(m|c)?tsx?$/;
const tjsRE = /\.(m|c)?(t|j)sx?$/;
const mtjsRE = /\.m(t|j)sx?$/;
const ctjsRE = /\.c(t|j)sx?$/;
const fullRelativeRE = /^\.\.?\//;
const defaultIndex = "index.d.ts";
const pluginName = "vite:dts";
const logPrefix = kolorist.cyan(`[${pluginName}]`);
const bundleDebug = debug__default("vite-plugin-dts:bundle");
const fixedCompilerOptions = {
  noEmit: false,
  declaration: true,
  emitDeclarationOnly: true,
  checkJs: false,
  skipLibCheck: true,
  preserveSymlinks: false,
  noEmitOnError: undefined,
  target: ts__default.ScriptTarget.ESNext
};
const noop = () => {
};
const extPrefix = (file) => mtjsRE.test(file) ? "m" : ctjsRE.test(file) ? "c" : "";
const tsToDts = (path) => `${path.replace(tsRE, "")}.d.ts`;
function dtsPlugin(options = {}) {
  const {
    tsconfigPath,
    logLevel,
    staticImport = false,
    clearPureImport = true,
    cleanVueFileName = false,
    insertTypesEntry = false,
    rollupTypes = false,
    pathsToAliases = true,
    aliasesExclude = [],
    rollupOptions = {},
    copyDtsFiles = false,
    declarationOnly = false,
    strictOutput = true,
    afterDiagnostic = noop,
    beforeWriteFile = noop,
    afterRollup = noop,
    afterBuild = noop
  } = options;
  let root = ensureAbsolute(options.root ?? "", process.cwd());
  let publicRoot = "";
  let entryRoot = options.entryRoot ?? "";
  let configPath;
  let compilerOptions;
  let rawCompilerOptions;
  let outDirs;
  let entries;
  let include;
  let exclude;
  let aliases;
  let libName;
  let indexName;
  let logger;
  let host;
  let program;
  let filter;
  let rootNames = [];
  let rebuildProgram;
  let bundled = false;
  let timeRecord = 0;
  const resolvers = parseResolvers([
    JsonResolver(),
    VueResolver(),
    SvelteResolver(),
    ...options.resolvers || []
  ]);
  const rootFiles = /* @__PURE__ */ new Set();
  const outputFiles = /* @__PURE__ */ new Map();
  const transformedFiles = /* @__PURE__ */ new Set();
  const diagnostics = [];
  const setOutputFile = (path, content) => {
    outputFiles.set(path, content);
  };
  const rollupConfig = { ...options.rollupConfig || {} };
  rollupConfig.bundledPackages = rollupConfig.bundledPackages || options.bundledPackages || [];
  const cleanPath = (path, emittedFiles) => {
    const newPath = path.replace(".vue.d.ts", ".d.ts");
    return !emittedFiles.has(newPath) && cleanVueFileName ? newPath : path;
  };
  return {
    name: pluginName,
    apply: "build",
    enforce: "pre",
    config(config) {
      const aliasOptions = config?.resolve?.alias ?? [];
      if (isNativeObj(aliasOptions)) {
        aliases = Object.entries(aliasOptions).map(([key, value]) => {
          return { find: key, replacement: value };
        });
      } else {
        aliases = ensureArray(aliasOptions).map((alias) => ({ ...alias }));
      }
      if (aliasesExclude.length > 0) {
        aliases = aliases.filter(
          ({ find }) => !aliasesExclude.some(
            (aliasExclude) => aliasExclude && (isRegExp(find) ? find.toString() === aliasExclude.toString() : isRegExp(aliasExclude) ? find.match(aliasExclude)?.[0] : find === aliasExclude)
          )
        );
      }
      for (const alias of aliases) {
        alias.replacement = resolve(alias.replacement);
      }
    },
    async configResolved(config) {
      logger = logLevel ? (await import('vite')).createLogger(logLevel, { allowClearScreen: config.clearScreen }) : config.logger;
      root = ensureAbsolute(options.root ?? "", config.root);
      if (config.build.lib) {
        const input = typeof config.build.lib.entry === "string" ? [config.build.lib.entry] : config.build.lib.entry;
        if (Array.isArray(input)) {
          entries = input.reduce(
            (prev, current) => {
              prev[node_path.basename(current)] = current;
              return prev;
            },
            {}
          );
        } else {
          entries = { ...input };
        }
        const filename = config.build.lib.fileName ?? defaultIndex;
        const entry = typeof config.build.lib.entry === "string" ? config.build.lib.entry : Object.keys(config.build.lib.entry)[0];
        libName = config.build.lib.name || "_default";
        indexName = typeof filename === "string" ? filename : filename("es", entry);
        if (!dtsRE.test(indexName)) {
          indexName = `${indexName.replace(tjsRE, "")}.d.${extPrefix(indexName)}ts`;
        }
      } else {
        logger.warn(
          `
${logPrefix} ${kolorist.yellow(
            "You are building a library that may not need to generate declaration files."
          )}
`
        );
        libName = "_default";
        indexName = defaultIndex;
      }
      if (!options.outDir) {
        outDirs = [ensureAbsolute(config.build.outDir, root)];
      }
      bundleDebug("parse vite config");
    },
    options(options2) {
      if (entries) return;
      const input = typeof options2.input === "string" ? [options2.input] : options2.input;
      if (Array.isArray(input)) {
        entries = input.reduce(
          (prev, current) => {
            prev[node_path.basename(current)] = current;
            return prev;
          },
          {}
        );
      } else {
        entries = { ...input };
      }
      logger = logger || console;
      aliases = aliases || [];
      libName = "_default";
      indexName = defaultIndex;
      bundleDebug("parse options");
    },
    async buildStart() {
      if (program) return;
      bundleDebug("begin buildStart");
      timeRecord = 0;
      const startTime = Date.now();
      configPath = tsconfigPath ? ensureAbsolute(tsconfigPath, root) : ts__default.findConfigFile(root, ts__default.sys.fileExists);
      const content = configPath ? languageCore.createParsedCommandLine(ts__default, ts__default.sys, configPath) : undefined;
      compilerOptions = {
        ...content?.options || {},
        ...options.compilerOptions || {},
        ...fixedCompilerOptions,
        outDir: ".",
        declarationDir: "."
      };
      rawCompilerOptions = content?.raw.compilerOptions || {};
      if (content?.fileNames.find((name) => name.endsWith(".vue"))) {
        setModuleResolution(compilerOptions);
      }
      if (!outDirs) {
        outDirs = options.outDir ? ensureArray(options.outDir).map((d) => ensureAbsolute(d, root)) : [
          ensureAbsolute(
            content?.raw.compilerOptions?.outDir ? resolveConfigDir(content.raw.compilerOptions.outDir, root) : "dist",
            root
          )
        ];
      }
      const {
        // Here we are using the default value to set the `baseUrl` to the current directory if no value exists. This is
        // the same behavior as the TS Compiler. See TS source:
        // https://github.com/microsoft/TypeScript/blob/3386e943215613c40f68ba0b108cda1ddb7faee1/src/compiler/utilities.ts#L6493-L6501
        baseUrl = compilerOptions.paths ? process.cwd() : undefined,
        paths
      } = compilerOptions;
      if (pathsToAliases && baseUrl && paths) {
        aliases.push(
          ...parseTsAliases(
            ensureAbsolute(
              resolveConfigDir(baseUrl, root),
              configPath ? node_path.dirname(configPath) : root
            ),
            paths
          )
        );
      }
      const computeGlobs = (rootGlobs, tsGlobs, defaultGlob) => {
        if (rootGlobs?.length) {
          return ensureArray(rootGlobs).map(
            (glob) => normalizeGlob(ensureAbsolute(resolveConfigDir(glob, root), root))
          );
        }
        return ensureArray(tsGlobs?.length ? tsGlobs : defaultGlob).map(
          (glob) => normalizeGlob(
            ensureAbsolute(resolveConfigDir(glob, root), configPath ? node_path.dirname(configPath) : root)
          )
        );
      };
      include = computeGlobs(
        options.include,
        [...ensureArray(content?.raw.include ?? []), ...ensureArray(content?.raw.files ?? [])],
        "**/*"
      );
      exclude = computeGlobs(options.exclude, content?.raw.exclude, "node_modules/**");
      filter = pluginutils.createFilter(include, exclude);
      rootNames = [
        ...new Set(
          Object.values(entries).map((entry) => ensureAbsolute(entry, root)).concat(content?.fileNames.filter(filter) || []).map(normalizePath)
        )
      ];
      host = ts__default.createCompilerHost(compilerOptions);
      program = (rebuildProgram = () => createProgram({
        host,
        rootNames,
        options: compilerOptions,
        projectReferences: content?.projectReferences
      }))();
      libName = toCapitalCase(libName || "_default");
      indexName = indexName || defaultIndex;
      const maybeEmitted = (sourceFile) => {
        return !(compilerOptions.noEmitForJsFiles && jsRE.test(sourceFile.fileName)) && !sourceFile.isDeclarationFile && !program.isSourceFileFromExternalLibrary(sourceFile);
      };
      publicRoot = compilerOptions.rootDir ? ensureAbsolute(resolveConfigDir(compilerOptions.rootDir, root), root) : compilerOptions.composite && compilerOptions.configFilePath ? node_path.dirname(compilerOptions.configFilePath) : queryPublicPath(
        program.getSourceFiles().filter(maybeEmitted).map((sourceFile) => sourceFile.fileName)
      );
      publicRoot = normalizePath(publicRoot);
      entryRoot = entryRoot || publicRoot;
      entryRoot = ensureAbsolute(entryRoot, root);
      diagnostics.push(
        ...program.getDeclarationDiagnostics(),
        ...program.getSemanticDiagnostics(),
        ...program.getSyntacticDiagnostics()
      );
      for (const file of rootNames) {
        this.addWatchFile(file);
        rootFiles.add(file);
      }
      bundleDebug("create ts program");
      timeRecord += Date.now() - startTime;
    },
    async transform(code, id) {
      let resolver;
      id = normalizePath(id).split("?")[0];
      if (!host || !program || !filter(id) || !(resolver = resolvers.find((r) => r.supports(id))) && !tjsRE.test(id) || transformedFiles.has(id)) {
        return;
      }
      const startTime = Date.now();
      const outDir = outDirs[0];
      rootFiles.delete(id);
      transformedFiles.add(id);
      if (resolver) {
        const result = await resolver.transform({
          id,
          code,
          root: publicRoot,
          outDir,
          host,
          program
        });
        let output;
        if (Array.isArray(result)) {
          output = result;
        } else {
          output = result.outputs;
          if (result.emitSkipped && result.diagnostics?.length) {
            diagnostics.push(...result.diagnostics);
          }
        }
        for (const { path, content } of output) {
          setOutputFile(
            resolve(publicRoot, node_path.relative(outDir, ensureAbsolute(path, outDir))),
            content
          );
        }
      } else {
        const sourceFile = program.getSourceFile(id);
        if (sourceFile) {
          const result = program.emit(
            sourceFile,
            (name, text) => {
              setOutputFile(
                resolve(publicRoot, node_path.relative(outDir, ensureAbsolute(name, outDir))),
                text
              );
            },
            undefined,
            true
          );
          if (result.emitSkipped && result.diagnostics.length) {
            diagnostics.push(...result.diagnostics);
          }
        }
      }
      const dtsId = id.replace(tjsRE, "") + ".d.ts";
      const dtsSourceFile = program.getSourceFile(dtsId);
      dtsSourceFile && filter(dtsSourceFile.fileName) && setOutputFile(normalizePath(dtsSourceFile.fileName), dtsSourceFile.getFullText());
      timeRecord += Date.now() - startTime;
    },
    watchChange(id) {
      id = normalizePath(id);
      if (!host || !program || !filter(id) || !resolvers.find((r) => r.supports(id)) && !tjsRE.test(id)) {
        return;
      }
      id = id.split("?")[0];
      const sourceFile = host.getSourceFile(id, ts__default.ScriptTarget.ESNext);
      if (sourceFile) {
        for (const file of rootNames) {
          rootFiles.add(file);
        }
        rootFiles.add(normalizePath(sourceFile.fileName));
        bundled = false;
        timeRecord = 0;
        program = rebuildProgram();
      }
    },
    async writeBundle() {
      transformedFiles.clear();
      if (!host || !program || bundled) return;
      bundled = true;
      bundleDebug("begin writeBundle");
      logger.info(kolorist.green(`
${logPrefix} Start generate declaration files...`));
      const startTime = Date.now();
      if (diagnostics?.length) {
        logger.error(ts__default.formatDiagnosticsWithColorAndContext(diagnostics, host));
      }
      if (typeof afterDiagnostic === "function") {
        await unwrapPromise(afterDiagnostic(diagnostics));
      }
      const outDir = outDirs[0];
      const emittedFiles = /* @__PURE__ */ new Map();
      const declareModules = [];
      const writeOutput = async (path, content, outDir2, record = true) => {
        if (typeof beforeWriteFile === "function") {
          const result = await unwrapPromise(beforeWriteFile(path, content));
          if (result === false) return;
          if (result) {
            path = result.filePath || path;
            content = result.content ?? content;
          }
        }
        path = normalizePath(path);
        const dir = normalizePath(node_path.dirname(path));
        if (strictOutput && !dir.startsWith(normalizePath(outDir2))) {
          logger.warn(`${logPrefix} ${kolorist.yellow("Outside emitted:")} ${path}`);
          return;
        }
        if (!node_fs.existsSync(dir)) {
          await promises.mkdir(dir, { recursive: true });
        }
        await promises.writeFile(path, content, "utf-8");
        record && emittedFiles.set(path, content);
      };
      const sourceFiles = program.getSourceFiles();
      for (const sourceFile of sourceFiles) {
        if (!filter(sourceFile.fileName)) continue;
        if (copyDtsFiles && dtsRE.test(sourceFile.fileName)) {
          setOutputFile(normalizePath(sourceFile.fileName), sourceFile.getFullText());
        }
        if (rootFiles.has(sourceFile.fileName)) {
          program.emit(
            sourceFile,
            (name, text) => {
              setOutputFile(
                resolve(publicRoot, node_path.relative(outDir, ensureAbsolute(name, outDir))),
                text
              );
            },
            undefined,
            true
          );
          rootFiles.delete(sourceFile.fileName);
        }
      }
      bundleDebug("emit output patch");
      const currentDir = host.getCurrentDirectory();
      const declarationFiles = /* @__PURE__ */ new Map();
      const mapFiles = /* @__PURE__ */ new Map();
      const prependMappings = /* @__PURE__ */ new Map();
      for (const [filePath, content] of outputFiles.entries()) {
        if (filePath.endsWith(".map")) {
          mapFiles.set(filePath, content);
        } else {
          declarationFiles.set(filePath, content);
        }
      }
      await runParallel(
        node_os.cpus().length,
        Array.from(declarationFiles.entries()),
        async ([filePath, content]) => {
          const newFilePath = resolve(
            outDir,
            node_path.relative(
              entryRoot,
              cleanVueFileName ? filePath.replace(".vue.d.ts", ".d.ts") : filePath
            )
          );
          if (content) {
            const result = transformCode({
              filePath,
              content,
              aliases,
              aliasesExclude,
              staticImport,
              clearPureImport,
              cleanVueFileName
            });
            content = result.content;
            declareModules.push(...result.declareModules);
            if (result.diffLineCount) {
              prependMappings.set(`${newFilePath}.map`, ";".repeat(result.diffLineCount));
            }
          }
          await writeOutput(newFilePath, content, outDir);
        }
      );
      await runParallel(
        node_os.cpus().length,
        Array.from(mapFiles.entries()),
        async ([filePath, content]) => {
          const baseDir = node_path.dirname(filePath);
          filePath = resolve(
            outDir,
            node_path.relative(
              entryRoot,
              cleanVueFileName ? filePath.replace(".vue.d.ts", ".d.ts") : filePath
            )
          );
          try {
            const sourceMap = JSON.parse(content);
            sourceMap.sources = sourceMap.sources.map((source) => {
              return normalizePath(
                node_path.relative(
                  node_path.dirname(filePath),
                  resolve(currentDir, node_path.relative(publicRoot, baseDir), source)
                )
              );
            });
            if (prependMappings.has(filePath)) {
              sourceMap.mappings = `${prependMappings.get(filePath)}${sourceMap.mappings}`;
            }
            content = JSON.stringify(sourceMap);
          } catch (e) {
            logger.warn(`${logPrefix} ${kolorist.yellow("Processing source map fail:")} ${filePath}`);
          }
          await writeOutput(filePath, content, outDir);
        }
      );
      bundleDebug("write output");
      if (insertTypesEntry || rollupTypes) {
        const pkgPath = tryGetPkgPath(root);
        let pkg;
        try {
          pkg = pkgPath && node_fs.existsSync(pkgPath) ? JSON.parse(await promises.readFile(pkgPath, "utf-8")) : {};
        } catch (e) {
        }
        const entryNames = Object.keys(entries);
        const types = findTypesPath(pkg.publishConfig, pkg);
        const multiple = entryNames.length > 1;
        let typesPath = cleanPath(
          types ? resolve(root, types) : resolve(outDir, indexName),
          emittedFiles
        );
        if (!multiple && !dtsRE.test(typesPath)) {
          logger.warn(
            `
${logPrefix} ${kolorist.yellow(
              "The resolved path of type entry is not ending with '.d.ts'."
            )}
`
          );
          typesPath = `${typesPath.replace(tjsRE, "")}.d.${extPrefix(typesPath)}ts`;
        }
        for (const name of entryNames) {
          const entryDtsPath = multiple ? cleanPath(resolve(outDir, tsToDts(name)), emittedFiles) : typesPath;
          if (node_fs.existsSync(entryDtsPath)) continue;
          const sourceEntry = normalizePath(
            cleanPath(resolve(outDir, node_path.relative(entryRoot, tsToDts(entries[name]))), emittedFiles)
          );
          let fromPath = normalizePath(node_path.relative(node_path.dirname(entryDtsPath), sourceEntry));
          fromPath = fromPath.replace(dtsRE, "");
          fromPath = fullRelativeRE.test(fromPath) ? fromPath : `./${fromPath}`;
          let content = "export {}\n";
          if (emittedFiles.has(sourceEntry)) {
            if (hasNormalExport(emittedFiles.get(sourceEntry))) {
              content = `export * from '${fromPath}'
${content}`;
            }
            if (hasExportDefault(emittedFiles.get(sourceEntry))) {
              content += `import ${libName} from '${fromPath}'
export default ${libName}
${content}`;
            }
          }
          await writeOutput(cleanPath(entryDtsPath, emittedFiles), content, outDir);
        }
        bundleDebug("insert index");
        if (rollupTypes) {
          logger.info(kolorist.green(`${logPrefix} Start rollup declaration files...`));
          const rollupFiles = /* @__PURE__ */ new Set();
          const compilerOptions2 = configPath ? getTsConfig(configPath, host.readFile).compilerOptions : rawCompilerOptions;
          const rollup = async (path) => {
            const result = rollupDeclarationFiles({
              root: publicRoot,
              configPath,
              compilerOptions: compilerOptions2,
              outDir,
              entryPath: path,
              fileName: node_path.basename(path),
              libFolder: getTsLibFolder(),
              rollupConfig,
              rollupOptions
            });
            emittedFiles.delete(path);
            rollupFiles.add(path);
            if (typeof afterRollup === "function") {
              await unwrapPromise(afterRollup(result));
            }
          };
          if (multiple) {
            await runParallel(node_os.cpus().length, entryNames, async (name) => {
              await rollup(cleanPath(resolve(outDir, tsToDts(name)), emittedFiles));
            });
          } else {
            await rollup(typesPath);
          }
          await runParallel(node_os.cpus().length, Array.from(emittedFiles.keys()), (f) => promises.unlink(f));
          removeDirIfEmpty(outDir);
          emittedFiles.clear();
          const declared = declareModules.join("\n");
          await runParallel(node_os.cpus().length, [...rollupFiles], async (filePath) => {
            await writeOutput(
              filePath,
              await promises.readFile(filePath, "utf-8") + (declared ? `
${declared}` : ""),
              node_path.dirname(filePath)
            );
          });
          bundleDebug("rollup output");
        }
      }
      if (outDirs.length > 1) {
        const extraOutDirs = outDirs.slice(1);
        await runParallel(node_os.cpus().length, Array.from(emittedFiles), async ([wroteFile, content]) => {
          const relativePath = node_path.relative(outDir, wroteFile);
          await Promise.all(
            extraOutDirs.map(async (targetOutDir) => {
              const path = resolve(targetOutDir, relativePath);
              if (wroteFile.endsWith(".map")) {
                if (!editSourceMapDir(content, outDir, targetOutDir)) {
                  logger.warn(`${logPrefix} ${kolorist.yellow("Processing source map fail:")} ${path}`);
                }
              }
              await writeOutput(path, content, targetOutDir, false);
            })
          );
        });
      }
      diagnostics.length = 0;
      if (typeof afterBuild === "function") {
        await unwrapPromise(afterBuild(emittedFiles));
      }
      bundleDebug("finish");
      logger.info(
        kolorist.green(`${logPrefix} Declaration files built in ${timeRecord + Date.now() - startTime}ms.
`)
      );
    },
    generateBundle(_, bundle) {
      if (declarationOnly) {
        for (const id of Object.keys(bundle)) {
          delete bundle[id];
        }
      }
    }
  };
}

exports.default = dtsPlugin;
exports.editSourceMapDir = editSourceMapDir;
