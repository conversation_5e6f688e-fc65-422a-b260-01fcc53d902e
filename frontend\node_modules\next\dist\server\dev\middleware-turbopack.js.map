{"version": 3, "sources": ["../../../src/server/dev/middleware-turbopack.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport {\n  getOriginalCodeFrame,\n  ignoreListAnonymousStackFramesIfSandwiched,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from '../../next-devtools/server/shared'\nimport { middlewareResponse } from '../../next-devtools/server/middleware-response'\nimport path from 'path'\nimport { openFileInEditor } from '../../next-devtools/server/launch-editor'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  SourceMapConsumer,\n  type NullableMappedPosition,\n} from 'next/dist/compiled/source-map08'\nimport type { Project, TurbopackStackFrame } from '../../build/swc/types'\nimport {\n  type ModernSourceMapPayload,\n  devirtualizeReactServerURL,\n  findApplicableSourceMapPayload,\n} from '../lib/source-maps'\nimport { getSourceMapFromFile } from './get-source-map-from-file'\nimport { findSourceMap } from 'node:module'\nimport { fileURLToPath, pathToFileURL } from 'node:url'\nimport { inspect } from 'node:util'\n\nfunction shouldIgnorePath(modulePath: string): boolean {\n  return (\n    modulePath.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    modulePath.includes('next/dist') ||\n    modulePath.startsWith('node:')\n  )\n}\n\ntype IgnorableStackFrame = StackFrame & { ignored: boolean }\n\nconst currentSourcesByFile: Map<string, Promise<string | null>> = new Map()\nasync function batchedTraceSource(\n  project: Project,\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const file = frame.file\n    ? // TODO(veil): Why are the frames sent encoded?\n      decodeURIComponent(frame.file)\n    : undefined\n\n  if (!file) return\n\n  // For node internals they cannot traced the actual source code with project.traceSource,\n  // we need an early return to indicate it's ignored to avoid the unknown scheme error from `project.traceSource`.\n  if (file.startsWith('node:')) {\n    return {\n      frame: {\n        file,\n        lineNumber: frame.line ?? 0,\n        column: frame.column ?? 0,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: true,\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  const currentDirectoryFileUrl = pathToFileURL(process.cwd()).href\n\n  const sourceFrame = await project.traceSource(frame, currentDirectoryFileUrl)\n  if (!sourceFrame) {\n    return {\n      frame: {\n        file,\n        lineNumber: frame.line ?? 0,\n        column: frame.column ?? 0,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: shouldIgnorePath(file),\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  let source = null\n  const originalFile = sourceFrame.originalFile\n\n  // Don't look up source for node_modules or internals. These can often be large bundled files.\n  const ignored =\n    shouldIgnorePath(originalFile ?? sourceFrame.file) ||\n    // isInternal means resource starts with turbopack:///[turbopack]\n    !!sourceFrame.isInternal\n  if (originalFile && !ignored) {\n    let sourcePromise = currentSourcesByFile.get(originalFile)\n    if (!sourcePromise) {\n      sourcePromise = project.getSourceForAsset(originalFile)\n      currentSourcesByFile.set(originalFile, sourcePromise)\n      setTimeout(() => {\n        // Cache file reads for 100ms, as frames will often reference the same\n        // files and can be large.\n        currentSourcesByFile.delete(originalFile!)\n      }, 100)\n    }\n    source = await sourcePromise\n  }\n\n  // TODO: get ignoredList from turbopack source map\n  const ignorableFrame = {\n    file: sourceFrame.file,\n    lineNumber: sourceFrame.line ?? 0,\n    column: sourceFrame.column ?? 0,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      frame.methodName ?? '<unknown>',\n    ignored,\n    arguments: [],\n  }\n\n  return {\n    frame: ignorableFrame,\n    source,\n  }\n}\n\nfunction parseFile(fileParam: string | null): string | undefined {\n  if (!fileParam) {\n    return undefined\n  }\n\n  return devirtualizeReactServerURL(fileParam)\n}\n\nfunction createStackFrames(\n  body: OriginalStackFramesRequest\n): TurbopackStackFrame[] {\n  const { frames, isServer } = body\n\n  return frames\n    .map((frame): TurbopackStackFrame | undefined => {\n      const file = parseFile(frame.file)\n\n      if (!file) {\n        return undefined\n      }\n\n      return {\n        file,\n        methodName: frame.methodName ?? '<unknown>',\n        line: frame.lineNumber ?? 0,\n        column: frame.column ?? 0,\n        isServer,\n      } satisfies TurbopackStackFrame\n    })\n    .filter((f): f is TurbopackStackFrame => f !== undefined)\n}\n\nfunction createStackFrame(\n  searchParams: URLSearchParams\n): TurbopackStackFrame | undefined {\n  const file = parseFile(searchParams.get('file'))\n\n  if (!file) {\n    return undefined\n  }\n\n  return {\n    file,\n    methodName: searchParams.get('methodName') ?? '<unknown>',\n    line: parseInt(searchParams.get('lineNumber') ?? '0', 10) || 0,\n    column: parseInt(searchParams.get('column') ?? '0', 10) || 0,\n    isServer: searchParams.get('isServer') === 'true',\n  } satisfies TurbopackStackFrame\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function nativeTraceSource(\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const sourceURL = frame.file\n  let sourceMapPayload: ModernSourceMapPayload | undefined\n  try {\n    sourceMapPayload = findSourceMap(sourceURL)?.payload\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (sourceMapPayload !== undefined) {\n    let consumer: SourceMapConsumer\n    try {\n      consumer = await new SourceMapConsumer(sourceMapPayload)\n    } catch (cause) {\n      throw new Error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    }\n    let traced: {\n      originalPosition: NullableMappedPosition\n      sourceContent: string | null\n    } | null\n    try {\n      const originalPosition = consumer.originalPositionFor({\n        line: frame.line ?? 1,\n        // 0-based columns out requires 0-based columns in.\n        column: (frame.column ?? 1) - 1,\n      })\n\n      if (originalPosition.source === null) {\n        traced = null\n      } else {\n        const sourceContent: string | null =\n          consumer.sourceContentFor(\n            originalPosition.source,\n            /* returnNullOnMissing */ true\n          ) ?? null\n\n        traced = { originalPosition, sourceContent }\n      }\n    } finally {\n      consumer.destroy()\n    }\n\n    if (traced !== null) {\n      const { originalPosition, sourceContent } = traced\n      const applicableSourceMap = findApplicableSourceMapPayload(\n        frame.line ?? 0,\n        frame.column ?? 0,\n        sourceMapPayload\n      )\n\n      // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n      let ignored = false\n      if (applicableSourceMap === undefined) {\n        console.error(\n          'No applicable source map found in sections for frame',\n          frame\n        )\n      } else {\n        // TODO: O(n^2). Consider moving `ignoreList` into a Set\n        const sourceIndex = applicableSourceMap.sources.indexOf(\n          originalPosition.source!\n        )\n        ignored =\n          applicableSourceMap.ignoreList?.includes(sourceIndex) ??\n          // When sourcemap is not available, fallback to checking `frame.file`.\n          // e.g. In pages router, nextjs server code is not bundled into the page.\n          shouldIgnorePath(frame.file)\n      }\n\n      const originalStackFrame: IgnorableStackFrame = {\n        methodName:\n          // We ignore the sourcemapped name since it won't be the correct name.\n          // The callsite will point to the column of the variable name instead of the\n          // name of the enclosing function.\n          // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n          frame.methodName\n            ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n            ?.replace('__webpack_exports__.', '') || '<unknown>',\n        column: (originalPosition.column ?? 0) + 1,\n        file: originalPosition.source,\n        lineNumber: originalPosition.line ?? 0,\n        // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n        arguments: [],\n        ignored,\n      }\n\n      return {\n        frame: originalStackFrame,\n        source: sourceContent,\n      }\n    }\n  }\n\n  return undefined\n}\n\nasync function createOriginalStackFrame(\n  project: Project,\n  projectPath: string,\n  frame: TurbopackStackFrame\n): Promise<OriginalStackFrameResponse | null> {\n  const traced =\n    (await nativeTraceSource(frame)) ??\n    // TODO(veil): When would the bundler know more than native?\n    // If it's faster, try the bundler first and fall back to native later.\n    (await batchedTraceSource(project, frame))\n  if (!traced) {\n    return null\n  }\n\n  let normalizedStackFrameLocation = traced.frame.file\n  if (\n    normalizedStackFrameLocation !== null &&\n    normalizedStackFrameLocation.startsWith('file://')\n  ) {\n    normalizedStackFrameLocation = path.relative(\n      projectPath,\n      fileURLToPath(normalizedStackFrameLocation)\n    )\n  }\n\n  return {\n    originalStackFrame: {\n      arguments: traced.frame.arguments,\n      column: traced.frame.column,\n      file: normalizedStackFrameLocation,\n      ignored: traced.frame.ignored,\n      lineNumber: traced.frame.lineNumber,\n      methodName: traced.frame.methodName,\n    },\n    originalCodeFrame: getOriginalCodeFrame(traced.frame, traced.source),\n  }\n}\n\nexport function getOverlayMiddleware({\n  project,\n  projectPath,\n  isSrcDir,\n}: {\n  project: Project\n  projectPath: string\n  isSrcDir: boolean\n}) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      const request = JSON.parse(body) as OriginalStackFramesRequest\n      const result = await getOriginalStackFrames({\n        project,\n        projectPath,\n        frames: request.frames,\n        isServer: request.isServer,\n        isEdgeServer: request.isEdgeServer,\n        isAppDirectory: request.isAppDirectory,\n      })\n\n      ignoreListAnonymousStackFramesIfSandwiched(result)\n\n      return middlewareResponse.json(res, result)\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const isAppRelativePath = searchParams.get('isAppRelativePath') === '1'\n\n      let openEditorResult\n      if (isAppRelativePath) {\n        const relativeFilePath = searchParams.get('file') || ''\n        const absoluteFilePath = path.join(\n          projectPath,\n          'app',\n          isSrcDir ? 'src' : '',\n          relativeFilePath\n        )\n        openEditorResult = await openFileInEditor(absoluteFilePath, 1, 1)\n      } else {\n        const frame = createStackFrame(searchParams)\n        if (!frame) return middlewareResponse.badRequest(res)\n        openEditorResult = await openFileInEditor(\n          frame.file,\n          frame.line ?? 1,\n          frame.column ?? 1\n        )\n      }\n\n      if (openEditorResult.error) {\n        return middlewareResponse.internalServerError(res)\n      }\n      if (!openEditorResult.found) {\n        return middlewareResponse.notFound(res)\n      }\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(project: Project) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    let filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    // TODO(veil): Always try the native version first.\n    // Externals could also be files that aren't bundled via Webpack.\n    if (\n      filename.startsWith('webpack://') ||\n      filename.startsWith('webpack-internal:///')\n    ) {\n      const sourceMap = findSourceMap(filename)\n\n      if (sourceMap) {\n        return middlewareResponse.json(res, sourceMap.payload)\n      }\n\n      return middlewareResponse.noContent(res)\n    }\n\n    try {\n      // Turbopack chunk filenames might be URL-encoded.\n      filename = decodeURI(filename)\n    } catch {\n      return middlewareResponse.badRequest(res)\n    }\n\n    if (path.isAbsolute(filename)) {\n      filename = pathToFileURL(filename).href\n    }\n\n    try {\n      const sourceMapString = await project.getSourceMap(filename)\n\n      if (sourceMapString) {\n        return middlewareResponse.jsonString(res, sourceMapString)\n      }\n\n      if (filename.startsWith('file:')) {\n        const sourceMap = await getSourceMapFromFile(filename)\n\n        if (sourceMap) {\n          return middlewareResponse.json(res, sourceMap)\n        }\n      }\n    } catch (cause) {\n      return middlewareResponse.internalServerError(\n        res,\n        new Error(\n          `Failed to get source map for '${filename}'. This is a bug in Next.js`,\n          {\n            cause,\n          }\n        )\n      )\n    }\n\n    middlewareResponse.noContent(res)\n  }\n}\n\nexport async function getOriginalStackFrames({\n  project,\n  projectPath,\n  frames,\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n}: {\n  project: Project\n  projectPath: string\n  frames: StackFrame[]\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n}): Promise<OriginalStackFramesResponse> {\n  const stackFrames = createStackFrames({\n    frames,\n    isServer,\n    isEdgeServer,\n    isAppDirectory,\n  })\n\n  return Promise.all(\n    stackFrames.map(async (frame) => {\n      try {\n        const stackFrame = await createOriginalStackFrame(\n          project,\n          projectPath,\n          frame\n        )\n        if (stackFrame === null) {\n          return {\n            status: 'rejected',\n            reason: 'Failed to create original stack frame',\n          }\n        }\n        return { status: 'fulfilled', value: stackFrame }\n      } catch (error) {\n        return {\n          status: 'rejected',\n          reason: inspect(error, { colors: false }),\n        }\n      }\n    })\n  )\n}\n"], "names": ["getOriginalStackFrames", "getOverlayMiddleware", "getSourceMapMiddleware", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modulePath", "includes", "startsWith", "currentSourcesByFile", "Map", "batchedTraceSource", "project", "frame", "file", "decodeURIComponent", "undefined", "lineNumber", "line", "column", "methodName", "ignored", "arguments", "source", "currentDirectoryFileUrl", "pathToFileURL", "process", "cwd", "href", "sourceFrame", "traceSource", "originalFile", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "ignorableFrame", "parseFile", "fileParam", "devirtualizeReactServerURL", "createStackFrames", "body", "frames", "isServer", "map", "filter", "f", "createStackFrame", "searchParams", "parseInt", "nativeTraceSource", "sourceURL", "sourceMapPayload", "findSourceMap", "payload", "cause", "Error", "consumer", "SourceMapConsumer", "traced", "originalPosition", "originalPositionFor", "sourceContent", "sourceContentFor", "destroy", "applicableSourceMap", "findApplicableSourceMapPayload", "console", "error", "sourceIndex", "sources", "indexOf", "ignoreList", "originalStackFrame", "replace", "createOriginalStackFrame", "projectPath", "normalizedStackFrameLocation", "path", "relative", "fileURLToPath", "originalCodeFrame", "getOriginalCodeFrame", "isSrcDir", "req", "res", "next", "pathname", "URL", "url", "method", "middlewareResponse", "badRequest", "Promise", "resolve", "reject", "data", "on", "chunk", "request", "JSON", "parse", "result", "isEdgeServer", "isAppDirectory", "ignoreListAnonymousStackFramesIfSandwiched", "json", "isAppRelativePath", "openEditorResult", "relativeFilePath", "absoluteFilePath", "join", "openFileInEditor", "internalServerError", "found", "notFound", "noContent", "filename", "sourceMap", "decodeURI", "isAbsolute", "sourceMapString", "getSourceMap", "jsonString", "getSourceMapFromFile", "stackFrames", "all", "stackFrame", "status", "reason", "value", "inspect", "colors"], "mappings": ";;;;;;;;;;;;;;;;IA0dsBA,sBAAsB;eAAtBA;;IAzJNC,oBAAoB;eAApBA;;IA+EAC,sBAAsB;eAAtBA;;;wBAzYT;oCAC4B;6DAClB;8BACgB;6BAK1B;4BAMA;sCAC8B;4BACP;yBACe;0BACrB;;;;;;AAExB,SAASC,iBAAiBC,UAAkB;IAC1C,OACEA,WAAWC,QAAQ,CAAC,mBACpB,2EAA2E;IAC3ED,WAAWC,QAAQ,CAAC,gBACpBD,WAAWE,UAAU,CAAC;AAE1B;AAIA,MAAMC,uBAA4D,IAAIC;AACtE,eAAeC,mBACbC,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAEnBC,mBAAmBF,MAAMC,IAAI,IAC7BE;IAEJ,IAAI,CAACF,MAAM;IAEX,yFAAyF;IACzF,iHAAiH;IACjH,IAAIA,KAAKN,UAAU,CAAC,UAAU;QAC5B,OAAO;YACLK,OAAO;gBACLC;gBACAG,YAAYJ,MAAMK,IAAI,IAAI;gBAC1BC,QAAQN,MAAMM,MAAM,IAAI;gBACxBC,YAAYP,MAAMO,UAAU,IAAI;gBAChCC,SAAS;gBACTC,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,MAAMC,0BAA0BC,IAAAA,sBAAa,EAACC,QAAQC,GAAG,IAAIC,IAAI;IAEjE,MAAMC,cAAc,MAAMjB,QAAQkB,WAAW,CAACjB,OAAOW;IACrD,IAAI,CAACK,aAAa;QAChB,OAAO;YACLhB,OAAO;gBACLC;gBACAG,YAAYJ,MAAMK,IAAI,IAAI;gBAC1BC,QAAQN,MAAMM,MAAM,IAAI;gBACxBC,YAAYP,MAAMO,UAAU,IAAI;gBAChCC,SAAShB,iBAAiBS;gBAC1BQ,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,IAAIA,SAAS;IACb,MAAMQ,eAAeF,YAAYE,YAAY;IAE7C,8FAA8F;IAC9F,MAAMV,UACJhB,iBAAiB0B,gBAAgBF,YAAYf,IAAI,KACjD,iEAAiE;IACjE,CAAC,CAACe,YAAYG,UAAU;IAC1B,IAAID,gBAAgB,CAACV,SAAS;QAC5B,IAAIY,gBAAgBxB,qBAAqByB,GAAG,CAACH;QAC7C,IAAI,CAACE,eAAe;YAClBA,gBAAgBrB,QAAQuB,iBAAiB,CAACJ;YAC1CtB,qBAAqB2B,GAAG,CAACL,cAAcE;YACvCI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1B5B,qBAAqB6B,MAAM,CAACP;YAC9B,GAAG;QACL;QACAR,SAAS,MAAMU;IACjB;IAEA,kDAAkD;IAClD,MAAMM,iBAAiB;QACrBzB,MAAMe,YAAYf,IAAI;QACtBG,YAAYY,YAAYX,IAAI,IAAI;QAChCC,QAAQU,YAAYV,MAAM,IAAI;QAC9BC,YACE,sEAAsE;QACtE,4EAA4E;QAC5E,kCAAkC;QAClC,oGAAoG;QACpGP,MAAMO,UAAU,IAAI;QACtBC;QACAC,WAAW,EAAE;IACf;IAEA,OAAO;QACLT,OAAO0B;QACPhB;IACF;AACF;AAEA,SAASiB,UAAUC,SAAwB;IACzC,IAAI,CAACA,WAAW;QACd,OAAOzB;IACT;IAEA,OAAO0B,IAAAA,sCAA0B,EAACD;AACpC;AAEA,SAASE,kBACPC,IAAgC;IAEhC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGF;IAE7B,OAAOC,OACJE,GAAG,CAAC,CAAClC;QACJ,MAAMC,OAAO0B,UAAU3B,MAAMC,IAAI;QAEjC,IAAI,CAACA,MAAM;YACT,OAAOE;QACT;QAEA,OAAO;YACLF;YACAM,YAAYP,MAAMO,UAAU,IAAI;YAChCF,MAAML,MAAMI,UAAU,IAAI;YAC1BE,QAAQN,MAAMM,MAAM,IAAI;YACxB2B;QACF;IACF,GACCE,MAAM,CAAC,CAACC,IAAgCA,MAAMjC;AACnD;AAEA,SAASkC,iBACPC,YAA6B;IAE7B,MAAMrC,OAAO0B,UAAUW,aAAajB,GAAG,CAAC;IAExC,IAAI,CAACpB,MAAM;QACT,OAAOE;IACT;IAEA,OAAO;QACLF;QACAM,YAAY+B,aAAajB,GAAG,CAAC,iBAAiB;QAC9ChB,MAAMkC,SAASD,aAAajB,GAAG,CAAC,iBAAiB,KAAK,OAAO;QAC7Df,QAAQiC,SAASD,aAAajB,GAAG,CAAC,aAAa,KAAK,OAAO;QAC3DY,UAAUK,aAAajB,GAAG,CAAC,gBAAgB;IAC7C;AACF;AAEA;;CAEC,GACD,eAAemB,kBACbxC,KAA0B;IAE1B,MAAMyC,YAAYzC,MAAMC,IAAI;IAC5B,IAAIyC;IACJ,IAAI;YACiBC;QAAnBD,oBAAmBC,iBAAAA,IAAAA,yBAAa,EAACF,+BAAdE,eAA0BC,OAAO;IACtD,EAAE,OAAOC,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,GAAGL,UAAU,wFAAwF,CAAC,EACtG;YAAEI;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIH,qBAAqBvC,WAAW;QAClC,IAAI4C;QACJ,IAAI;YACFA,WAAW,MAAM,IAAIC,8BAAiB,CAACN;QACzC,EAAE,OAAOG,OAAO;YACd,MAAM,qBAGL,CAHK,IAAIC,MACR,GAAGL,UAAU,wFAAwF,CAAC,EACtG;gBAAEI;YAAM,IAFJ,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,IAAII;QAIJ,IAAI;YACF,MAAMC,mBAAmBH,SAASI,mBAAmB,CAAC;gBACpD9C,MAAML,MAAMK,IAAI,IAAI;gBACpB,mDAAmD;gBACnDC,QAAQ,AAACN,CAAAA,MAAMM,MAAM,IAAI,CAAA,IAAK;YAChC;YAEA,IAAI4C,iBAAiBxC,MAAM,KAAK,MAAM;gBACpCuC,SAAS;YACX,OAAO;gBACL,MAAMG,gBACJL,SAASM,gBAAgB,CACvBH,iBAAiBxC,MAAM,EACvB,uBAAuB,GAAG,SACvB;gBAEPuC,SAAS;oBAAEC;oBAAkBE;gBAAc;YAC7C;QACF,SAAU;YACRL,SAASO,OAAO;QAClB;QAEA,IAAIL,WAAW,MAAM;gBA6Bf,sEAAsE;YACtE,4EAA4E;YAC5E,kCAAkC;YAClC,oGAAoG;YACpGjD,2BAAAA;YAhCJ,MAAM,EAAEkD,gBAAgB,EAAEE,aAAa,EAAE,GAAGH;YAC5C,MAAMM,sBAAsBC,IAAAA,0CAA8B,EACxDxD,MAAMK,IAAI,IAAI,GACdL,MAAMM,MAAM,IAAI,GAChBoC;YAGF,0GAA0G;YAC1G,IAAIlC,UAAU;YACd,IAAI+C,wBAAwBpD,WAAW;gBACrCsD,QAAQC,KAAK,CACX,wDACA1D;YAEJ,OAAO;oBAMHuD;gBALF,wDAAwD;gBACxD,MAAMI,cAAcJ,oBAAoBK,OAAO,CAACC,OAAO,CACrDX,iBAAiBxC,MAAM;gBAEzBF,UACE+C,EAAAA,kCAAAA,oBAAoBO,UAAU,qBAA9BP,gCAAgC7D,QAAQ,CAACiE,iBACzC,sEAAsE;gBACtE,yEAAyE;gBACzEnE,iBAAiBQ,MAAMC,IAAI;YAC/B;YAEA,MAAM8D,qBAA0C;gBAC9CxD,YAKEP,EAAAA,oBAAAA,MAAMO,UAAU,sBAAhBP,4BAAAA,kBACIgE,OAAO,CAAC,8BAA8B,+BAD1ChE,0BAEIgE,OAAO,CAAC,wBAAwB,QAAO;gBAC7C1D,QAAQ,AAAC4C,CAAAA,iBAAiB5C,MAAM,IAAI,CAAA,IAAK;gBACzCL,MAAMiD,iBAAiBxC,MAAM;gBAC7BN,YAAY8C,iBAAiB7C,IAAI,IAAI;gBACrC,6EAA6E;gBAC7EI,WAAW,EAAE;gBACbD;YACF;YAEA,OAAO;gBACLR,OAAO+D;gBACPrD,QAAQ0C;YACV;QACF;IACF;IAEA,OAAOjD;AACT;AAEA,eAAe8D,yBACblE,OAAgB,EAChBmE,WAAmB,EACnBlE,KAA0B;IAE1B,MAAMiD,SACJ,AAAC,MAAMT,kBAAkBxC,UACzB,4DAA4D;IAC5D,uEAAuE;IACtE,MAAMF,mBAAmBC,SAASC;IACrC,IAAI,CAACiD,QAAQ;QACX,OAAO;IACT;IAEA,IAAIkB,+BAA+BlB,OAAOjD,KAAK,CAACC,IAAI;IACpD,IACEkE,iCAAiC,QACjCA,6BAA6BxE,UAAU,CAAC,YACxC;QACAwE,+BAA+BC,aAAI,CAACC,QAAQ,CAC1CH,aACAI,IAAAA,sBAAa,EAACH;IAElB;IAEA,OAAO;QACLJ,oBAAoB;YAClBtD,WAAWwC,OAAOjD,KAAK,CAACS,SAAS;YACjCH,QAAQ2C,OAAOjD,KAAK,CAACM,MAAM;YAC3BL,MAAMkE;YACN3D,SAASyC,OAAOjD,KAAK,CAACQ,OAAO;YAC7BJ,YAAY6C,OAAOjD,KAAK,CAACI,UAAU;YACnCG,YAAY0C,OAAOjD,KAAK,CAACO,UAAU;QACrC;QACAgE,mBAAmBC,IAAAA,4BAAoB,EAACvB,OAAOjD,KAAK,EAAEiD,OAAOvC,MAAM;IACrE;AACF;AAEO,SAASpB,qBAAqB,EACnCS,OAAO,EACPmE,WAAW,EACXO,QAAQ,EAKT;IACC,OAAO,eACLC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEvC,YAAY,EAAE,GAAG,IAAIwC,IAAIJ,IAAIK,GAAG,EAAG;QAErD,IAAIF,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAOC,sCAAkB,CAACC,UAAU,CAACP;YACvC;YAEA,MAAM5C,OAAO,MAAM,IAAIoD,QAAgB,CAACC,SAASC;gBAC/C,IAAIC,OAAO;gBACXZ,IAAIa,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAd,IAAIa,EAAE,CAAC,OAAO,IAAMH,QAAQE;gBAC5BZ,IAAIa,EAAE,CAAC,SAASF;YAClB;YAEA,MAAMI,UAAUC,KAAKC,KAAK,CAAC5D;YAC3B,MAAM6D,SAAS,MAAMvG,uBAAuB;gBAC1CU;gBACAmE;gBACAlC,QAAQyD,QAAQzD,MAAM;gBACtBC,UAAUwD,QAAQxD,QAAQ;gBAC1B4D,cAAcJ,QAAQI,YAAY;gBAClCC,gBAAgBL,QAAQK,cAAc;YACxC;YAEAC,IAAAA,kDAA0C,EAACH;YAE3C,OAAOX,sCAAkB,CAACe,IAAI,CAACrB,KAAKiB;QACtC,OAAO,IAAIf,aAAa,2BAA2B;YACjD,MAAMoB,oBAAoB3D,aAAajB,GAAG,CAAC,yBAAyB;YAEpE,IAAI6E;YACJ,IAAID,mBAAmB;gBACrB,MAAME,mBAAmB7D,aAAajB,GAAG,CAAC,WAAW;gBACrD,MAAM+E,mBAAmBhC,aAAI,CAACiC,IAAI,CAChCnC,aACA,OACAO,WAAW,QAAQ,IACnB0B;gBAEFD,mBAAmB,MAAMI,IAAAA,8BAAgB,EAACF,kBAAkB,GAAG;YACjE,OAAO;gBACL,MAAMpG,QAAQqC,iBAAiBC;gBAC/B,IAAI,CAACtC,OAAO,OAAOiF,sCAAkB,CAACC,UAAU,CAACP;gBACjDuB,mBAAmB,MAAMI,IAAAA,8BAAgB,EACvCtG,MAAMC,IAAI,EACVD,MAAMK,IAAI,IAAI,GACdL,MAAMM,MAAM,IAAI;YAEpB;YAEA,IAAI4F,iBAAiBxC,KAAK,EAAE;gBAC1B,OAAOuB,sCAAkB,CAACsB,mBAAmB,CAAC5B;YAChD;YACA,IAAI,CAACuB,iBAAiBM,KAAK,EAAE;gBAC3B,OAAOvB,sCAAkB,CAACwB,QAAQ,CAAC9B;YACrC;YACA,OAAOM,sCAAkB,CAACyB,SAAS,CAAC/B;QACtC;QAEA,OAAOC;IACT;AACF;AAEO,SAASrF,uBAAuBQ,OAAgB;IACrD,OAAO,eACL2E,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEvC,YAAY,EAAE,GAAG,IAAIwC,IAAIJ,IAAIK,GAAG,EAAG;QAErD,IAAIF,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,IAAI+B,WAAWrE,aAAajB,GAAG,CAAC;QAEhC,IAAI,CAACsF,UAAU;YACb,OAAO1B,sCAAkB,CAACC,UAAU,CAACP;QACvC;QAEA,mDAAmD;QACnD,iEAAiE;QACjE,IACEgC,SAAShH,UAAU,CAAC,iBACpBgH,SAAShH,UAAU,CAAC,yBACpB;YACA,MAAMiH,YAAYjE,IAAAA,yBAAa,EAACgE;YAEhC,IAAIC,WAAW;gBACb,OAAO3B,sCAAkB,CAACe,IAAI,CAACrB,KAAKiC,UAAUhE,OAAO;YACvD;YAEA,OAAOqC,sCAAkB,CAACyB,SAAS,CAAC/B;QACtC;QAEA,IAAI;YACF,kDAAkD;YAClDgC,WAAWE,UAAUF;QACvB,EAAE,OAAM;YACN,OAAO1B,sCAAkB,CAACC,UAAU,CAACP;QACvC;QAEA,IAAIP,aAAI,CAAC0C,UAAU,CAACH,WAAW;YAC7BA,WAAW/F,IAAAA,sBAAa,EAAC+F,UAAU5F,IAAI;QACzC;QAEA,IAAI;YACF,MAAMgG,kBAAkB,MAAMhH,QAAQiH,YAAY,CAACL;YAEnD,IAAII,iBAAiB;gBACnB,OAAO9B,sCAAkB,CAACgC,UAAU,CAACtC,KAAKoC;YAC5C;YAEA,IAAIJ,SAAShH,UAAU,CAAC,UAAU;gBAChC,MAAMiH,YAAY,MAAMM,IAAAA,0CAAoB,EAACP;gBAE7C,IAAIC,WAAW;oBACb,OAAO3B,sCAAkB,CAACe,IAAI,CAACrB,KAAKiC;gBACtC;YACF;QACF,EAAE,OAAO/D,OAAO;YACd,OAAOoC,sCAAkB,CAACsB,mBAAmB,CAC3C5B,KACA,qBAKC,CALD,IAAI7B,MACF,CAAC,8BAA8B,EAAE6D,SAAS,2BAA2B,CAAC,EACtE;gBACE9D;YACF,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKA;QAEJ;QAEAoC,sCAAkB,CAACyB,SAAS,CAAC/B;IAC/B;AACF;AAEO,eAAetF,uBAAuB,EAC3CU,OAAO,EACPmE,WAAW,EACXlC,MAAM,EACNC,QAAQ,EACR4D,YAAY,EACZC,cAAc,EAQf;IACC,MAAMqB,cAAcrF,kBAAkB;QACpCE;QACAC;QACA4D;QACAC;IACF;IAEA,OAAOX,QAAQiC,GAAG,CAChBD,YAAYjF,GAAG,CAAC,OAAOlC;QACrB,IAAI;YACF,MAAMqH,aAAa,MAAMpD,yBACvBlE,SACAmE,aACAlE;YAEF,IAAIqH,eAAe,MAAM;gBACvB,OAAO;oBACLC,QAAQ;oBACRC,QAAQ;gBACV;YACF;YACA,OAAO;gBAAED,QAAQ;gBAAaE,OAAOH;YAAW;QAClD,EAAE,OAAO3D,OAAO;YACd,OAAO;gBACL4D,QAAQ;gBACRC,QAAQE,IAAAA,iBAAO,EAAC/D,OAAO;oBAAEgE,QAAQ;gBAAM;YACzC;QACF;IACF;AAEJ", "ignoreList": [0]}