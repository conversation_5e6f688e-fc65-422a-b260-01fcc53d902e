"use strict";exports.id=702,exports.ids=[702],exports.modules={7044:(a,b,c)=>{c.d(b,{B:()=>d});let d="undefined"!=typeof window},12157:(a,b,c)=>{c.d(b,{L:()=>d});let d=(0,c(43210).createContext)({})},15124:(a,b,c)=>{c.d(b,{E:()=>e});var d=c(43210);let e=c(7044).B?d.useLayoutEffect:d.useEffect},18171:(a,b,c)=>{c.d(b,{s:()=>e});var d=c(74479);function e(a){return(0,d.G)(a)&&"offsetHeight"in a}},21279:(a,b,c)=>{c.d(b,{t:()=>d});let d=(0,c(43210).createContext)(null)},32582:(a,b,c)=>{c.d(b,{Q:()=>d});let d=(0,c(43210).createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"})},44801:(a,b,c)=>{let d;c.d(b,{P:()=>fn});var e=c(43210);let f=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],g=new Set(f),h=a=>180*a/Math.PI,i=a=>k(h(Math.atan2(a[1],a[0]))),j={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:i,rotateZ:i,skewX:a=>h(Math.atan(a[1])),skewY:a=>h(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},k=a=>((a%=360)<0&&(a+=360),a),l=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),m=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),n={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:l,scaleY:m,scale:a=>(l(a)+m(a))/2,rotateX:a=>k(h(Math.atan2(a[6],a[5]))),rotateY:a=>k(h(Math.atan2(-a[2],a[0]))),rotateZ:i,rotate:i,skewX:a=>h(Math.atan(a[4])),skewY:a=>h(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function o(a){return+!!a.includes("scale")}function p(a,b){let c,d;if(!a||"none"===a)return o(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=n,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=j,d=b}if(!d)return o(b);let f=c[b],g=d[1].split(",").map(q);return"function"==typeof f?f(g):g[f]}function q(a){return parseFloat(a.trim())}let r=a=>b=>"string"==typeof b&&b.startsWith(a),s=r("--"),t=r("var(--"),u=a=>!!t(a)&&v.test(a.split("/*")[0].trim()),v=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function w({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}let x=(a,b,c)=>a+(b-a)*c;function y(a){return void 0===a||1===a}function z({scale:a,scaleX:b,scaleY:c}){return!y(a)||!y(b)||!y(c)}function A(a){return z(a)||B(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function B(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}function C(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function D(a,b=0,c=1,d,e){a.min=C(a.min,b,c,d,e),a.max=C(a.max,b,c,d,e)}function E(a,{x:b,y:c}){D(a.x,b.translate,b.scale,b.originPoint),D(a.y,c.translate,c.scale,c.originPoint)}function F(a,b){a.min=a.min+b,a.max=a.max+b}function G(a,b,c,d,e=.5){let f=x(a.min,a.max,e);D(a,b,c,f,d)}function H(a,b){G(a.x,b.x,b.scaleX,b.scale,b.originX),G(a.y,b.y,b.scaleY,b.scale,b.originY)}function I(a,b){return w(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let J=new Set(["width","height","top","left","right","bottom",...f]),K=(a,b,c)=>c>b?b:c<a?a:c,L={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},M={...L,transform:a=>K(0,1,a)},N={...L,default:1},O=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),P=O("deg"),Q=O("%"),R=O("px"),S=O("vh"),T=O("vw"),U={...Q,parse:a=>Q.parse(a)/100,transform:a=>Q.transform(100*a)},V=a=>b=>b.test(a),W=[L,R,Q,P,T,S,{test:a=>"auto"===a,parse:a=>a}],X=a=>W.find(V(a)),Y=()=>{},Z=()=>{},$=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),_=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,aa=a=>a===L||a===R,ab=new Set(["x","y","z"]),ac=f.filter(a=>!ab.has(a)),ad={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>p(b,"x"),y:(a,{transform:b})=>p(b,"y")};ad.translateX=ad.x,ad.translateY=ad.y;let ae=a=>a,af={},ag=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ah={value:null,addProjectionMetrics:null};function ai(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=ag.reduce((a,c)=>(a[c]=function(a,b){let c=new Set,d=new Set,e=!1,f=!1,g=new WeakSet,h={delta:0,timestamp:0,isProcessing:!1},i=0;function j(b){g.has(b)&&(k.schedule(b),a()),i++,b(h)}let k={schedule:(a,b=!1,f=!1)=>{let h=f&&e?c:d;return b&&g.add(a),h.has(a)||h.add(a),a},cancel:a=>{d.delete(a),g.delete(a)},process:a=>{if(h=a,e){f=!0;return}e=!0,[c,d]=[d,c],c.forEach(j),b&&ah.value&&ah.value.frameloop[b].push(i),i=0,c.clear(),e=!1,f&&(f=!1,k.process(a))}};return k}(f,b?c:void 0),a),{}),{setup:h,read:i,resolveKeyframes:j,preUpdate:k,update:l,preRender:m,render:n,postRender:o}=g,p=()=>{let f=af.useManualTiming?e.timestamp:performance.now();c=!1,af.useManualTiming||(e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1)),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),n.process(e),o.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(p))};return{schedule:ag.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(p)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<ag.length;b++)g[ag[b]].cancel(a)},state:e,steps:g}}let{schedule:aj,cancel:ak,state:al,steps:am}=ai("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ae,!0),an=new Set,ao=!1,ap=!1,aq=!1;function ar(){if(ap){let a=Array.from(an).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return ac.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}ap=!1,ao=!1,an.forEach(a=>a.complete(aq)),an.clear()}function as(){an.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(ap=!0)})}class at{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(an.add(this),ao||(ao=!0,aj.read(as),aj.resolveKeyframes(ar))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),an.delete(this)}cancel(){"scheduled"===this.state&&(an.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let au=a=>/^0[^.\s]+$/u.test(a),av=a=>Math.round(1e5*a)/1e5,aw=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ax=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ay=(a,b)=>c=>!!("string"==typeof c&&ax.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),az=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(aw);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},aA={...L,transform:a=>Math.round(K(0,255,a))},aB={test:ay("rgb","red"),parse:az("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+aA.transform(a)+", "+aA.transform(b)+", "+aA.transform(c)+", "+av(M.transform(d))+")"},aC={test:ay("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:aB.transform},aD={test:ay("hsl","hue"),parse:az("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+Q.transform(av(b))+", "+Q.transform(av(c))+", "+av(M.transform(d))+")"},aE={test:a=>aB.test(a)||aC.test(a)||aD.test(a),parse:a=>aB.test(a)?aB.parse(a):aD.test(a)?aD.parse(a):aC.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?aB.transform(a):aD.transform(a),getAnimatableNone:a=>{let b=aE.parse(a);return b.alpha=0,aE.transform(b)}},aF=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,aG="number",aH="color",aI=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function aJ(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(aI,a=>(aE.test(a)?(d.color.push(f),e.push(aH),c.push(aE.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(aG),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function aK(a){return aJ(a).values}function aL(a){let{split:b,types:c}=aJ(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===aG?e+=av(a[f]):b===aH?e+=aE.transform(a[f]):e+=a[f]}return e}}let aM=a=>"number"==typeof a?0:aE.test(a)?aE.getAnimatableNone(a):a,aN={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(aw)?.length||0)+(a.match(aF)?.length||0)>0},parse:aK,createTransformer:aL,getAnimatableNone:function(a){let b=aK(a);return aL(a)(b.map(aM))}},aO=new Set(["brightness","contrast","saturate","opacity"]);function aP(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(aw)||[];if(!d)return a;let e=c.replace(d,""),f=+!!aO.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let aQ=/\b([a-z-]*)\(.*?\)/gu,aR={...aN,getAnimatableNone:a=>{let b=a.match(aQ);return b?b.map(aP).join(" "):a}},aS={...L,transform:Math.round},aT={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R,rotate:P,rotateX:P,rotateY:P,rotateZ:P,scale:N,scaleX:N,scaleY:N,scaleZ:N,skew:P,skewX:P,skewY:P,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:M,originX:U,originY:U,originZ:R,zIndex:aS,fillOpacity:M,strokeOpacity:M,numOctaves:aS},aU={...aT,color:aE,backgroundColor:aE,outlineColor:aE,fill:aE,stroke:aE,borderColor:aE,borderTopColor:aE,borderRightColor:aE,borderBottomColor:aE,borderLeftColor:aE,filter:aR,WebkitFilter:aR},aV=a=>aU[a];function aW(a,b){let c=aV(a);return c!==aR&&(c=aN),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let aX=new Set(["auto","none","0"]);class aY extends at{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&u(d=d.trim())){let e=function a(b,c,d=1){Z(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=_.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return $(a)?parseFloat(a):a}return u(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!J.has(c)||2!==a.length)return;let[d,e]=a,f=X(d),g=X(e);if(f!==g)if(aa(f)&&aa(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else ad[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||au(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!aX.has(b)&&aJ(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=aW(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ad[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=ad[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}let aZ=a=>!!(a&&a.getVelocity);function a$(){d=void 0}let a_={now:()=>(void 0===d&&a_.set(al.isProcessing||af.useManualTiming?al.timestamp:performance.now()),d),set:a=>{d=a,queueMicrotask(a$)}};function a0(a,b){-1===a.indexOf(b)&&a.push(b)}function a1(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}class a2{constructor(){this.subscriptions=[]}add(a){return a0(this.subscriptions,a),()=>a1(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let a3={current:void 0};class a4{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=a=>{let b=a_.now();if(this.updatedAt!==b&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty()},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=a_.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new a2);let c=this.events[a].add(b);return"change"===a?()=>{c(),aj.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a){this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return a3.current&&a3.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var a;let b=a_.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||b-this.updatedAt>30)return 0;let c=Math.min(this.updatedAt-this.prevUpdatedAt,30);return a=parseFloat(this.current)-parseFloat(this.prevFrameValue),c?1e3/c*a:0}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function a5(a,b){return new a4(a,b)}let a6=[...W,aE,aN],{schedule:a7}=ai(queueMicrotask,!1),a8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},a9={};for(let a in a8)a9[a]={isEnabled:b=>a8[a].some(a=>!!b[a])};let ba=()=>({translate:0,scale:1,origin:0,originPoint:0}),bb=()=>({x:ba(),y:ba()}),bc=()=>({min:0,max:0}),bd=()=>({x:bc(),y:bc()});var be=c(7044);let bf={current:null},bg={current:!1},bh=new WeakMap;function bi(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}function bj(a){return"string"==typeof a||Array.isArray(a)}let bk=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],bl=["initial",...bk];function bm(a){return bi(a.animate)||bl.some(b=>bj(a[b]))}function bn(a){return!!(bm(a)||a.variants)}function bo(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function bp(a,b,c,d){if("function"==typeof b){let[e,f]=bo(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=bo(d);b=b(void 0!==c?c:a.custom,e,f)}return b}let bq=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class br{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=at,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=a_.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,aj.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=bm(b),this.isVariantNode=bn(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&aZ(b)&&b.set(h[a])}}mount(a){this.current=a,bh.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),bg.current||function(){if(bg.current=!0,be.B)if(window.matchMedia){let a=window.matchMedia("(prefers-reduced-motion)"),b=()=>bf.current=a.matches;a.addEventListener("change",b),b()}else bf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||bf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),ak(this.notifyUpdate),ak(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=g.has(a);d&&this.onBindTransform&&this.onBindTransform();let e=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&aj.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{e(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in a9){let b=a9[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):bd()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<bq.length;b++){let c=bq[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(aZ(e))a.addValue(d,e);else if(aZ(f))a.addValue(d,a5(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,a5(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=a5(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&($(c)||au(c)))c=parseFloat(c);else{let d;d=c,!a6.find(V(d))&&aN.test(b)&&(c=aW(a,b))}this.setBaseTarget(a,aZ(c)?c.get():c)}return aZ(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=bp(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||aZ(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new a2),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){a7.render(this.render)}}class bs extends br{constructor(){super(...arguments),this.KeyframeResolver=aY}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;aZ(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}let bt=(a,b)=>b&&"number"==typeof a?b.transform(a):a,bu={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bv=f.length;function bw(a,b,c){let{style:d,vars:e,transformOrigin:h}=a,i=!1,j=!1;for(let a in b){let c=b[a];if(g.has(a)){i=!0;continue}if(s(a)){e[a]=c;continue}{let b=bt(c,aT[a]);a.startsWith("origin")?(j=!0,h[a]=b):d[a]=b}}if(!b.transform&&(i||c?d.transform=function(a,b,c){let d="",e=!0;for(let g=0;g<bv;g++){let h=f[g],i=a[h];if(void 0===i)continue;let j=!0;if(!(j="number"==typeof i?i===+!!h.startsWith("scale"):0===parseFloat(i))||c){let a=bt(i,aT[h]);if(!j){e=!1;let b=bu[h]||h;d+=`${b}(${a}) `}c&&(b[h]=a)}}return d=d.trim(),c?d=c(b,e?"":d):e&&(d="none"),d}(b,a.transform,c):d.transform&&(d.transform="none")),j){let{originX:a="50%",originY:b="50%",originZ:c=0}=h;d.transformOrigin=`${a} ${b} ${c}`}}function bx(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}let by={};function bz(a,{layout:b,layoutId:c}){return g.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!by[a]||"opacity"===a)}function bA(a,b,c){let{style:d}=a,e={};for(let f in d)(aZ(d[f])||b.style&&aZ(b.style[f])||bz(f,a)||c?.getValue(f)?.liveStyle!==void 0)&&(e[f]=d[f]);return e}class bB extends bs{constructor(){super(...arguments),this.type="html",this.renderInstance=bx}readValueFromInstance(a,b){if(g.has(b))return this.projection?.isProjecting?o(b):((a,b)=>{let{transform:c="none"}=getComputedStyle(a);return p(c,b)})(a,b);{let c=window.getComputedStyle(a),d=(s(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return I(a,b)}build(a,b,c){bw(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return bA(a,b,c)}}let bC=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),bD={offset:"stroke-dashoffset",array:"stroke-dasharray"},bE={offset:"strokeDashoffset",array:"strokeDasharray"};function bF(a,{attrX:b,attrY:c,attrScale:d,pathLength:e,pathSpacing:f=1,pathOffset:g=0,...h},i,j,k){if(bw(a,h,j),i){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:l,style:m}=a;l.transform&&(m.transform=l.transform,delete l.transform),(m.transform||l.transformOrigin)&&(m.transformOrigin=l.transformOrigin??"50% 50%",delete l.transformOrigin),m.transform&&(m.transformBox=k?.transformBox??"fill-box",delete l.transformBox),void 0!==b&&(l.x=b),void 0!==c&&(l.y=c),void 0!==d&&(l.scale=d),void 0!==e&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?bD:bE;a[f.offset]=R.transform(-d);let g=R.transform(b),h=R.transform(c);a[f.array]=`${g} ${h}`}(l,e,f,g,!1)}let bG=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),bH=a=>"string"==typeof a&&"svg"===a.toLowerCase();function bI(a,b,c){let d=bA(a,b,c);for(let c in a)(aZ(a[c])||aZ(b[c]))&&(d[-1!==f.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return d}class bJ extends bs{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=bd}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(g.has(b)){let a=aV(b);return a&&a.default||0}return b=bG.has(b)?b:bC(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return bI(a,b,c)}build(a,b,c){bF(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in bx(a,b,void 0,d),b.attrs)a.setAttribute(bG.has(c)?c:bC(c),b.attrs[c])}mount(a){this.isSVGTag=bH(a.tagName),super.mount(a)}}let bK=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function bL(a){if("string"!=typeof a||a.includes("-"));else if(bK.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}var bM=c(60687),bN=c(12157);let bO=(0,e.createContext)({strict:!1});var bP=c(32582);let bQ=(0,e.createContext)({});function bR(a){return Array.isArray(a)?a.join(" "):a}let bS=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bT(a,b,c){for(let d in b)aZ(b[d])||bz(d,c)||(a[d]=b[d])}let bU=()=>({...bS(),attrs:{}}),bV=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function bW(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||bV.has(a)}let bX=a=>!bW(a);try{!function(a){"function"==typeof a&&(bX=b=>b.startsWith("on")?!bW(b):a(b))}(require("@emotion/is-prop-valid").default)}catch{}var bY=c(21279),bZ=c(72789);function b$(a){return aZ(a)?a.get():a}let b_=a=>(b,c)=>{let d=(0,e.useContext)(bQ),f=(0,e.useContext)(bY.t),g=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,e){return{latestValues:function(a,b,c,d){let e={},f=d(a,{});for(let a in f)e[a]=b$(f[a]);let{initial:g,animate:h}=a,i=bm(a),j=bn(a);b&&j&&!i&&!1!==a.inherit&&(void 0===g&&(g=b.initial),void 0===h&&(h=b.animate));let k=!!c&&!1===c.initial,l=(k=k||!1===g)?h:g;if(l&&"boolean"!=typeof l&&!bi(l)){let b=Array.isArray(l)?l:[l];for(let c=0;c<b.length;c++){let d=bp(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=k?b.length-1:0;b=b[a]}null!==b&&(e[a]=b)}for(let b in a)e[b]=a[b]}}}return e}(c,d,e,a),renderState:b()}})(a,b,d,f);return c?g():(0,bZ.M)(g)},b0=b_({scrapeMotionValuesFromProps:bA,createRenderState:bS}),b1=b_({scrapeMotionValuesFromProps:bI,createRenderState:bU}),b2=Symbol.for("motionComponentSymbol");function b3(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}let b4="data-"+bC("framerAppearId"),b5=(0,e.createContext)({});var b6=c(15124);function b7(a,{forwardMotionProps:b=!1}={},c,d){c&&function(a){for(let b in a)a9[b]={...a9[b],...a[b]}}(c);let f=bL(a)?b1:b0;function g(c,g){var h;let i,j={...(0,e.useContext)(bP.Q),...c,layoutId:function({layoutId:a}){let b=(0,e.useContext)(bN.L).id;return b&&void 0!==a?b+"-"+a:a}(c)},{isStatic:k}=j,l=function(a){let{initial:b,animate:c}=function(a,b){if(bm(a)){let{initial:b,animate:c}=a;return{initial:!1===b||bj(b)?b:void 0,animate:bj(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,e.useContext)(bQ));return(0,e.useMemo)(()=>({initial:b,animate:c}),[bR(b),bR(c)])}(c),m=f(c,k);if(!k&&be.B){(0,e.useContext)(bO).strict;let b=function(a){let{drag:b,layout:c}=a9;if(!b&&!c)return{};let d={...b,...c};return{MeasureLayout:b?.isEnabled(a)||c?.isEnabled(a)?d.MeasureLayout:void 0,ProjectionNode:d.ProjectionNode}}(j);i=b.MeasureLayout,l.visualElement=function(a,b,c,d,f){let{visualElement:g}=(0,e.useContext)(bQ),h=(0,e.useContext)(bO),i=(0,e.useContext)(bY.t),j=(0,e.useContext)(bP.Q).reducedMotion,k=(0,e.useRef)(null);d=d||h.renderer,!k.current&&d&&(k.current=d(a,{visualState:b,parent:g,props:c,presenceContext:i,blockInitialAnimation:!!i&&!1===i.initial,reducedMotionConfig:j}));let l=k.current,m=(0,e.useContext)(b5);l&&!l.projection&&f&&("html"===l.type||"svg"===l.type)&&function(a,b,c,d){let{layoutId:e,layout:f,drag:g,dragConstraints:h,layoutScroll:i,layoutRoot:j,layoutCrossfade:k}=b;a.projection=new c(a.latestValues,b["data-framer-portal-id"]?void 0:function a(b){if(b)return!1!==b.options.allowProjection?b.projection:a(b.parent)}(a.parent)),a.projection.setOptions({layoutId:e,layout:f,alwaysMeasureLayout:!!g||h&&b3(h),visualElement:a,animationType:"string"==typeof f?f:"both",initialPromotionConfig:d,crossfade:k,layoutScroll:i,layoutRoot:j})}(k.current,c,f,m);let n=(0,e.useRef)(!1);(0,e.useInsertionEffect)(()=>{l&&n.current&&l.update(c,i)});let o=c[b4],p=(0,e.useRef)(!!o&&!window.MotionHandoffIsComplete?.(o)&&window.MotionHasOptimisedAnimation?.(o));return(0,b6.E)(()=>{l&&(n.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),l.scheduleRenderMicrotask(),p.current&&l.animationState&&l.animationState.animateChanges())}),(0,e.useEffect)(()=>{l&&(!p.current&&l.animationState&&l.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(o)}),p.current=!1))}),l}(a,m,j,d,b.ProjectionNode)}return(0,bM.jsxs)(bQ.Provider,{value:l,children:[i&&l.visualElement?(0,bM.jsx)(i,{visualElement:l.visualElement,...j}):null,function(a,b,c,{latestValues:d},f,g=!1){let h=(bL(a)?function(a,b,c,d){let f=(0,e.useMemo)(()=>{let c=bU();return bF(c,b,bH(d),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};bT(b,a.style,a),f.style={...b,...f.style}}return f}:function(a,b){let c={},d=function(a,b){let c=a.style||{},d={};return bT(d,c,a),Object.assign(d,function({transformTemplate:a},b){return(0,e.useMemo)(()=>{let c=bS();return bw(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),d}(a,b);return a.drag&&!1!==a.dragListener&&(c.draggable=!1,d.userSelect=d.WebkitUserSelect=d.WebkitTouchCallout="none",d.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(c.tabIndex=0),c.style=d,c})(b,d,f,a),i=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(bX(e)||!0===c&&bW(e)||!b&&!bW(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(b,"string"==typeof a,g),j=a!==e.Fragment?{...i,...h,ref:c}:{},{children:k}=b,l=(0,e.useMemo)(()=>aZ(k)?k.get():k,[k]);return(0,e.createElement)(a,{...j,children:l})}(a,c,(h=l.visualElement,(0,e.useCallback)(a=>{a&&m.onMount&&m.onMount(a),h&&(a?h.mount(a):h.unmount()),g&&("function"==typeof g?g(a):b3(g)&&(g.current=a))},[h])),m,k,b)]})}g.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;let h=(0,e.forwardRef)(g);return h[b2]=a,h}function b8(a,b,c){let d=a.getProps();return bp(d,b,void 0!==c?c:d.custom,a)}function b9(a,b){return a?.[b]??a?.default??a}let ca=a=>Array.isArray(a);function cb(a,b){let c=a.getValue("willChange");if(aZ(c)&&c.add)return c.add(b);if(!c&&af.WillChange){let c=new af.WillChange("auto");a.addValue("willChange",c),c.add(b)}}function cc(a){a.duration=0,a.type}let cd=(a,b)=>c=>b(a(c)),ce=(...a)=>a.reduce(cd),cf=a=>1e3*a,cg={layout:0,mainThread:0,waapi:0};function ch(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function ci(a,b){return c=>c>0?b:a}let cj=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},ck=[aC,aB,aD];function cl(a){let b=ck.find(b=>b.test(a));if(Y(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===aD&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=ch(h,d,a+1/3),f=ch(h,d,a),g=ch(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let cm=(a,b)=>{let c=cl(a),d=cl(b);if(!c||!d)return ci(a,b);let e={...c};return a=>(e.red=cj(c.red,d.red,a),e.green=cj(c.green,d.green,a),e.blue=cj(c.blue,d.blue,a),e.alpha=x(c.alpha,d.alpha,a),aB.transform(e))},cn=new Set(["none","hidden"]);function co(a,b){return c=>x(a,b,c)}function cp(a){return"number"==typeof a?co:"string"==typeof a?u(a)?ci:aE.test(a)?cm:cs:Array.isArray(a)?cq:"object"==typeof a?aE.test(a)?cm:cr:ci}function cq(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>cp(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function cr(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=cp(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let cs=(a,b)=>{let c=aN.createTransformer(b),d=aJ(a),e=aJ(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?cn.has(a)&&!e.values.length||cn.has(b)&&!d.values.length?function(a,b){return cn.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):ce(cq(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):(Y(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),ci(a,b))};function ct(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?x(a,b,c):cp(a)(a,b)}let cu=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>aj.update(b,a),stop:()=>ak(b),now:()=>al.isProcessing?al.timestamp:a_.now()}},cv=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function cw(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function cx(a,b,c){var d,e;let f=Math.max(b-5,0);return d=c-a(f),(e=b-f)?1e3/e*d:0}let cy={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function cz(a,b){return a*Math.sqrt(1-b*b)}let cA=["duration","bounce"],cB=["stiffness","damping","mass"];function cC(a,b){return b.some(b=>void 0!==a[b])}function cD(a=cy.visualDuration,b=cy.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],h=d.keyframes[d.keyframes.length-1],i={done:!1,value:g},{stiffness:j,damping:k,mass:l,duration:m,velocity:n,isResolvedFromDuration:o}=function(a){let b={velocity:cy.velocity,stiffness:cy.stiffness,damping:cy.damping,mass:cy.mass,isResolvedFromDuration:!1,...a};if(!cC(a,cB)&&cC(a,cA))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*K(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:cy.mass,stiffness:d,damping:e}}else{let c=function({duration:a=cy.duration,bounce:b=cy.bounce,velocity:c=cy.velocity,mass:d=cy.mass}){let e,f;Y(a<=cf(cy.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let g=1-b;g=K(cy.minDamping,cy.maxDamping,g),a=K(cy.minDuration,cy.maxDuration,a/1e3),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/cz(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=cz(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let h=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=cf(a),isNaN(h))return{stiffness:cy.stiffness,damping:cy.damping,duration:a};{let b=Math.pow(h,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:cy.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-((d.velocity||0)/1e3)}),p=n||0,q=k/(2*Math.sqrt(j*l)),r=h-g,s=Math.sqrt(j/l)/1e3,t=5>Math.abs(r);if(e||(e=t?cy.restSpeed.granular:cy.restSpeed.default),f||(f=t?cy.restDelta.granular:cy.restDelta.default),q<1){let a=cz(s,q);c=b=>h-Math.exp(-q*s*b)*((p+q*s*r)/a*Math.sin(a*b)+r*Math.cos(a*b))}else if(1===q)c=a=>h-Math.exp(-s*a)*(r+(p+s*r)*a);else{let a=s*Math.sqrt(q*q-1);c=b=>{let c=Math.exp(-q*s*b),d=Math.min(a*b,300);return h-c*((p+q*s*r)*Math.sinh(d)+a*r*Math.cosh(d))/a}}let u={calculatedDuration:o&&m||null,next:a=>{let b=c(a);if(o)i.done=a>=m;else{let d=0===a?p:0;q<1&&(d=0===a?cf(p):cx(c,a,b));let g=Math.abs(h-b)<=f;i.done=Math.abs(d)<=e&&g}return i.value=i.done?h:b,i},toString:()=>{let a=Math.min(cw(u),2e4),b=cv(b=>u.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return u}function cE({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=cD({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:cx(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}cD.applyToOptions=a=>{let b=function(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(cw(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:e/1e3}}(a,100,cD);return a.ease=b.ease,a.duration=cf(b.duration),a.type="keyframes",a};let cF=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function cG(a,b,c,d){return a===b&&c===d?ae:e=>0===e||1===e?e:cF(function(a,b,c,d,e){let f,g,h=0;do(f=cF(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12);return g}(e,0,1,a,c),b,d)}let cH=cG(.42,0,1,1),cI=cG(0,0,.58,1),cJ=cG(.42,0,.58,1),cK=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,cL=a=>b=>1-a(1-b),cM=cG(.33,1.53,.69,.99),cN=cL(cM),cO=cK(cN),cP=a=>(a*=2)<1?.5*cN(a):.5*(2-Math.pow(2,-10*(a-1))),cQ=a=>1-Math.sin(Math.acos(a)),cR=cL(cQ),cS=cK(cQ),cT=a=>Array.isArray(a)&&"number"==typeof a[0],cU={linear:ae,easeIn:cH,easeInOut:cJ,easeOut:cI,circIn:cQ,circInOut:cS,circOut:cR,backIn:cN,backInOut:cO,backOut:cM,anticipate:cP},cV=a=>{if(cT(a)){Z(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return cG(b,c,d,e)}return"string"==typeof a?(Z(void 0!==cU[a],`Invalid easing type '${a}'`,"invalid-easing-type"),cU[a]):a},cW=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d};function cX({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(cV):cV(d),g={done:!1,value:b[0]},h=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(Z(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let g=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let h=function(a,b,c){let d=[],e=c||af.mix||ct,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=ce(Array.isArray(b)?b[c]||ae:b,f)),d.push(f)}return d}(b,d,e),i=h.length,j=c=>{if(g&&c<a[0])return b[0];let d=0;if(i>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=cW(a[d],a[d+1],c);return h[d](e)};return c?b=>j(K(a[0],a[f-1],b)):j}((e=c&&c.length===b.length?c:function(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=cW(0,b,d);a.push(x(c,1,e))}}(b,a.length-1),b}(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||cJ).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(g.value=h(b),g.done=b>=a,g)}}let cY=a=>null!==a;function cZ(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(cY),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let c$={decay:cE,inertia:cE,tween:cX,keyframes:cX,spring:cD};function c_(a){"string"==typeof a.type&&(a.type=c$[a.type])}class c0{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let c1=a=>a/100;class c2 extends c0{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==a_.now()&&this.tick(a_.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},cg.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;c_(a);let{type:b=cX,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:g}=a,h=b||cX;h!==cX&&"number"!=typeof g[0]&&(this.mixKeyframes=ce(c1,ct(g[0],g[1])),g=[0,100]);let i=h({...a,keyframes:g});"mirror"===e&&(this.mirroredGenerator=h({...a,keyframes:[...g].reverse(),velocity:-f})),null===i.calculatedDuration&&(i.calculatedDuration=cw(i));let{calculatedDuration:j}=i;this.calculatedDuration=j,this.resolvedDuration=j+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=i}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:g,calculatedDuration:h}=this;if(null===this.startTime)return c.next(0);let{delay:i=0,keyframes:j,repeat:k,repeatType:l,repeatDelay:m,type:n,onUpdate:o,finalKeyframe:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let q=this.currentTime-i*(this.playbackSpeed>=0?1:-1),r=this.playbackSpeed>=0?q<0:q>d;this.currentTime=Math.max(q,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let s=this.currentTime,t=c;if(k){let a=Math.min(this.currentTime,d)/g,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,k+1))%2&&("reverse"===l?(c=1-c,m&&(c-=m/g)):"mirror"===l&&(t=f)),s=K(0,1,c)*g}let u=r?{done:!1,value:j[0]}:t.next(s);e&&(u.value=e(u.value));let{done:v}=u;r||null===h||(v=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&v);return w&&n!==cE&&(u.value=cZ(j,this.options,p,this.speed)),o&&o(u.value),w&&this.finish(),u}then(a,b){return this.finished.then(a,b)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(a){a=cf(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(a_.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:a=cu,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(a_.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,cg.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}function c3(a){let b;return()=>(void 0===b&&(b=a()),b)}let c4=c3(()=>void 0!==window.ScrollTimeline),c5={},c6=function(a,b){let c=c3(a);return()=>c5[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),c7=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,c8={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:c7([0,.65,.55,1]),circOut:c7([.55,0,1,.45]),backIn:c7([.31,.01,.66,-.59]),backOut:c7([.33,1.53,.69,.99])};function c9(a){return"function"==typeof a&&"applyToOptions"in a}class da extends c0{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:h}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,Z("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let i=function({type:a,...b}){return c9(a)&&c6()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeOut",times:i}={},j){let k={[b]:c};i&&(k.offset=i);let l=function a(b,c){if(b)return"function"==typeof b?c6()?cv(b,c):"ease-out":cT(b)?c7(b):Array.isArray(b)?b.map(b=>a(b,c)||c8.easeOut):c8[b]}(h,e);Array.isArray(l)&&(k.easing=l),ah.value&&cg.waapi++;let m={delay:d,duration:e,easing:Array.isArray(l)?"linear":l,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};j&&(m.pseudoElement=j);let n=a.animate(k,m);return ah.value&&n.finished.finally(()=>{cg.waapi--}),n}(b,c,d,i,e),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=cZ(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}h?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(a){this.finishedTime=null,this.animation.currentTime=cf(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&c4())?(this.animation.timeline=a,ae):b(this)}}let db={anticipate:cP,backInOut:cO,circInOut:cS};class dc extends da{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in db&&(a.ease=db[a.ease])}(a),c_(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:e,...f}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let g=new c2({...f,autoplay:!1}),h=cf(this.finishedTime??this.time);b.setWithVelocity(g.sample(h-10).value,g.sample(h).value,10),g.stop()}}let dd=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(aN.test(a)||"0"===a)&&!a.startsWith("url(")),de=new Set(["opacity","clipPath","filter","transform"]),df=c3(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class dg extends c0{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:j,...k}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=a_.now();let l={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:j,...k},m=j?.KeyframeResolver||at;this.keyframeResolver=new m(g,(a,b,c)=>this.onKeyframesResolved(a,b,l,!c),h,i,j),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:e,type:f,velocity:g,delay:h,isHandoff:i,onUpdate:j}=c;this.resolvedAt=a_.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=dd(e,b),h=dd(f,b);return Y(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||c9(c))&&d)}(a,e,f,g)&&((af.instantAnimations||!h)&&j?.(cZ(a,c,b)),a[0]=a[a.length-1],cc(c),c.repeat=0);let k={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},l=!i&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return df()&&c&&de.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(k)?new dc({...k,element:k.motionValue.owner.current}):new c2(k);l.finished.then(()=>this.notifyFinished()).catch(ae),this.pendingTimeline&&(this.stopTimeline=l.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=l}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),aq=!0,as(),ar(),aq=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let dh=a=>null!==a,di={type:"spring",stiffness:500,damping:25,restSpeed:10},dj={type:"keyframes",duration:.8},dk={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},dl=(a,b,c,d={},e,f)=>h=>{let i=b9(d,a)||{},j=i.delay||d.delay||0,{elapsed:k=0}=d;k-=cf(j);let l={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...i,delay:-k,onUpdate:a=>{b.set(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{h(),i.onComplete&&i.onComplete()},name:a,motionValue:b,element:f?void 0:e};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(i)&&Object.assign(l,((a,{keyframes:b})=>b.length>2?dj:g.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:di:dk)(a,l)),l.duration&&(l.duration=cf(l.duration)),l.repeatDelay&&(l.repeatDelay=cf(l.repeatDelay)),void 0!==l.from&&(l.keyframes[0]=l.from);let m=!1;if(!1!==l.type&&(0!==l.duration||l.repeatDelay)||(cc(l),0===l.delay&&(m=!0)),(af.instantAnimations||af.skipAnimations)&&(m=!0,cc(l),l.delay=0),l.allowFlatten=!i.type&&!i.ease,m&&!f&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(dh),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(l.keyframes,i);if(void 0!==a)return void aj.update(()=>{l.onUpdate(a),l.onComplete()})}return i.isSync?new c2(l):new dg(l)};function dm(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:g,...h}=b;d&&(f=d);let i=[],j=e&&a.animationState&&a.animationState.getState()[e];for(let b in h){let d=a.getValue(b,a.latestValues[b]??null),e=h[b];if(void 0===e||j&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(j,b))continue;let g={delay:c,...b9(f||{},b)},k=d.get();if(void 0!==k&&!d.isAnimating&&!Array.isArray(e)&&e===k&&!g.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let c=a.props[b4];if(c){let a=window.MotionHandoffAnimation(c,b,aj);null!==a&&(g.startTime=a,l=!0)}}cb(a,b),d.start(dl(b,d,e,a.shouldReduceMotion&&J.has(b)?{type:!1}:g,a,l));let m=d.animation;m&&i.push(m)}return g&&Promise.all(i).then(()=>{aj.update(()=>{g&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=b8(a,b)||{};for(let b in e={...e,...c}){var f;let c=ca(f=e[b])?f[f.length-1]||0:f;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,a5(c))}}(a,g)})}),i}function dn(a,b,c={}){let d=b8(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let f=d?()=>Promise.all(dm(a,d,c)):()=>Promise.resolve(),g=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[],i=a.variantChildren.size,j=(i-1)*e,k="function"==typeof d,l=k?a=>d(a,i):1===f?(a=0)=>a*e:(a=0)=>j-a*e;return Array.from(a.variantChildren).sort(dp).forEach((a,e)=>{a.notify("AnimationStart",b),h.push(dn(a,b,{...g,delay:c+(k?0:d)+l(e)}).then(()=>a.notify("AnimationComplete",b)))}),Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:h}=e;if(!h)return Promise.all([f(),g(c.delay)]);{let[a,b]="beforeChildren"===h?[f,g]:[g,f];return a().then(()=>b())}}function dp(a,b){return a.sortNodePosition(b)}function dq(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}let dr=bl.length,ds=[...bk].reverse(),dt=bk.length;function du(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function dv(){return{animate:du(!0),whileInView:du(),whileHover:du(),whileTap:du(),whileDrag:du(),whileFocus:du(),exit:du()}}class dw{constructor(a){this.isMounted=!1,this.node=a}update(){}}class dx extends dw{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>dn(a,b,c)));else if("string"==typeof b)d=dn(a,b,c);else{let e="function"==typeof b?b8(a,b,c.custom):b;d=Promise.all(dm(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=dv(),d=!0,e=b=>(c,d)=>{let e=b8(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function f(f){let{props:g}=a,h=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<dr;a++){let d=bl[a],e=b.props[d];(bj(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},i=[],j=new Set,k={},l=1/0;for(let b=0;b<dt;b++){var m,n;let o=ds[b],p=c[o],q=void 0!==g[o]?g[o]:h[o],r=bj(q),s=o===f?p.isActive:null;!1===s&&(l=b);let t=q===h[o]&&q!==g[o]&&r;if(t&&d&&a.manuallyAnimateOnMount&&(t=!1),p.protectedKeys={...k},!p.isActive&&null===s||!q&&!p.prevProp||bi(q)||"boolean"==typeof q)continue;let u=(m=p.prevProp,"string"==typeof(n=q)?n!==m:!!Array.isArray(n)&&!dq(n,m)),v=u||o===f&&p.isActive&&!t&&r||b>l&&r,w=!1,x=Array.isArray(q)?q:[q],y=x.reduce(e(o),{});!1===s&&(y={});let{prevResolvedValues:z={}}=p,A={...z,...y},B=b=>{v=!0,j.has(b)&&(w=!0,j.delete(b)),p.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in A){let b=y[a],c=z[a];if(!k.hasOwnProperty(a))(ca(b)&&ca(c)?dq(b,c):b===c)?void 0!==b&&j.has(a)?B(a):p.protectedKeys[a]=!0:null!=b?B(a):j.add(a)}p.prevProp=q,p.prevResolvedValues=y,p.isActive&&(k={...k,...y}),d&&a.blockInitialAnimation&&(v=!1);let C=!(t&&u)||w;v&&C&&i.push(...x.map(a=>({animation:a,options:{type:o}})))}if(j.size){let b={};if("boolean"!=typeof g.initial){let c=b8(a,Array.isArray(g.initial)?g.initial[0]:g.initial);c&&c.transition&&(b.transition=c.transition)}j.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),i.push({animation:b})}let o=!!i.length;return d&&(!1===g.initial||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(o=!1),d=!1,o?b(i):Promise.resolve()}return{animateChanges:f,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=f(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=dv(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();bi(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let dy=0;class dz extends dw{constructor(){super(...arguments),this.id=dy++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}let dA={x:!1,y:!1};function dB(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}let dC=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary;function dD(a){return{point:{x:a.pageX,y:a.pageY}}}function dE(a,b,c,d){return dB(a,b,a=>dC(a)&&c(a,dD(a)),d)}function dF(a){return a.max-a.min}function dG(a,b,c,d=.5){a.origin=d,a.originPoint=x(b.min,b.max,a.origin),a.scale=dF(c)/dF(b),a.translate=x(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function dH(a,b,c,d){dG(a.x,b.x,c.x,d?d.originX:void 0),dG(a.y,b.y,c.y,d?d.originY:void 0)}function dI(a,b,c){a.min=c.min+b.min,a.max=a.min+dF(b)}function dJ(a,b,c){a.min=b.min-c.min,a.max=a.min+dF(b)}function dK(a,b,c){dJ(a.x,b.x,c.x),dJ(a.y,b.y,c.y)}function dL(a){return[a("x"),a("y")]}let dM=({current:a})=>a?a.ownerDocument.defaultView:null,dN=(a,b)=>Math.abs(a-b);class dO{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=dR(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(dN(a.x,b.x)**2+dN(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=al;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=dP(b,this.transformPagePoint),aj.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=dR("pointercancel"===a.type?this.lastMoveEventInfo:dP(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!dC(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=dP(dD(a),this.transformPagePoint),{point:h}=g,{timestamp:i}=al;this.history=[{...h,timestamp:i}];let{onSessionStart:j}=b;j&&j(a,dR(g,this.history)),this.removeListeners=ce(dE(this.contextWindow,"pointermove",this.handlePointerMove),dE(this.contextWindow,"pointerup",this.handlePointerUp),dE(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),ak(this.updatePoint)}}function dP(a,b){return b?{point:b(a.point)}:a}function dQ(a,b){return{x:a.x-b.x,y:a.y-b.y}}function dR({point:a},b){return{point:a,delta:dQ(a,dS(b)),offset:dQ(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=dS(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>cf(.1)));)c--;if(!d)return{x:0,y:0};let f=(e.timestamp-d.timestamp)/1e3;if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function dS(a){return a[a.length-1]}function dT(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function dU(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function dV(a,b,c){return{min:dW(a,b),max:dW(a,c)}}function dW(a,b){return"number"==typeof a?a:a[b]||0}let dX=new WeakMap;class dY{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=bd(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let e=a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(dD(a).point)},f=(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(dA[a])return null;else return dA[a]=!0,()=>{dA[a]=!1};return dA.x||dA.y?null:(dA.x=dA.y=!0,()=>{dA.x=dA.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),dL(a=>{let b=this.getAxisMotionValue(a).get()||0;if(Q.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=dF(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&aj.postRender(()=>e(a,b)),cb(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},g=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},h=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},i=()=>dL(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play()),{dragSnapToOrigin:j}=this.getProps();this.panSession=new dO(a,{onSessionStart:e,onStart:f,onMove:g,onSessionEnd:h,resumeAnimation:i},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:j,distanceThreshold:c,contextWindow:dM(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&aj.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!dZ(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?x(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?x(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&b3(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:dT(a.x,c,e),y:dT(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:dV(a,"left","right"),y:dV(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&dL(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!b3(b))return!1;let d=b.current;Z(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=I(a,c),{scroll:e}=b;return e&&(F(d.x,e.offset.x),F(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:dU(a.x,f.x),y:dU(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=w(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(dL(g=>{if(!dZ(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return cb(this.visualElement,a),c.start(dl(a,c,0,b,this.visualElement,!1))}stopAnimation(){dL(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){dL(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){dL(b=>{let{drag:c}=this.getProps();if(!dZ(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-x(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!b3(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};dL(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=dF(a),e=dF(b);return e>d?c=cW(b.min,b.max-d,a.min):d>e&&(c=cW(a.min,a.max-e,b.min)),K(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),dL(b=>{if(!dZ(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(x(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;dX.set(this.visualElement,this);let a=dE(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();b3(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),aj.read(b);let e=dB(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(dL(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function dZ(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}class d$ extends dw{constructor(a){super(a),this.removeGroupControls=ae,this.removeListeners=ae,this.controls=new dY(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ae}unmount(){this.removeGroupControls(),this.removeListeners()}}let d_=a=>(b,c)=>{a&&aj.postRender(()=>a(b,c))};class d0 extends dw{constructor(){super(...arguments),this.removePointerDownListener=ae}onPointerDown(a){this.session=new dO(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:dM(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:d_(a),onStart:d_(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&aj.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=dE(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var d1=c(86044);let d2={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function d3(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let d4={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!R.test(a))return a;else a=parseFloat(a);let c=d3(a,b.target.x),d=d3(a,b.target.y);return`${c}% ${d}%`}},d5=!1;class d6 extends e.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;for(let a in d8)by[a]=d8[a],s(a)&&(by[a].isCSSVariable=!0);e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),d5&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),d2.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,{projection:f}=c;return f&&(f.isPresent=e,d5=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==e?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||aj.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),a7.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function d7(a){let[b,c]=(0,d1.xQ)(),d=(0,e.useContext)(bN.L);return(0,bM.jsx)(d6,{...a,layoutGroup:d,switchLayoutGroup:(0,e.useContext)(b5),isPresent:b,safeToRemove:c})}let d8={borderRadius:{...d4,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d4,borderTopRightRadius:d4,borderBottomLeftRadius:d4,borderBottomRightRadius:d4,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=aN.parse(a);if(d.length>5)return a;let e=aN.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=x(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}};var d9=c(74479);function ea(a){return(0,d9.G)(a)&&"ownerSVGElement"in a}let eb=(a,b)=>a.depth-b.depth;class ec{constructor(){this.children=[],this.isDirty=!1}add(a){a0(this.children,a),this.isDirty=!0}remove(a){a1(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(eb),this.isDirty=!1,this.children.forEach(a)}}let ed=["TopLeft","TopRight","BottomLeft","BottomRight"],ee=ed.length,ef=a=>"string"==typeof a?parseFloat(a):a,eg=a=>"number"==typeof a||R.test(a);function eh(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let ei=ek(0,.5,cR),ej=ek(.5,.95,ae);function ek(a,b,c){return d=>d<a?0:d>b?1:c(cW(a,b,d))}function el(a,b){a.min=b.min,a.max=b.max}function em(a,b){el(a.x,b.x),el(a.y,b.y)}function en(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function eo(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function ep(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(Q.test(b)&&(b=parseFloat(b),b=x(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=x(f.min,f.max,d);a===f&&(h-=b),a.min=eo(a.min,b,c,h,e),a.max=eo(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let eq=["x","scaleX","originX"],er=["y","scaleY","originY"];function es(a,b,c,d){ep(a.x,b,eq,c?c.x:void 0,d?d.x:void 0),ep(a.y,b,er,c?c.y:void 0,d?d.y:void 0)}function et(a){return 0===a.translate&&1===a.scale}function eu(a){return et(a.x)&&et(a.y)}function ev(a,b){return a.min===b.min&&a.max===b.max}function ew(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function ex(a,b){return ew(a.x,b.x)&&ew(a.y,b.y)}function ey(a){return dF(a.x)/dF(a.y)}function ez(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class eA{constructor(){this.members=[]}add(a){a0(this.members,a),a.scheduleRender()}remove(a){if(a1(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let eB={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},eC=["","X","Y","Z"],eD=0;function eE(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function eF({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=b?.()){this.id=eD++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ah.value&&(eB.nodes=eB.calculatedTargetDeltas=eB.calculatedProjections=0),this.nodes.forEach(eI),this.nodes.forEach(eP),this.nodes.forEach(eQ),this.nodes.forEach(eJ),ah.addProjectionMetrics&&ah.addProjectionMetrics(eB)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new ec)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new a2),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=ea(b)&&!(ea(b)&&"svg"===b.tagName),this.instance=b;let{layoutId:c,layout:d,visualElement:e}=this.options;if(e&&!e.current&&e.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=0,e=()=>this.root.updateBlockedByResize=!1;aj.read(()=>{d=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==d&&(d=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=a_.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(ak(d),a(e-250))};return aj.setup(d,!0),()=>ak(d)}(e,250),d2.hasAnimatedSinceResize&&(d2.hasAnimatedSinceResize=!1,this.nodes.forEach(eO)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&e&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||e.getDefaultTransition()||eW,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=e.getProps(),i=!this.targetLayout||!ex(this.targetLayout,d),j=!b&&c;if(this.options.layoutRoot||this.resumeFrom||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...b9(f,"layout"),onPlay:g,onComplete:h};(e.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,j)}else b||eO(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ak(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eR),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[b4];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",aj,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eL);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(eM);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(eN),this.nodes.forEach(eG),this.nodes.forEach(eH)):this.nodes.forEach(eM),this.clearAllSnapshots();let a=a_.now();al.delta=K(0,1e3/60,a-al.timestamp),al.timestamp=a,al.isProcessing=!0,am.update.process(al),am.preRender.process(al),am.render.process(al),al.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,a7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eK),this.sharedNodes.forEach(eS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,aj.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){aj.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||dF(this.snapshot.measuredBox.x)||dF(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=bd(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!eu(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||A(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),eZ((b=d).x),eZ(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return bd();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(e_))){let{scroll:a}=this.root;a&&(F(b.x,a.offset.x),F(b.y,a.offset.y))}return b}removeElementScroll(a){let b=bd();if(em(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&em(b,a),F(b.x,e.offset.x),F(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=bd();em(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&H(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),A(d.latestValues)&&H(c,d.latestValues)}return A(this.latestValues)&&H(c,this.latestValues),c}removeTransform(a){let b=bd();em(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!A(c.latestValues))continue;z(c.latestValues)&&c.updateSnapshot();let d=bd();em(d,c.measurePageBox()),es(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return A(this.latestValues)&&es(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==al.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:d,layoutId:e}=this.options;if(this.layout&&(d||e)){if(this.resolvedRelativeTargetAt=al.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bd(),this.relativeTargetOrigin=bd(),dK(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),em(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=bd(),this.targetWithTransforms=bd()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var f,g,h;this.forceRelativeParentToResolveTarget(),f=this.target,g=this.relativeTarget,h=this.relativeParent.target,dI(f.x,g.x,h.x),dI(f.y,g.y,h.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):em(this.target,this.layout.layoutBox),E(this.target,this.targetDelta)):em(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bd(),this.relativeTargetOrigin=bd(),dK(this.relativeTargetOrigin,this.target,a.target),em(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ah.value&&eB.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||z(this.parent.latestValues)||B(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===al.timestamp&&(c=!1),c)return;let{layout:d,layoutId:e}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||e))return;em(this.layoutCorrected,this.layout.layoutBox);let f=this.treeScale.x,g=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&H(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,E(a,f)),d&&A(e.latestValues)&&H(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=bd());let{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(en(this.prevProjectionDelta.x,this.projectionDelta.x),en(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),dH(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===f&&this.treeScale.y===g&&ez(this.projectionDelta.x,this.prevProjectionDelta.x)&&ez(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),ah.value&&eB.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=bb(),this.projectionDelta=bb(),this.projectionDeltaWithTransform=bb()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=bb();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=bd(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(eV));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(eT(g.x,a.x,d),eT(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;dK(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,eU(n.x,o.x,p.x,q),eU(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,ev(j.x,m.x)&&ev(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=bd()),em(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=x(0,c.opacity??1,ei(d)),a.opacityExit=x(b.opacity??1,0,ej(d))):f&&(a.opacity=x(b.opacity??1,c.opacity??1,d));for(let e=0;e<ee;e++){let f=`border${ed[e]}Radius`,g=eh(b,f),h=eh(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||eg(g)===eg(h)?(a[f]=Math.max(x(ef(g),ef(h),d),0),(Q.test(h)||Q.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=x(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ak(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=aj.update(()=>{d2.hasAnimatedSinceResize=!0,cg.layout++,this.motionValue||(this.motionValue=a5(0)),this.currentAnimation=function(a,b,c){let d=aZ(a)?a:a5(a);return d.start(dl("",d,b,c)),d.animation}(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{cg.layout--},onComplete:()=>{cg.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&e$(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||bd();let b=dF(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=dF(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}em(b,c),H(b,e),dH(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new eA),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&eE("z",a,d,this.animationValues);for(let b=0;b<eC.length;b++)eE(`rotate${eC[b]}`,a,d,this.animationValues),eE(`skew${eC[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=b$(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=b$(b?.pointerEvents)||""),this.hasProjected&&!A(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,by){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=by[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?b$(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(eL),this.root.sharedNodes.clear()}}}function eG(a){a.updateLayout()}function eH(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?dL(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=dF(d);d.min=c[a].min,d.max=d.min+e}):e$(e,b.layoutBox,c)&&dL(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=dF(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=bb();dH(g,c,b.layoutBox);let h=bb();f?dH(h,a.applyTransform(d,!0),b.measuredBox):dH(h,c,b.layoutBox);let i=!eu(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=bd();dK(g,b.layoutBox,e.layoutBox);let h=bd();dK(h,c,f.layoutBox),ex(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function eI(a){ah.value&&eB.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function eJ(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function eK(a){a.clearSnapshot()}function eL(a){a.clearMeasurements()}function eM(a){a.isLayoutDirty=!1}function eN(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function eO(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function eP(a){a.resolveTargetDelta()}function eQ(a){a.calcProjection()}function eR(a){a.resetSkewAndRotation()}function eS(a){a.removeLeadSnapshot()}function eT(a,b,c){a.translate=x(b.translate,0,c),a.scale=x(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function eU(a,b,c,d){a.min=x(b.min,c.min,d),a.max=x(b.max,c.max,d)}function eV(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let eW={duration:.45,ease:[.4,0,.1,1]},eX=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),eY=eX("applewebkit/")&&!eX("chrome/")?Math.round:ae;function eZ(a){a.min=eY(a.min),a.max=eY(a.max)}function e$(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(ey(b)-ey(c)))}function e_(a){return a!==a.root&&a.scroll?.wasRoot}let e0=eF({attachResizeListener:(a,b)=>dB(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),e1={current:void 0},e2=eF({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!e1.current){let a=new e0({});a.mount(window),a.setOptions({layoutScroll:!0}),e1.current=a}return e1.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function e3(a,b){let c=function(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let b=document,c=(void 0)??b.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function e4(a){return!("touch"===a.pointerType||dA.x||dA.y)}function e5(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&aj.postRender(()=>e(b,dD(b)))}class e6 extends dw{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e3(a,c),g=a=>{if(!e4(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{e4(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,(a,b)=>(e5(this.node,b,"Start"),a=>e5(this.node,a,"End"))))}unmount(){}}class e7 extends dw{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ce(dB(this.node.current,"focus",()=>this.onFocus()),dB(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var e8=c(18171);let e9=(a,b)=>!!b&&(a===b||e9(a,b.parentElement)),fa=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),fb=new WeakSet;function fc(a){return b=>{"Enter"===b.key&&a(b)}}function fd(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function fe(a){return dC(a)&&!(dA.x||dA.y)}function ff(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&aj.postRender(()=>e(b,dD(b)))}class fg extends dw{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e3(a,c),g=a=>{let d=a.currentTarget;if(!fe(a))return;fb.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),fb.has(d)&&fb.delete(d),fe(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||e9(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{((c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),(0,e8.s)(a))&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=fc(()=>{if(fb.has(c))return;fd(c,"down");let a=fc(()=>{fd(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>fd(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),fa.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),f}(a,(a,b)=>(ff(this.node,b,"Start"),(a,{success:b})=>ff(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let fh=new WeakMap,fi=new WeakMap,fj=a=>{let b=fh.get(a.target);b&&b(a)},fk=a=>{a.forEach(fj)},fl={some:0,all:1};class fm extends dw{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:fl[d]},g=a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)};var h=this.node.current;let i=function({root:a,...b}){let c=a||document;fi.has(c)||fi.set(c,{});let d=fi.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(fk,{root:a,...b})),d[e]}(f);return fh.set(h,g),i.observe(h),()=>{fh.delete(h),i.unobserve(h)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}let fn=function(a,b){if("undefined"==typeof Proxy)return b7;let c=new Map,d=(c,d)=>b7(c,d,a,b);return new Proxy((a,b)=>d(a,b),{get:(e,f)=>"create"===f?d:(c.has(f)||c.set(f,b7(f,void 0,a,b)),c.get(f))})}({animation:{Feature:dx},exit:{Feature:dz},inView:{Feature:fm},tap:{Feature:fg},focus:{Feature:e7},hover:{Feature:e6},pan:{Feature:d0},drag:{Feature:d$,ProjectionNode:e2,MeasureLayout:d7},layout:{ProjectionNode:e2,MeasureLayout:d7}},(a,b)=>bL(a)?new bJ(b):new bB(b,{allowProjection:a!==e.Fragment}))},72789:(a,b,c)=>{c.d(b,{M:()=>e});var d=c(43210);function e(a){let b=(0,d.useRef)(null);return null===b.current&&(b.current=a()),b.current}},74479:(a,b,c)=>{c.d(b,{G:()=>d});function d(a){return"object"==typeof a&&null!==a}},86044:(a,b,c)=>{c.d(b,{xQ:()=>f});var d=c(43210),e=c(21279);function f(a=!0){let b=(0,d.useContext)(e.t);if(null===b)return[!0,null];let{isPresent:c,onExitComplete:g,register:h}=b,i=(0,d.useId)();(0,d.useEffect)(()=>{if(a)return h(i)},[a]);let j=(0,d.useCallback)(()=>a&&g&&g(i),[i,g,a]);return!c&&g?[!1,j]:[!0]}},88920:(a,b,c)=>{c.d(b,{N:()=>s});var d=c(60687),e=c(43210),f=c(12157),g=c(72789),h=c(15124),i=c(21279),j=c(18171),k=c(32582);class l extends e.Component{getSnapshotBeforeUpdate(a){let b=this.props.childRef.current;if(b&&a.isPresent&&!this.props.isPresent){let a=b.offsetParent,c=(0,j.s)(a)&&a.offsetWidth||0,d=this.props.sizeRef.current;d.height=b.offsetHeight||0,d.width=b.offsetWidth||0,d.top=b.offsetTop,d.left=b.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function m({children:a,isPresent:b,anchorX:c,root:f}){let g=(0,e.useId)(),h=(0,e.useRef)(null),i=(0,e.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:j}=(0,e.useContext)(k.Q);return(0,e.useInsertionEffect)(()=>{let{width:a,height:d,top:e,left:k,right:l}=i.current;if(b||!h.current||!a||!d)return;let m="left"===c?`left: ${k}`:`right: ${l}`;h.current.dataset.motionPopId=g;let n=document.createElement("style");j&&(n.nonce=j);let o=f??document.head;return o.appendChild(n),n.sheet&&n.sheet.insertRule(`
          [data-motion-pop-id="${g}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${d}px !important;
            ${m}px !important;
            top: ${e}px !important;
          }
        `),()=>{o.contains(n)&&o.removeChild(n)}},[b]),(0,d.jsx)(l,{isPresent:b,childRef:h,sizeRef:i,children:e.cloneElement(a,{ref:h})})}let n=({children:a,initial:b,isPresent:c,onExitComplete:f,custom:h,presenceAffectsLayout:j,mode:k,anchorX:l,root:n})=>{let p=(0,g.M)(o),q=(0,e.useId)(),r=!0,s=(0,e.useMemo)(()=>(r=!1,{id:q,initial:b,isPresent:c,custom:h,onExitComplete:a=>{for(let b of(p.set(a,!0),p.values()))if(!b)return;f&&f()},register:a=>(p.set(a,!1),()=>p.delete(a))}),[c,p,f]);return j&&r&&(s={...s}),(0,e.useMemo)(()=>{p.forEach((a,b)=>p.set(b,!1))},[c]),e.useEffect(()=>{c||p.size||!f||f()},[c]),"popLayout"===k&&(a=(0,d.jsx)(m,{isPresent:c,anchorX:l,root:n,children:a})),(0,d.jsx)(i.t.Provider,{value:s,children:a})};function o(){return new Map}var p=c(86044);let q=a=>a.key||"";function r(a){let b=[];return e.Children.forEach(a,a=>{(0,e.isValidElement)(a)&&b.push(a)}),b}let s=({children:a,custom:b,initial:c=!0,onExitComplete:i,presenceAffectsLayout:j=!0,mode:k="sync",propagate:l=!1,anchorX:m="left",root:o})=>{let[s,t]=(0,p.xQ)(l),u=(0,e.useMemo)(()=>r(a),[a]),v=l&&!s?[]:u.map(q),w=(0,e.useRef)(!0),x=(0,e.useRef)(u),y=(0,g.M)(()=>new Map),[z,A]=(0,e.useState)(u),[B,C]=(0,e.useState)(u);(0,h.E)(()=>{w.current=!1,x.current=u;for(let a=0;a<B.length;a++){let b=q(B[a]);v.includes(b)?y.delete(b):!0!==y.get(b)&&y.set(b,!1)}},[B,v.length,v.join("-")]);let D=[];if(u!==z){let a=[...u];for(let b=0;b<B.length;b++){let c=B[b],d=q(c);v.includes(d)||(a.splice(b,0,c),D.push(c))}return"wait"===k&&D.length&&(a=D),C(r(a)),A(u),null}let{forceRender:E}=(0,e.useContext)(f.L);return(0,d.jsx)(d.Fragment,{children:B.map(a=>{let e=q(a),f=(!l||!!s)&&(u===B||v.includes(e));return(0,d.jsx)(n,{isPresent:f,initial:(!w.current||!!c)&&void 0,custom:b,presenceAffectsLayout:j,mode:k,root:o,onExitComplete:f?void 0:()=>{if(!y.has(e))return;y.set(e,!0);let a=!0;y.forEach(b=>{b||(a=!1)}),a&&(E?.(),C(x.current),l&&t?.(),i&&i())},anchorX:m,children:a},e)})})}}};