{"version": 3, "sources": ["../../../src/server/async-storage/request-store.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { RenderOpts } from '../app-render/types'\nimport type { NextRequest } from '../web/spec-extension/request'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nimport { FLIGHT_HEADERS } from '../../client/components/app-router-headers'\nimport {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  MutableRequestCookiesAdapter,\n  RequestCookiesAdapter,\n  responseCookiesToRequestCookies,\n  wrapWithMutableAccessCheck,\n  type ReadonlyRequestCookies,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { ResponseCookies, RequestCookies } from '../web/spec-extension/cookies'\nimport { DraftModeProvider } from './draft-mode-provider'\nimport { splitCookiesString } from '../web/utils'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RenderResumeDataCache } from '../resume-data-cache/resume-data-cache'\nimport type { Params } from '../request/params'\nimport type { ImplicitTags } from '../lib/implicit-tags'\n\nfunction getHeaders(headers: Headers | IncomingHttpHeaders): ReadonlyHeaders {\n  const cleaned = HeadersAdapter.from(headers)\n  for (const header of FLIGHT_HEADERS) {\n    cleaned.delete(header.toLowerCase())\n  }\n\n  return HeadersAdapter.seal(cleaned)\n}\n\nfunction getMutableCookies(\n  headers: Headers | IncomingHttpHeaders,\n  onUpdateCookies?: (cookies: string[]) => void\n): ResponseCookies {\n  const cookies = new RequestCookies(HeadersAdapter.from(headers))\n  return MutableRequestCookiesAdapter.wrap(cookies, onUpdateCookies)\n}\n\nexport type WrapperRenderOpts = Partial<Pick<RenderOpts, 'onUpdateCookies'>> & {\n  previewProps?: __ApiPreviewProps\n}\n\ntype RequestContext = RequestResponsePair & {\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL. This is only undefined when generating static paths (ie,\n   * there is no request in progress, nor do we know one).\n   */\n  url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    search?: string\n  }\n  phase: RequestStore['phase']\n  renderOpts?: WrapperRenderOpts\n  isHmrRefresh?: boolean\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n  implicitTags: ImplicitTags\n}\n\ntype RequestResponsePair =\n  | { req: BaseNextRequest; res: BaseNextResponse } // for an app page\n  | { req: NextRequest; res: undefined } // in an api route or middleware\n\n/**\n * If middleware set cookies in this request (indicated by `x-middleware-set-cookie`),\n * then merge those into the existing cookie object, so that when `cookies()` is accessed\n * it's able to read the newly set cookies.\n */\nfunction mergeMiddlewareCookies(\n  req: RequestContext['req'],\n  existingCookies: RequestCookies | ResponseCookies\n) {\n  if (\n    'x-middleware-set-cookie' in req.headers &&\n    typeof req.headers['x-middleware-set-cookie'] === 'string'\n  ) {\n    const setCookieValue = req.headers['x-middleware-set-cookie']\n    const responseHeaders = new Headers()\n\n    for (const cookie of splitCookiesString(setCookieValue)) {\n      responseHeaders.append('set-cookie', cookie)\n    }\n\n    const responseCookies = new ResponseCookies(responseHeaders)\n\n    // Transfer cookies from ResponseCookies to RequestCookies\n    for (const cookie of responseCookies.getAll()) {\n      existingCookies.set(cookie)\n    }\n  }\n}\n\nexport function createRequestStoreForRender(\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache'],\n  renderResumeDataCache: RenderResumeDataCache | undefined\n): RequestStore {\n  return createRequestStoreImpl(\n    // Pages start in render phase by default\n    'render',\n    req,\n    res,\n    url,\n    rootParams,\n    implicitTags,\n    onUpdateCookies,\n    renderResumeDataCache,\n    previewProps,\n    isHmrRefresh,\n    serverComponentsHmrCache\n  )\n}\n\nexport function createRequestStoreForAPI(\n  req: RequestContext['req'],\n  url: RequestContext['url'],\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps']\n): RequestStore {\n  return createRequestStoreImpl(\n    // API routes start in action phase by default\n    'action',\n    req,\n    undefined,\n    url,\n    {},\n    implicitTags,\n    onUpdateCookies,\n    undefined,\n    previewProps,\n    false,\n    undefined\n  )\n}\n\nfunction createRequestStoreImpl(\n  phase: RequestStore['phase'],\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  renderResumeDataCache: RenderResumeDataCache | undefined,\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache']\n): RequestStore {\n  function defaultOnUpdateCookies(cookies: string[]) {\n    if (res) {\n      res.setHeader('Set-Cookie', cookies)\n    }\n  }\n\n  const cache: {\n    headers?: ReadonlyHeaders\n    cookies?: ReadonlyRequestCookies\n    mutableCookies?: ResponseCookies\n    userspaceMutableCookies?: ResponseCookies\n    draftMode?: DraftModeProvider\n  } = {}\n\n  return {\n    type: 'request',\n    phase,\n    implicitTags,\n    // Rather than just using the whole `url` here, we pull the parts we want\n    // to ensure we don't use parts of the URL that we shouldn't. This also\n    // lets us avoid requiring an empty string for `search` in the type.\n    url: { pathname: url.pathname, search: url.search ?? '' },\n    rootParams,\n    get headers() {\n      if (!cache.headers) {\n        // Seal the headers object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.headers = getHeaders(req.headers)\n      }\n\n      return cache.headers\n    },\n    get cookies() {\n      if (!cache.cookies) {\n        // if middleware is setting cookie(s), then include those in\n        // the initial cached cookies so they can be read in render\n        const requestCookies = new RequestCookies(\n          HeadersAdapter.from(req.headers)\n        )\n\n        mergeMiddlewareCookies(req, requestCookies)\n\n        // Seal the cookies object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.cookies = RequestCookiesAdapter.seal(requestCookies)\n      }\n\n      return cache.cookies\n    },\n    set cookies(value: ReadonlyRequestCookies) {\n      cache.cookies = value\n    },\n    get mutableCookies() {\n      if (!cache.mutableCookies) {\n        const mutableCookies = getMutableCookies(\n          req.headers,\n          onUpdateCookies || (res ? defaultOnUpdateCookies : undefined)\n        )\n\n        mergeMiddlewareCookies(req, mutableCookies)\n\n        cache.mutableCookies = mutableCookies\n      }\n      return cache.mutableCookies\n    },\n    get userspaceMutableCookies() {\n      if (!cache.userspaceMutableCookies) {\n        const userspaceMutableCookies = wrapWithMutableAccessCheck(\n          this.mutableCookies\n        )\n        cache.userspaceMutableCookies = userspaceMutableCookies\n      }\n      return cache.userspaceMutableCookies\n    },\n    get draftMode() {\n      if (!cache.draftMode) {\n        cache.draftMode = new DraftModeProvider(\n          previewProps,\n          req,\n          this.cookies,\n          this.mutableCookies\n        )\n      }\n\n      return cache.draftMode\n    },\n    renderResumeDataCache: renderResumeDataCache ?? null,\n    isHmrRefresh,\n    serverComponentsHmrCache:\n      serverComponentsHmrCache ||\n      (globalThis as any).__serverComponentsHmrCache,\n  }\n}\n\nexport function synchronizeMutableCookies(store: RequestStore) {\n  // TODO: does this need to update headers as well?\n  store.cookies = RequestCookiesAdapter.seal(\n    responseCookiesToRequestCookies(store.mutableCookies)\n  )\n}\n"], "names": ["createRequestStoreForAPI", "createRequestStoreForRender", "synchronizeMutableCookies", "getHeaders", "headers", "cleaned", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "header", "FLIGHT_HEADERS", "delete", "toLowerCase", "seal", "getMutableCookies", "onUpdateCookies", "cookies", "RequestCookies", "MutableRequestCookiesAdapter", "wrap", "mergeMiddlewareCookies", "req", "existingCookies", "setCookieValue", "responseHeaders", "Headers", "cookie", "splitCookiesString", "append", "responseCookies", "ResponseCookies", "getAll", "set", "res", "url", "rootParams", "implicitTags", "previewProps", "isHmrRefresh", "serverComponentsHmrCache", "renderResumeDataCache", "createRequestStoreImpl", "undefined", "phase", "defaultOnUpdateCookies", "<PERSON><PERSON><PERSON><PERSON>", "cache", "type", "pathname", "search", "requestCookies", "RequestCookiesAdapter", "value", "mutableCookies", "userspaceMutableCookies", "wrapWithMutableAccessCheck", "draftMode", "DraftModeProvider", "globalThis", "__serverComponentsHmrCache", "store", "responseCookiesToRequestCookies"], "mappings": ";;;;;;;;;;;;;;;;IAsIgBA,wBAAwB;eAAxBA;;IA5BAC,2BAA2B;eAA3BA;;IA8JAC,yBAAyB;eAAzBA;;;kCAjQe;yBAIxB;gCAOA;yBACyC;mCACd;uBACC;AAMnC,SAASC,WAAWC,OAAsC;IACxD,MAAMC,UAAUC,uBAAc,CAACC,IAAI,CAACH;IACpC,KAAK,MAAMI,UAAUC,gCAAc,CAAE;QACnCJ,QAAQK,MAAM,CAACF,OAAOG,WAAW;IACnC;IAEA,OAAOL,uBAAc,CAACM,IAAI,CAACP;AAC7B;AAEA,SAASQ,kBACPT,OAAsC,EACtCU,eAA6C;IAE7C,MAAMC,UAAU,IAAIC,uBAAc,CAACV,uBAAc,CAACC,IAAI,CAACH;IACvD,OAAOa,4CAA4B,CAACC,IAAI,CAACH,SAASD;AACpD;AAmCA;;;;CAIC,GACD,SAASK,uBACPC,GAA0B,EAC1BC,eAAiD;IAEjD,IACE,6BAA6BD,IAAIhB,OAAO,IACxC,OAAOgB,IAAIhB,OAAO,CAAC,0BAA0B,KAAK,UAClD;QACA,MAAMkB,iBAAiBF,IAAIhB,OAAO,CAAC,0BAA0B;QAC7D,MAAMmB,kBAAkB,IAAIC;QAE5B,KAAK,MAAMC,UAAUC,IAAAA,yBAAkB,EAACJ,gBAAiB;YACvDC,gBAAgBI,MAAM,CAAC,cAAcF;QACvC;QAEA,MAAMG,kBAAkB,IAAIC,wBAAe,CAACN;QAE5C,0DAA0D;QAC1D,KAAK,MAAME,UAAUG,gBAAgBE,MAAM,GAAI;YAC7CT,gBAAgBU,GAAG,CAACN;QACtB;IACF;AACF;AAEO,SAASxB,4BACdmB,GAA0B,EAC1BY,GAA0B,EAC1BC,GAA0B,EAC1BC,UAAkB,EAClBC,YAA4C,EAC5CrB,eAA8C,EAC9CsB,YAA+C,EAC/CC,YAA4C,EAC5CC,wBAAoE,EACpEC,qBAAwD;IAExD,OAAOC,uBACL,yCAAyC;IACzC,UACApB,KACAY,KACAC,KACAC,YACAC,cACArB,iBACAyB,uBACAH,cACAC,cACAC;AAEJ;AAEO,SAAStC,yBACdoB,GAA0B,EAC1Ba,GAA0B,EAC1BE,YAA4C,EAC5CrB,eAA8C,EAC9CsB,YAA+C;IAE/C,OAAOI,uBACL,8CAA8C;IAC9C,UACApB,KACAqB,WACAR,KACA,CAAC,GACDE,cACArB,iBACA2B,WACAL,cACA,OACAK;AAEJ;AAEA,SAASD,uBACPE,KAA4B,EAC5BtB,GAA0B,EAC1BY,GAA0B,EAC1BC,GAA0B,EAC1BC,UAAkB,EAClBC,YAA4C,EAC5CrB,eAA8C,EAC9CyB,qBAAwD,EACxDH,YAA+C,EAC/CC,YAA4C,EAC5CC,wBAAoE;IAEpE,SAASK,uBAAuB5B,OAAiB;QAC/C,IAAIiB,KAAK;YACPA,IAAIY,SAAS,CAAC,cAAc7B;QAC9B;IACF;IAEA,MAAM8B,QAMF,CAAC;IAEL,OAAO;QACLC,MAAM;QACNJ;QACAP;QACA,yEAAyE;QACzE,uEAAuE;QACvE,oEAAoE;QACpEF,KAAK;YAAEc,UAAUd,IAAIc,QAAQ;YAAEC,QAAQf,IAAIe,MAAM,IAAI;QAAG;QACxDd;QACA,IAAI9B,WAAU;YACZ,IAAI,CAACyC,MAAMzC,OAAO,EAAE;gBAClB,oEAAoE;gBACpE,8BAA8B;gBAC9ByC,MAAMzC,OAAO,GAAGD,WAAWiB,IAAIhB,OAAO;YACxC;YAEA,OAAOyC,MAAMzC,OAAO;QACtB;QACA,IAAIW,WAAU;YACZ,IAAI,CAAC8B,MAAM9B,OAAO,EAAE;gBAClB,4DAA4D;gBAC5D,2DAA2D;gBAC3D,MAAMkC,iBAAiB,IAAIjC,uBAAc,CACvCV,uBAAc,CAACC,IAAI,CAACa,IAAIhB,OAAO;gBAGjCe,uBAAuBC,KAAK6B;gBAE5B,oEAAoE;gBACpE,8BAA8B;gBAC9BJ,MAAM9B,OAAO,GAAGmC,qCAAqB,CAACtC,IAAI,CAACqC;YAC7C;YAEA,OAAOJ,MAAM9B,OAAO;QACtB;QACA,IAAIA,SAAQoC,MAA+B;YACzCN,MAAM9B,OAAO,GAAGoC;QAClB;QACA,IAAIC,kBAAiB;YACnB,IAAI,CAACP,MAAMO,cAAc,EAAE;gBACzB,MAAMA,iBAAiBvC,kBACrBO,IAAIhB,OAAO,EACXU,mBAAoBkB,CAAAA,MAAMW,yBAAyBF,SAAQ;gBAG7DtB,uBAAuBC,KAAKgC;gBAE5BP,MAAMO,cAAc,GAAGA;YACzB;YACA,OAAOP,MAAMO,cAAc;QAC7B;QACA,IAAIC,2BAA0B;YAC5B,IAAI,CAACR,MAAMQ,uBAAuB,EAAE;gBAClC,MAAMA,0BAA0BC,IAAAA,0CAA0B,EACxD,IAAI,CAACF,cAAc;gBAErBP,MAAMQ,uBAAuB,GAAGA;YAClC;YACA,OAAOR,MAAMQ,uBAAuB;QACtC;QACA,IAAIE,aAAY;YACd,IAAI,CAACV,MAAMU,SAAS,EAAE;gBACpBV,MAAMU,SAAS,GAAG,IAAIC,oCAAiB,CACrCpB,cACAhB,KACA,IAAI,CAACL,OAAO,EACZ,IAAI,CAACqC,cAAc;YAEvB;YAEA,OAAOP,MAAMU,SAAS;QACxB;QACAhB,uBAAuBA,yBAAyB;QAChDF;QACAC,0BACEA,4BACA,AAACmB,WAAmBC,0BAA0B;IAClD;AACF;AAEO,SAASxD,0BAA0ByD,KAAmB;IAC3D,kDAAkD;IAClDA,MAAM5C,OAAO,GAAGmC,qCAAqB,CAACtC,IAAI,CACxCgD,IAAAA,+CAA+B,EAACD,MAAMP,cAAc;AAExD", "ignoreList": [0]}