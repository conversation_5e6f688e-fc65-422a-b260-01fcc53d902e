1:"$Sreact.fragment"
2:I[9413,["302","static/chunks/302-c203ae1d098564d4.js","926","static/chunks/926-e4de4b54c07c49e8.js","661","static/chunks/661-3ee66207c661ef44.js","585","static/chunks/585-c8bf55fa4b914e65.js","177","static/chunks/app/layout-f9bb8a9ced3018a4.js"],"default"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[2261,["302","static/chunks/302-c203ae1d098564d4.js","926","static/chunks/926-e4de4b54c07c49e8.js","661","static/chunks/661-3ee66207c661ef44.js","585","static/chunks/585-c8bf55fa4b914e65.js","305","static/chunks/app/(dashboard)/layout-d3968e78be335965.js"],"SidebarProvider"]
6:I[6839,["302","static/chunks/302-c203ae1d098564d4.js","926","static/chunks/926-e4de4b54c07c49e8.js","661","static/chunks/661-3ee66207c661ef44.js","585","static/chunks/585-c8bf55fa4b914e65.js","305","static/chunks/app/(dashboard)/layout-d3968e78be335965.js"],"default"]
7:I[1831,["302","static/chunks/302-c203ae1d098564d4.js","926","static/chunks/926-e4de4b54c07c49e8.js","661","static/chunks/661-3ee66207c661ef44.js","585","static/chunks/585-c8bf55fa4b914e65.js","305","static/chunks/app/(dashboard)/layout-d3968e78be335965.js"],"default"]
8:I[894,[],"ClientPageRoot"]
9:I[7195,["302","static/chunks/302-c203ae1d098564d4.js","36","static/chunks/36-c864246f2b552ae7.js","300","static/chunks/app/(dashboard)/profile/page-86d956b2910b775b.js"],"default"]
c:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[9665,[],"ViewportBoundary"]
12:I[9665,[],"MetadataBoundary"]
13:"$Sreact.suspense"
15:I[8393,[],""]
:HL["/_next/static/css/e2a0a94af7378cb9.css","style"]
0:{"P":null,"b":"qaZ_EjVGVjzNUm49HIkxv","p":"","c":["","profile"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["profile",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e2a0a94af7378cb9.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"pt-BR","className":"__variable_e8ce0c __variable_694534","children":["$","body",null,{"className":"__className_e8ce0c antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","$L5",null,{"children":["$","div",null,{"className":"flex h-screen","children":[["$","$L6",null,{}],["$","div",null,{"className":"flex-1 flex flex-col","children":[["$","$L7",null,{}],["$","main",null,{"className":"flex-1 overflow-auto","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]}]]}],{"children":["profile",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L8",null,{"Component":"$9","searchParams":{},"params":{},"promises":["$@a","$@b"]}],null,["$","$Lc",null,{"children":["$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$L10",null,{"children":"$L11"}],null],["$","$L12",null,{"children":["$","div",null,{"hidden":true,"children":["$","$13",null,{"fallback":null,"children":"$L14"}]}]}]]}],false]],"m":"$undefined","G":["$15",[]],"s":false,"S":true}
a:{}
b:"$0:f:0:1:2:children:2:children:2:children:1:props:children:0:props:params"
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
d:null
16:I[8175,[],"IconMark"]
f:{"metadata":[["$","title","0",{"children":"Frontend - Projeto Profit Growth"}],["$","meta","1",{"name":"description","content":"Frontend do projeto Profit Growth"}],["$","link","2",{"rel":"shortcut icon","href":"/icon-logo.svg"}],["$","link","3",{"rel":"icon","href":"/icon-logo.svg","type":"image/svg+xml"}],["$","link","4",{"rel":"apple-touch-icon","href":"/icon-logo.svg"}],["$","$L16","5",{}]],"error":null,"digest":"$undefined"}
14:"$f:metadata"
