exports.id=794,exports.ids=[794],exports.modules={4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},6880:(a,b,c)=>{"use strict";c.d(b,{H:()=>i,x:()=>j});var d=c(60687),e=c(43210),f=c(16391),g=c(87979);let h=(0,e.createContext)(void 0),i=({children:a})=>{let{user:b}=(0,g.A)(),[c,i]=(0,e.useState)(null),[j,k]=(0,e.useState)(!0),[l,m]=(0,e.useState)(null),[n,o]=(0,e.useState)(!1),p=(0,e.useCallback)(async(a=!1)=>{if(!b){i(null),k(!1),o(!1);return}if(!a&&c&&n)return void k(!1);try{k(!0),m(null);let{data:a,error:c}=await f.N.from("profiles").select("*").eq("id",b.id).single();if(c&&"PGRST116"!==c.code)throw c;if(a)i(a);else{let a={id:b.id,email:b.email||"",full_name:b.user_metadata?.full_name||"",avatar_url:b.user_metadata?.avatar_url||"",created_at:b.created_at},{data:c,error:d}=await f.N.from("profiles").insert([{...a,updated_at:new Date().toISOString()}]).select().single();if(d)throw d;i(c)}o(!0)}catch(a){console.error("Erro ao carregar perfil:",a),m("Erro ao carregar perfil do usu\xe1rio"),o(!1)}finally{k(!1)}},[b,c,n]),q=async()=>{await p(!0)},r=async a=>{if(!b||!c)throw Error("Usu\xe1rio n\xe3o autenticado ou perfil n\xe3o carregado");try{m(null);let c={...a,updated_at:new Date().toISOString()},{data:d,error:e}=await f.N.from("profiles").update(c).eq("id",b.id).select().single();if(e)throw e;return i(d),{success:!0,data:d}}catch(b){console.error("Erro ao atualizar perfil:",b);let a="Erro ao salvar altera\xe7\xf5es";return m(a),{success:!1,error:a}}},s=async a=>{if(!b)throw Error("Usu\xe1rio n\xe3o autenticado");try{if(m(null),!a.type.startsWith("image/"))throw Error("Por favor, selecione apenas arquivos de imagem");if(a.size>5242880)throw Error("A imagem deve ter no m\xe1ximo 5MB");let d=a.name.split(".").pop(),e=`${b.id}-${Date.now()}.${d}`,g=`avatars/${e}`;if(c?.avatar_url){let a=c.avatar_url.split("/").pop();a&&await f.N.storage.from("avatars").remove([`avatars/${a}`])}let{error:h}=await f.N.storage.from("avatars").upload(g,a);if(h)throw h;let{data:{publicUrl:i}}=f.N.storage.from("avatars").getPublicUrl(g),j=await r({avatar_url:i});if(j.success)return{success:!0,url:i};throw Error(j.error)}catch(b){console.error("Erro ao fazer upload do avatar:",b);let a=b instanceof Error?b.message:"Erro ao fazer upload da imagem";return m(a),{success:!1,error:a}}},t=async()=>{if(b&&c?.avatar_url)try{m(null);let a=c.avatar_url.split("/").pop();return a&&await f.N.storage.from("avatars").remove([`avatars/${a}`]),await r({avatar_url:""})}catch(b){console.error("Erro ao remover avatar:",b);let a="Erro ao remover avatar";return m(a),{success:!1,error:a}}};return(0,e.useEffect)(()=>{b?(o(!1),p()):(i(null),k(!1),o(!1))},[b?.id,b,p]),(0,d.jsx)(h.Provider,{value:{profile:c,isLoading:j,error:l,updateProfile:r,uploadAvatar:s,removeAvatar:t,clearError:()=>m(null),refreshProfile:q},children:a})},j=()=>{let a=(0,e.useContext)(h);if(void 0===a)throw Error("useProfile deve ser usado dentro de um ProfileProvider");return a}},16391:(a,b,c)=>{"use strict";c.d(b,{N:()=>d});let d=(0,c(60463).UU)("https://dpofnwutgpbwylwtbgnv.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.lE40lk7gBuq77mqJUezZYxUmHSeRSC6kOTVjwvP2hLw")},18890:(a,b,c)=>{"use strict";c.d(b,{default:()=>u});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(30474),i=c(50549),j=c(20158),k=c(4780);function l({delayDuration:a=0,...b}){return(0,d.jsx)(j.Kq,{"data-slot":"tooltip-provider",delayDuration:a,...b})}function m({...a}){return(0,d.jsx)(l,{children:(0,d.jsx)(j.bL,{"data-slot":"tooltip",...a})})}function n({...a}){return(0,d.jsx)(j.l9,{"data-slot":"tooltip-trigger",...a})}function o({className:a,sideOffset:b=4,showArrow:c=!1,children:e,...f}){return(0,d.jsx)(j.ZL,{children:(0,d.jsxs)(j.UC,{"data-slot":"tooltip-content",sideOffset:b,className:(0,k.cn)("bg-popover text-popover-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-w-70 rounded-md border px-3 py-1.5 text-sm",a),...f,children:[e,c&&(0,d.jsx)(j.i3,{className:"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]"})]})})}let p=()=>(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})}),q=()=>(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"})}),r=()=>(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"})}),s=()=>(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})}),t=()=>(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"})}),u=()=>{let[a,b]=(0,e.useState)(null),{isCollapsed:c,toggleSidebar:f}=(0,i.c)(),j=(0,e.useCallback)(a=>{b(b=>b===a?null:a)},[]);return(0,d.jsx)(l,{delayDuration:0,children:(0,d.jsxs)("div",{className:`${c?"w-16":"w-[270px]"} h-full bg-card border-r border-border flex flex-col transition-all duration-300 ease-in-out`,children:[(0,d.jsx)("div",{className:"p-4 border-b border-border",children:(0,d.jsx)("div",{className:"flex items-center justify-between",children:c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)("button",{onClick:f,className:"flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors",children:(0,d.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 4l8 8-8 8"})})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Expandir sidebar"})]}):(0,d.jsx)("button",{onClick:f,className:"flex items-center justify-center w-8 h-8 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors",children:(0,d.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 4l-8 8 8 8"})})})})}),(0,d.jsx)("div",{className:"p-4 border-b border-border",children:(0,d.jsx)(g(),{href:"/dashboard",className:"flex items-center gap-3",children:(0,d.jsx)(h.default,{src:c?"/icon-logo.svg":"/logo.svg",alt:"Profit Growth",width:32,height:32,className:"h-8 w-auto"})})}),(0,d.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,d.jsxs)("div",{className:"p-4",children:[!c&&(0,d.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"PRINCIPAL"}),(0,d.jsxs)("nav",{className:"space-y-1",children:[c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)(g(),{href:"/",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg bg-primary text-primary-foreground",children:(0,d.jsx)(p,{})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"In\xedcio"})]}):(0,d.jsxs)(g(),{href:"/",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg bg-primary text-primary-foreground",children:[(0,d.jsx)(p,{}),"In\xedcio",(0,d.jsx)("span",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full"})]}),c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)(g(),{href:"/dashboard",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,d.jsx)(q,{})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Dashboard"})]}):(0,d.jsxs)(g(),{href:"/dashboard",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,d.jsx)(q,{}),"Dashboard"]}),c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)(g(),{href:"/analytics",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,d.jsx)(r,{})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Analytics"})]}):(0,d.jsxs)(g(),{href:"/analytics",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,d.jsx)(r,{}),"Analytics"]})]})]}),(0,d.jsxs)("div",{className:"p-4",children:[!c&&(0,d.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"APLICA\xc7\xd5ES"}),(0,d.jsxs)("nav",{className:"space-y-1",children:[c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)(g(),{href:"/chat",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,d.jsx)(s,{})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Chat"})]}):(0,d.jsxs)(g(),{href:"/chat",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,d.jsx)(s,{}),"Chat"]}),c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)(g(),{href:"/calendar",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,d.jsx)(t,{})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Calend\xe1rio"})]}):(0,d.jsxs)(g(),{href:"/calendar",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,d.jsx)(t,{}),"Calend\xe1rio"]})]})]}),(0,d.jsxs)("div",{className:"p-4",children:[!c&&(0,d.jsx)("h3",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"OUTROS"}),(0,d.jsxs)("nav",{className:"space-y-1",children:[c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)("button",{className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full",children:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Menu Multin\xedvel"})]}):(0,d.jsxs)("div",{children:[(0,d.jsxs)("button",{onClick:()=>j("multilevel"),className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent w-full text-left",children:[(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})}),"Menu Multin\xedvel",(0,d.jsx)("svg",{className:`w-4 h-4 ml-auto transition-transform ${"multilevel"===a?"rotate-90":""}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]}),"multilevel"===a&&(0,d.jsxs)("div",{className:"ml-6 mt-1 space-y-1",children:[(0,d.jsx)(g(),{href:"/posts",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:"Posts"}),(0,d.jsx)(g(),{href:"/details",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:"Detalhes"})]})]}),c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)(g(),{href:"/sobre",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Sobre"})]}):(0,d.jsxs)(g(),{href:"/sobre",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),"Sobre"]}),c?(0,d.jsxs)(m,{children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center px-2 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})})}),(0,d.jsx)(o,{side:"right",className:"px-2 py-1 text-xs",children:"Link Externo"})]}):(0,d.jsxs)("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-3 px-3 py-2 text-sm rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent",children:[(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Link Externo",(0,d.jsx)("svg",{className:"w-4 h-4 ml-auto",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})]})]})]})})}},22289:(a,b,c)=>{"use strict";c.d(b,{default:()=>m});var d=c(60687),e=c(16189),f=c(50549),g=c(6880),h=c(18890),i=c(27889),j=c(34570);let k=["/login","/"],l=["/dashboard","/analytics","/chat","/calendar","/profile","/perfil"];function m({children:a}){let b=(0,e.usePathname)(),c=k.includes(b),m=l.some(a=>b.startsWith(a));return c?(0,d.jsx)(d.Fragment,{children:a}):m?(0,d.jsx)(j.A,{children:(0,d.jsx)(g.H,{children:(0,d.jsx)(f.SidebarProvider,{children:(0,d.jsxs)("div",{className:"h-screen flex bg-background",children:[(0,d.jsx)(h.default,{}),(0,d.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,d.jsx)(i.default,{}),(0,d.jsx)("main",{className:"flex-1 overflow-y-auto",children:a})]})]})})})}):(0,d.jsx)(g.H,{children:(0,d.jsx)(f.SidebarProvider,{children:(0,d.jsxs)("div",{className:"h-screen flex bg-background",children:[(0,d.jsx)(h.default,{}),(0,d.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,d.jsx)(i.default,{}),(0,d.jsx)("main",{className:"flex-1 overflow-y-auto",children:a})]})]})})})}},27889:(a,b,c)=>{"use strict";c.d(b,{default:()=>ac});var d=c(60687),e=c(96882),f=c(8403),g=c(32940),h=c(23512),i=c(43210),j=c(24224),k=c(8730),l=c(4780);let m=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function n({className:a,variant:b,size:c,asChild:e=!1,...f}){let g=e?k.bL:"button";return(0,d.jsx)(g,{"data-slot":"button",className:(0,l.cn)(m({variant:b,size:c,className:a})),...f})}var o=c(92930);function p({...a}){return(0,d.jsx)(o.bL,{"data-slot":"dropdown-menu",...a})}function q({...a}){return(0,d.jsx)(o.l9,{"data-slot":"dropdown-menu-trigger",...a})}function r({className:a,sideOffset:b=4,onPointerDown:c,onPointerDownOutside:e,onCloseAutoFocus:f,...g}){let h=i.useRef(!1),j=i.useCallback(a=>{h.current=!0,c?.(a)},[c]),k=i.useCallback(a=>{h.current=!0,e?.(a)},[e]),m=i.useCallback(a=>{if(f)return f(a);h.current&&(a.preventDefault(),h.current=!1)},[f]);return(0,d.jsx)(o.ZL,{children:(0,d.jsx)(o.UC,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-40 overflow-hidden rounded-md border p-1 shadow-lg",a),onPointerDown:j,onPointerDownOutside:k,onCloseAutoFocus:m,...g})})}function s({...a}){return(0,d.jsx)(o.YJ,{"data-slot":"dropdown-menu-group",...a})}function t({className:a,inset:b,variant:c="default",...e}){return(0,d.jsx)(o.q7,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0",a),...e})}function u({className:a,inset:b,...c}){return(0,d.jsx)(o.JU,{"data-slot":"dropdown-menu-label","data-inset":b,className:(0,l.cn)("text-muted-foreground px-2 py-1.5 text-xs font-medium data-[inset]:pl-8",a),...c})}function v({className:a,...b}){return(0,d.jsx)(o.wv,{"data-slot":"dropdown-menu-separator",className:(0,l.cn)("bg-border -mx-1 my-1 h-px",a),...b})}function w(){return(0,d.jsxs)(p,{children:[(0,d.jsx)(q,{asChild:!0,children:(0,d.jsx)(n,{size:"icon",variant:"ghost",className:"size-8 rounded-full shadow-none","aria-label":"Open edit menu",children:(0,d.jsx)(e.A,{className:"text-muted-foreground",size:16,"aria-hidden":"true"})})}),(0,d.jsxs)(r,{className:"pb-2",children:[(0,d.jsx)(u,{children:"Precisa de ajuda?"}),(0,d.jsx)(t,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,d.jsxs)("a",{href:"https://docs.supabase.com",target:"_blank",rel:"noopener noreferrer",children:[(0,d.jsx)(f.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Documenta\xe7\xe3o"]})}),(0,d.jsx)(t,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,d.jsxs)("a",{href:"#",onClick:()=>alert("Em breve!"),children:[(0,d.jsx)(g.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Suporte"]})}),(0,d.jsx)(t,{className:"cursor-pointer py-1 focus:bg-transparent focus:underline",asChild:!0,children:(0,d.jsxs)("a",{href:"#",onClick:()=>alert("Em breve!"),children:[(0,d.jsx)(h.A,{size:16,className:"opacity-60","aria-hidden":"true"}),"Contato"]})})]})]})}var x=c(97051),y=c(40599);function z({...a}){return(0,d.jsx)(y.bL,{"data-slot":"popover",...a})}function A({...a}){return(0,d.jsx)(y.l9,{"data-slot":"popover-trigger",...a})}function B({className:a,align:b="center",sideOffset:c=4,showArrow:e=!1,...f}){return(0,d.jsx)(y.ZL,{children:(0,d.jsxs)(y.UC,{"data-slot":"popover-content",align:b,sideOffset:c,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 rounded-md border p-4 shadow-md outline-hidden",a),...f,children:[f.children,e&&(0,d.jsx)(y.i3,{className:"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]"})]})})}let C=[{id:1,user:"Carlos Silva",action:"solicitou revis\xe3o em",target:"PR #42: Implementa\xe7\xe3o de funcionalidade",timestamp:"h\xe1 15 minutos",unread:!0},{id:2,user:"Ana Santos",action:"compartilhou",target:"Nova biblioteca de componentes",timestamp:"h\xe1 45 minutos",unread:!0},{id:3,user:"Jo\xe3o Oliveira",action:"atribuiu voc\xea para",target:"Tarefa de integra\xe7\xe3o da API",timestamp:"h\xe1 4 horas",unread:!1},{id:4,user:"Maria Costa",action:"respondeu ao seu coment\xe1rio em",target:"Fluxo de autentica\xe7\xe3o",timestamp:"h\xe1 12 horas",unread:!1},{id:5,user:"Pedro Lima",action:"comentou em",target:"Redesign do dashboard",timestamp:"h\xe1 2 dias",unread:!1},{id:6,user:"Lucia Ferreira",action:"mencionou voc\xea em",target:"Imagem do projeto Supabase",timestamp:"h\xe1 2 semanas",unread:!1}];function D({className:a}){return(0,d.jsx)("svg",{width:"6",height:"6",fill:"currentColor",viewBox:"0 0 6 6",xmlns:"http://www.w3.org/2000/svg",className:a,"aria-hidden":"true",children:(0,d.jsx)("circle",{cx:"3",cy:"3",r:"3"})})}function E(){let[a,b]=(0,i.useState)(C),c=a.filter(a=>a.unread).length;return(0,d.jsxs)(z,{children:[(0,d.jsx)(A,{asChild:!0,children:(0,d.jsxs)(n,{size:"icon",variant:"ghost",className:"text-muted-foreground relative size-8 rounded-full shadow-none","aria-label":"Open notifications",children:[(0,d.jsx)(x.A,{size:16,"aria-hidden":"true"}),c>0&&(0,d.jsx)("div",{"aria-hidden":"true",className:"bg-primary absolute top-0.5 right-0.5 size-1 rounded-full"})]})}),(0,d.jsxs)(B,{className:"w-80 p-1",children:[(0,d.jsxs)("div",{className:"flex items-baseline justify-between gap-4 px-3 py-2",children:[(0,d.jsx)("div",{className:"text-sm font-semibold",children:"Notifica\xe7\xf5es"}),c>0&&(0,d.jsx)("button",{className:"text-xs font-medium hover:underline",onClick:()=>{b(a.map(a=>({...a,unread:!1})))},children:"Marcar todas como lidas"})]}),(0,d.jsx)("div",{role:"separator","aria-orientation":"horizontal",className:"bg-border -mx-1 my-1 h-px"}),a.map(c=>(0,d.jsx)("div",{className:"hover:bg-accent rounded-md px-3 py-2 text-sm transition-colors",children:(0,d.jsxs)("div",{className:"relative flex items-start pe-3",children:[(0,d.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,d.jsxs)("button",{className:"text-foreground/80 text-left after:absolute after:inset-0",onClick:()=>{var d;return d=c.id,void b(a.map(a=>a.id===d?{...a,unread:!1}:a))},children:[(0,d.jsx)("span",{className:"text-foreground font-medium hover:underline",children:c.user})," ",c.action," ",(0,d.jsx)("span",{className:"text-foreground font-medium hover:underline",children:c.target}),"."]}),(0,d.jsx)("div",{className:"text-muted-foreground text-xs",children:c.timestamp})]}),c.unread&&(0,d.jsxs)("div",{className:"absolute end-0 self-center",children:[(0,d.jsx)("span",{className:"sr-only",children:"Unread"}),(0,d.jsx)(D,{})]})]})},c.id))]})]})}var F=c(58869),G=c(84027),H=c(895),I=c(27407),J=c(82080),K=c(40083),L=c(16189),M=c(87979),N=c(54348),O=c(11096);function P({className:a,...b}){return(0,d.jsx)(O.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...b})}function Q({className:a,...b}){return(0,d.jsx)(O._V,{"data-slot":"avatar-image",className:(0,l.cn)("aspect-square size-full",a),...b})}function R({className:a,...b}){return(0,d.jsx)(O.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-secondary flex size-full items-center justify-center rounded-[inherit] text-xs",a),...b})}function S(){let{user:a,signOut:b,isLoading:c}=(0,M.A)(),{profile:e}=(0,N.x)(),f=(0,L.useRouter)(),[g,h]=(0,i.useState)(!1),j=async()=>{try{h(!0);let a=await b();a.success?f.push("/login"):console.error("Erro ao fazer logout:",a.error)}catch(a){console.error("Erro inesperado ao fazer logout:",a)}finally{h(!1)}},k=a=>{if(!a)return"US";let b=a.split("@")[0].split(".");return b.length>=2?(b[0][0]+b[1][0]).toUpperCase():a.substring(0,2).toUpperCase()},l=()=>{if(e?.full_name)return e.full_name;if(a?.user_metadata?.full_name)return a.user_metadata.full_name;if(a?.user_metadata?.name)return a.user_metadata.name;if(a?.email){let b=a.email.split("@")[0];return b.charAt(0).toUpperCase()+b.slice(1)}return"Usu\xe1rio"},m=a=>{f.push(a)};return c||!a?null:(0,d.jsxs)(p,{children:[(0,d.jsx)(q,{asChild:!0,children:(0,d.jsx)(n,{variant:"ghost",className:"h-auto p-2 hover:bg-accent focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg",disabled:g,children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)(P,{className:"h-8 w-8",children:[(0,d.jsx)(Q,{src:e?.avatar_url||a.user_metadata?.avatar_url||a.user_metadata?.picture,alt:"Imagem do perfil"}),(0,d.jsx)(R,{className:"bg-primary/10 text-primary font-medium",children:k(a.email||"US")})]}),(0,d.jsxs)("div",{className:"flex flex-col items-start min-w-0",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-foreground truncate max-w-[150px]",children:l()}),(0,d.jsx)("span",{className:"text-xs text-muted-foreground truncate max-w-[150px]",children:a.email})]})]})})}),(0,d.jsxs)(r,{className:"w-64",align:"end",sideOffset:8,children:[(0,d.jsx)(u,{className:"flex min-w-0 flex-col p-3",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)(P,{className:"h-10 w-10",children:[(0,d.jsx)(Q,{src:e?.avatar_url||a.user_metadata?.avatar_url||a.user_metadata?.picture,alt:"Imagem do perfil"}),(0,d.jsx)(R,{className:"bg-primary/10 text-primary font-medium",children:k(a.email||"US")})]}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"text-foreground truncate text-sm font-medium",children:l()}),(0,d.jsx)("p",{className:"text-muted-foreground truncate text-xs font-normal",children:a.email})]})]})}),(0,d.jsx)(v,{}),(0,d.jsxs)(s,{children:[(0,d.jsxs)(t,{onClick:()=>m("/profile"),className:"cursor-pointer",children:[(0,d.jsx)(F.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,d.jsx)("span",{children:"Meu Perfil"})]}),(0,d.jsxs)(t,{onClick:()=>m("/configuracoes"),className:"cursor-pointer",children:[(0,d.jsx)(G.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,d.jsx)("span",{children:"Configura\xe7\xf5es"})]})]}),(0,d.jsx)(v,{}),(0,d.jsxs)(s,{children:[(0,d.jsxs)(t,{onClick:()=>m("/projetos"),className:"cursor-pointer",children:[(0,d.jsx)(H.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,d.jsx)("span",{children:"Meus Projetos"})]}),(0,d.jsxs)(t,{onClick:()=>m("/favoritos"),className:"cursor-pointer",children:[(0,d.jsx)(I.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,d.jsx)("span",{children:"Favoritos"})]})]}),(0,d.jsx)(v,{}),(0,d.jsx)(s,{children:(0,d.jsxs)(t,{onClick:()=>window.open("https://docs.supabase.com","_blank"),className:"cursor-pointer",children:[(0,d.jsx)(J.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,d.jsx)("span",{children:"Documenta\xe7\xe3o"})]})}),(0,d.jsx)(v,{}),(0,d.jsxs)(t,{onClick:j,className:"cursor-pointer text-destructive focus:text-destructive",disabled:g,children:[(0,d.jsx)(K.A,{size:16,className:"opacity-60","aria-hidden":"true"}),(0,d.jsx)("span",{children:g?"Saindo...":"Sair"})]})]})]})}var T=c(25541),U=c(12640),V=c(78122);function W(){let[a,b]=(0,i.useState)({portfolioBalance:623098.17,availableFunds:122912.5,isLoading:!1,trend:"up",changePercent:2.34,lastUpdated:new Date}),[c,e]=(0,i.useState)(!1),f=(0,i.useMemo)(()=>{let a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2});return b=>a.format(b)},[]),g=async()=>{e(!0),await new Promise(a=>setTimeout(a,1e3)),b(a=>({...a,portfolioBalance:a.portfolioBalance+(Math.random()-.5)*2e3,availableFunds:a.availableFunds+(Math.random()-.5)*1e3,changePercent:(Math.random()-.5)*8,trend:Math.random()>.5?"up":"down",lastUpdated:new Date})),e(!1)};return a.isLoading?(0,d.jsxs)("div",{className:"flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg border animate-pulse",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,d.jsx)("div",{className:"h-3 bg-muted rounded w-20"}),(0,d.jsx)("div",{className:"h-5 bg-muted rounded w-28"})]}),(0,d.jsx)("div",{className:"h-6 w-px bg-border"}),(0,d.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,d.jsx)("div",{className:"h-3 bg-muted rounded w-24"}),(0,d.jsx)("div",{className:"h-5 bg-muted rounded w-24"})]})]}):(0,d.jsxs)("div",{className:"flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg  hover:bg-card/50 transition-all duration-200 group",children:[(0,d.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,d.jsx)("div",{className:"flex items-center gap-1 mb-0.5",children:(0,d.jsx)("span",{className:"text-xs  text-muted-foreground  tracking-wider",children:"Saldo Inicial"})}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm  text-foreground tabular-nums truncate",children:f(a.portfolioBalance)}),(0,d.jsxs)("div",{className:`flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs font-medium transition-colors ${"up"===a.trend?"text-primary  dark:text-primary ":"text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900/30"}`,children:["up"===a.trend?(0,d.jsx)(T.A,{size:10}):(0,d.jsx)(U.A,{size:10}),(0,d.jsxs)("span",{children:[Math.abs(a.changePercent).toFixed(2),"%"]})]})]})]}),(0,d.jsx)("div",{className:"h-8 w-px bg-border/60"}),(0,d.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,d.jsx)("div",{className:"flex items-center gap-1 mb-0.5",children:(0,d.jsx)("span",{className:"text-xs  text-muted-foreground  tracking-wider",children:"Saldo Atual"})}),(0,d.jsx)("span",{className:"text-sm  text-foreground tabular-nums truncate",children:f(a.availableFunds)})]}),(0,d.jsx)("div",{className:"flex items-center gap-2 ml-2",children:(0,d.jsx)(n,{variant:"ghost",size:"sm",onClick:g,disabled:c,className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,d.jsx)(V.A,{size:14,className:`${c?"animate-spin":""} text-muted-foreground hover:text-foreground`})})})]})}var X=c(363),Y=c(21134),Z=c(78148);function $({className:a,...b}){return(0,d.jsx)(Z.b,{"data-slot":"label",className:(0,l.cn)("text-foreground text-sm leading-4 font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}var _=c(83680);function aa({className:a,...b}){return(0,d.jsx)(_.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:ring-ring/50 inline-flex h-6 w-10 shrink-0 items-center rounded-full border-2 border-transparent transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...b,children:(0,d.jsx)(_.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background pointer-events-none block size-5 rounded-full shadow-xs ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0 data-[state=checked]:rtl:-translate-x-4")})})}function ab(){let a=(0,i.useId)(),{toggleTheme:b,isDark:c,isInitialized:e}=function(){let[a,b]=(0,i.useState)("dark"),[c,d]=(0,i.useState)(!1);return{theme:a,toggleTheme:()=>{if(!c)return;let d="dark"===a?"light":"dark";b(d),document.documentElement.className=d,localStorage.setItem("theme",d)},setTheme:a=>{c&&(b(a),document.documentElement.className=a,localStorage.setItem("theme",a))},isDark:"dark"===a,isLight:"light"===a,isInitialized:c}}();return(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"relative inline-grid h-9 grid-cols-[1fr_1fr] items-center text-sm font-medium",children:[(0,d.jsx)(aa,{id:a,checked:c,onCheckedChange:b,disabled:!e,className:"peer data-[state=checked]:bg-input/50 data-[state=unchecked]:bg-input/50 absolute inset-0 h-[inherit] w-auto [&_span]:h-full [&_span]:w-1/2 [&_span]:transition-transform [&_span]:duration-300 [&_span]:ease-[cubic-bezier(0.16,1,0.3,1)] [&_span]:data-[state=checked]:translate-x-full [&_span]:data-[state=checked]:rtl:-translate-x-full"}),(0,d.jsx)("span",{className:"peer-data-[state=checked]:text-muted-foreground/70 pointer-events-none relative ms-0.5 flex min-w-8 items-center justify-center text-center",children:(0,d.jsx)(X.A,{size:16,"aria-hidden":"true"})}),(0,d.jsx)("span",{className:"peer-data-[state=unchecked]:text-muted-foreground/70 pointer-events-none relative me-0.5 flex min-w-8 items-center justify-center text-center",children:(0,d.jsx)(Y.A,{size:16,"aria-hidden":"true"})})]}),(0,d.jsx)($,{htmlFor:a,className:"sr-only",children:"Alternar tema entre claro e escuro"})]})}function ac(){return(0,d.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsx)("div",{className:"container mx-auto px-4 md:px-6",children:(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between gap-4",children:[(0,d.jsx)("div",{className:"flex items-center gap-4",children:(0,d.jsx)(W,{})}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(ab,{}),(0,d.jsx)(E,{}),(0,d.jsx)(w,{}),(0,d.jsx)(S,{})]})]})})})}},28566:(a,b,c)=>{Promise.resolve().then(c.bind(c,33031))},33031:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\layout\\\\ConditionalLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\layout\\ConditionalLayout.tsx","default")},34570:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687);c(43210);var e=c(16189),f=c(87979);function g({children:a}){let{user:b,isLoading:c}=(0,f.A)();return((0,e.useRouter)(),c)?(0,d.jsx)("div",{className:"h-screen flex items-center justify-center bg-background",children:(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):b?(0,d.jsx)(d.Fragment,{children:a}):null}},35798:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},39727:()=>{},47990:()=>{},50549:(a,b,c)=>{"use strict";c.d(b,{SidebarProvider:()=>g,c:()=>h});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0);function g({children:a}){let[b,c]=(0,e.useState)(!1),[g,h]=(0,e.useState)(!1);return(0,d.jsx)(f.Provider,{value:{isCollapsed:b,toggleSidebar:()=>{c(a=>!a)},setSidebarCollapsed:a=>{c(a)}},children:a})}function h(){let a=(0,e.useContext)(f);if(void 0===a)throw Error("useSidebar deve ser usado dentro de um SidebarProvider");return a}},54348:(a,b,c)=>{"use strict";c.d(b,{x:()=>d.x});var d=c(6880)},61135:()=>{},72750:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},75422:(a,b,c)=>{Promise.resolve().then(c.bind(c,22289))},87979:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(43210),e=c(16391);let f=()=>{let[a,b]=(0,d.useState)(null),[c,f]=(0,d.useState)(null),[g,h]=(0,d.useState)(!0),[i,j]=(0,d.useState)(null);return(0,d.useEffect)(()=>{(async()=>{let{data:{session:a},error:c}=await e.N.auth.getSession();c?j({message:c.message}):(f(a),b(a?.user??null)),h(!1)})();let{data:{subscription:a}}=e.N.auth.onAuthStateChange(async(a,c)=>{f(c),b(c?.user??null),h(!1),"SIGNED_OUT"===a&&j(null)});return()=>a.unsubscribe()},[]),{user:a,session:c,isLoading:g,error:i,signInWithEmail:async(a,b)=>{h(!0),j(null);let{data:c,error:d}=await e.N.auth.signInWithPassword({email:a,password:b});return d?(j({message:d.message}),h(!1),{success:!1,error:d}):(h(!1),{success:!0,data:c})},signUpWithEmail:async(a,b)=>{h(!0),j(null);let{data:c,error:d}=await e.N.auth.signUp({email:a,password:b});return d?(j({message:d.message}),h(!1),{success:!1,error:d}):(h(!1),{success:!0,data:c})},signInWithGoogle:async()=>{h(!0),j(null);let{data:a,error:b}=await e.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/dashboard`}});return b?(j({message:b.message}),h(!1),{success:!1,error:b}):{success:!0,data:a}},signInWithOTP:async a=>{h(!0),j(null);let{data:b,error:c}=await e.N.auth.signInWithOtp({email:a,options:{shouldCreateUser:!0}});return c?(j({message:c.message}),h(!1),{success:!1,error:c}):(h(!1),{success:!0,data:b})},verifyOTP:async(a,b)=>{h(!0),j(null);let{data:c,error:d}=await e.N.auth.verifyOtp({email:a,token:b,type:"email"});return d?(j({message:d.message}),h(!1),{success:!1,error:d}):(h(!1),{success:!0,data:c})},signOut:async()=>{h(!0);let{error:a}=await e.N.auth.signOut();return a&&j({message:a.message}),h(!1),{success:!a,error:a}}}}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(37413),e=c(16695),f=c.n(e),g=c(15666),h=c.n(g);c(61135);var i=c(33031);let j={title:"Frontend - Projeto Profit Growth",description:"Frontend do projeto Profit Growth",icons:{icon:[{url:"/icon-logo.svg",type:"image/svg+xml"}],shortcut:"/icon-logo.svg",apple:"/icon-logo.svg"}};function k({children:a}){return(0,d.jsx)("html",{lang:"pt-BR",className:`${f().variable} ${h().variable}`,children:(0,d.jsx)("body",{className:`${f().className} antialiased`,children:(0,d.jsx)(i.default,{children:a})})})}}};