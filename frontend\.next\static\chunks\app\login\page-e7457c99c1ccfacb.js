(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{2099:(e,t,s)=>{"use strict";s.d(t,{N:()=>a});let a=(0,s(5647).UU)("https://dpofnwutgpbwylwtbgnv.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwb2Zud3V0Z3Bid3lsd3RiZ252Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNDQyOTMsImV4cCI6MjA2ODgyMDI5M30.lE40lk7gBuq77mqJUezZYxUmHSeRSC6kOTVjwvP2hLw")},2543:(e,t,s)=>{Promise.resolve().then(s.bind(s,4215))},4215:(e,t,s)=>{"use strict";s.d(t,{SupabaseSignIn:()=>_});var a=s(5155),n=s(2115),i=s(3463),r=s(760),l=s(6874),o=s.n(l),c=s(6766),u=s(5695),d=s(9434),m=s(6681),f=s(1949),x=s(7558),h=s(3264);let p=e=>{let{animationSpeed:t=10,opacities:s=[.3,.3,.3,.5,.5,.5,.8,.8,.8,1],colors:n=[[8,210,133]],containerClassName:i,dotSize:r,showGradient:l=!0,reverse:o=!1}=e;return(0,a.jsxs)("div",{className:(0,d.cn)("h-full relative w-full",i),children:[(0,a.jsx)("div",{className:"h-full w-full",children:(0,a.jsx)(v,{colors:null!=n?n:[[8,210,133]],dotSize:null!=r?r:3,opacities:null!=s?s:[.3,.3,.3,.5,.5,.5,.8,.8,.8,1],shader:"".concat(o?"u_reverse_active":"false","_;animation_speed_factor_").concat(t.toFixed(1),"_;"),center:["x","y"]})}),l&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black to-transparent"})]})},v=e=>{let{colors:t=[[8,210,133]],opacities:s=[.04,.04,.04,.04,.04,.08,.08,.08,.08,.14],totalSize:i=20,dotSize:r=2,shader:l="",center:o=["x","y"]}=e,c=n.useMemo(()=>{let e=[t[0],t[0],t[0],t[0],t[0],t[0]];return 2===t.length?e=[t[0],t[0],t[0],t[1],t[1],t[1]]:3===t.length&&(e=[t[0],t[0],t[1],t[1],t[2],t[2]]),{u_colors:{value:e.map(e=>[e[0]/255,e[1]/255,e[2]/255]),type:"uniform3fv"},u_opacities:{value:s,type:"uniform1fv"},u_total_size:{value:i,type:"uniform1f"},u_dot_size:{value:r,type:"uniform1f"},u_reverse:{value:+!!l.includes("u_reverse_active"),type:"uniform1i"}}},[t,s,i,r,l]);return(0,a.jsx)(y,{source:"precision mediump float;\nin vec2 fragCoord;\nuniform float u_time;\nuniform float u_opacities[10];\nuniform vec3 u_colors[6];\nuniform float u_total_size;\nuniform float u_dot_size;\nuniform vec2 u_resolution;\nuniform int u_reverse;\nout vec4 fragColor;\n\nfloat PHI = 1.61803398874989484820459;\n\nfloat random(vec2 xy) {\n  return fract(tan(distance(xy * PHI, xy) * 0.5) * xy.x);\n}\n\nfloat map(float value, float min1, float max1, float min2, float max2) {\n  return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n}\n\nvoid main() {\n  vec2 st = fragCoord.xy;\n  \n  ".concat(o.includes("x")?"st.x -= abs(floor((mod(u_resolution.x, u_total_size) - u_dot_size) * 0.5));":"","\n  ").concat(o.includes("y")?"st.y -= abs(floor((mod(u_resolution.y, u_total_size) - u_dot_size) * 0.5));":"","\n\n  float opacity = step(0.0, st.x);\n  opacity *= step(0.0, st.y);\n  \n  vec2 st2 = vec2(int(st.x / u_total_size), int(st.y / u_total_size));\n  \n  float frequency = 5.0;\n  float show_offset = random(st2);\n  float rand = random(st2 * floor((u_time / frequency) + show_offset + frequency));\n  \n  opacity *= u_opacities[int(rand * 10.0)];\n  opacity *= 1.0 - step(u_dot_size / u_total_size, fract(st.x / u_total_size));\n  opacity *= 1.0 - step(u_dot_size / u_total_size, fract(st.y / u_total_size));\n  \n  vec3 color = u_colors[int(show_offset * 6.0)];\n  \n  float animation_speed_factor = 0.5;\n  vec2 center_grid = u_resolution / 2.0 / u_total_size;\n  float dist_from_center = distance(center_grid, st2);\n  \n  float timing_offset_intro = dist_from_center * 0.01 + (random(st2) * 0.15);\n  float max_grid_dist = distance(center_grid, vec2(0.0, 0.0));\n  float timing_offset_outro = (max_grid_dist - dist_from_center) * 0.02 + (random(st2 + 42.0) * 0.2);\n  \n  float current_timing_offset;\n  if (u_reverse == 1) {\n    current_timing_offset = timing_offset_outro;\n    opacity *= 1.0 - step(current_timing_offset, u_time * animation_speed_factor);\n    opacity *= clamp((step(current_timing_offset + 0.1, u_time * animation_speed_factor)) * 1.25, 1.0, 1.25);\n  } else {\n    current_timing_offset = timing_offset_intro;\n    opacity *= step(current_timing_offset, u_time * animation_speed_factor);\n    opacity *= clamp((1.0 - step(current_timing_offset + 0.1, u_time * animation_speed_factor)) * 1.25, 1.0, 1.25);\n  }\n  \n  fragColor = vec4(color, opacity);\n  fragColor.rgb *= fragColor.a;\n}"),uniforms:c})},g=e=>{let{source:t,uniforms:s}=e,{size:i}=(0,f.C)(),r=(0,n.useRef)(null);(0,f.D)(e=>{let{clock:t}=e;if(!r.current)return;let s=t.getElapsedTime();r.current.material.uniforms.u_time.value=s});let l=(0,n.useMemo)(()=>new h.BKk({vertexShader:"precision mediump float;\nin vec2 coordinates;\nuniform vec2 u_resolution;\nout vec2 fragCoord;\n\nvoid main(){\n  float x = position.x;\n  float y = position.y;\n  gl_Position = vec4(x, y, 0.0, 1.0);\n  fragCoord = (position.xy + vec2(1.0)) * 0.5 * u_resolution;\n  fragCoord.y = u_resolution.y - fragCoord.y;\n}",fragmentShader:t,uniforms:(()=>{let e={};for(let t in s){let a=s[t];switch(a.type){case"uniform1f":e[t]={value:a.value,type:"1f"};break;case"uniform1i":e[t]={value:a.value,type:"1i"};break;case"uniform3f":e[t]={value:new h.Pq0().fromArray(Array.isArray(a.value)&&!Array.isArray(a.value[0])&&a.value.length>=3?a.value:[0,0,0]),type:"3f"};break;case"uniform1fv":e[t]={value:a.value,type:"1fv"};break;case"uniform3fv":e[t]={value:Array.isArray(a.value)&&Array.isArray(a.value[0])?a.value.map(e=>new h.Pq0().fromArray(e)):[],type:"3fv"};break;case"uniform2f":e[t]={value:new h.I9Y().fromArray(Array.isArray(a.value)&&!Array.isArray(a.value[0])?a.value:[0,0]),type:"2f"};break;default:console.error("Invalid uniform type for '".concat(t,"'."))}}return e.u_time={value:0,type:"1f"},e.u_resolution={value:new h.I9Y(2*i.width,2*i.height)},e})(),glslVersion:h.Wdf,blending:h.bCz,blendSrc:h.ie2,blendDst:h.qad}),[t,s,i.width,i.height]);return(0,a.jsxs)("mesh",{ref:r,children:[(0,a.jsx)("planeGeometry",{args:[2,2]}),(0,a.jsx)("primitive",{object:l,attach:"material"})]})},y=e=>{let{source:t,uniforms:s}=e;return(0,a.jsx)(x.Hl,{className:"absolute inset-0 h-full w-full",children:(0,a.jsx)(g,{source:t,uniforms:s})})},b=e=>{let{href:t,children:s}=e;return(0,a.jsx)("a",{href:t,className:"group relative inline-block overflow-hidden h-5 flex items-center ".concat("text-sm"),children:(0,a.jsxs)("div",{className:"flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2",children:[(0,a.jsx)("span",{className:"text-gray-300",children:s}),(0,a.jsx)("span",{className:"text-white",children:s})]})})},w=()=>{let[e,t]=(0,n.useState)(!1),[s,i]=(0,n.useState)("rounded-full"),r=(0,n.useRef)(null);(0,n.useEffect)(()=>(r.current&&clearTimeout(r.current),e?i("rounded-xl"):r.current=setTimeout(()=>{i("rounded-full")},300),()=>{r.current&&clearTimeout(r.current)}),[e]);let l=[{label:"Planos",href:"#1"},{label:"Ferramentas",href:"#2"},{label:"Contato",href:"#3"}],o=(0,a.jsx)("button",{className:"px-4 py-2 sm:px-3 text-xs sm:text-sm border border-[#333] bg-[rgba(31,31,31,0.62)] text-gray-300 rounded-full hover:border-white/50 hover:text-white transition-colors duration-200 w-full sm:w-auto",children:"Entrar"}),u=(0,a.jsxs)("div",{className:"relative group w-full sm:w-auto",children:[(0,a.jsx)("div",{className:"absolute inset-0 -m-2 rounded-full hidden sm:block bg-green-100 opacity-40 filter blur-lg pointer-events-none transition-all duration-300 ease-out group-hover:opacity-60 group-hover:blur-xl group-hover:-m-3"}),(0,a.jsx)("button",{className:"relative z-10 px-4 py-2 sm:px-3 text-xs sm:text-sm font-semibold text-black bg-gradient-to-br from-green-100 to-green-400 rounded-full hover:from-green-200 hover:to-green-400 transition-all duration-200 w-full sm:w-auto",children:"Inscreva-se"})]});return(0,a.jsxs)("header",{className:"fixed top-6 left-1/2 transform -translate-x-1/2 z-20 flex flex-col items-center pl-6 pr-6 py-3 backdrop-blur-sm ".concat(s," border border-[#333] bg-[#1f1f1f57] w-[calc(100%-2rem)] sm:w-auto transition-[border-radius] duration-0 ease-in-out"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between w-full gap-x-6 sm:gap-x-8",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(c.default,{src:"/logo.svg",alt:"Logo",width:144,height:20,className:"w-36 h-5"})}),(0,a.jsx)("nav",{className:"hidden sm:flex items-center space-x-4 sm:space-x-6 text-sm",children:l.map(e=>(0,a.jsx)(b,{href:e.href,children:e.label},e.href))}),(0,a.jsxs)("div",{className:"hidden sm:flex items-center gap-2 sm:gap-3",children:[o,u]}),(0,a.jsx)("button",{className:"sm:hidden flex items-center justify-center w-8 h-8 text-gray-300 focus:outline-none",onClick:()=>{t(!e)},"aria-label":e?"Close Menu":"Open Menu",children:e?(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})}):(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})})]}),(0,a.jsxs)("div",{className:"sm:hidden flex flex-col items-center w-full transition-all ease-in-out duration-300 overflow-hidden ".concat(e?"max-h-[1000px] opacity-100 pt-4":"max-h-0 opacity-0 pt-0 pointer-events-none"),children:[(0,a.jsx)("nav",{className:"flex flex-col items-center space-y-4 text-base w-full",children:l.map(e=>(0,a.jsx)("a",{href:e.href,className:"text-gray-300 hover:text-white transition-colors w-full text-center",children:e.label},e.href))}),(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 mt-4 w-full",children:[o,u]})]})]})},_=e=>{let{className:t}=e,[s,l]=(0,n.useState)(""),[c,f]=(0,n.useState)(""),[x,h]=(0,n.useState)("email"),[v,g]=(0,n.useState)(["","","","","",""]),[y,b]=(0,n.useState)("signin"),[_,j]=(0,n.useState)(!1),N=(0,n.useRef)([]),[k,C]=(0,n.useState)(!0),[S,z]=(0,n.useState)(!1),I=(0,u.useRouter)(),{signInWithEmail:P,signInWithGoogle:A,signInWithOTP:O,verifyOTP:E,isLoading:T,error:M,user:R}=(0,m.A)();(0,n.useEffect)(()=>{R&&I.push("/dashboard")},[R,I]);let q=async e=>{e.preventDefault(),s&&("signin"===y?h("password"):(await O(s)).success&&h("code"))},D=async e=>{e.preventDefault(),s&&c&&(await P(s,c)).success&&(z(!0),setTimeout(()=>{C(!1)},50),setTimeout(()=>{h("success")},2e3))},J=async()=>{await A()};(0,n.useEffect)(()=>{"code"===x&&setTimeout(()=>{var e;null==(e=N.current[0])||e.focus()},500)},[x]);let L=async e=>{(await E(s,e)).success&&(z(!0),setTimeout(()=>{C(!1)},50),setTimeout(()=>{h("success")},2e3))},W=()=>{"password"===x?(h("email"),f("")):"code"===x&&(h("email"),g(["","","","","",""])),z(!1),C(!0)};return(0,a.jsxs)("div",{className:(0,d.cn)("flex w-[100%] flex-col min-h-screen bg-black relative",t),children:[(0,a.jsxs)("div",{className:"absolute inset-0 z-0",children:[k&&(0,a.jsx)("div",{className:"absolute inset-0",children:(0,a.jsx)(p,{animationSpeed:3,containerClassName:"bg-black",colors:[[8,210,133],[255,255,255]],dotSize:6,reverse:!1})}),S&&(0,a.jsx)("div",{className:"absolute inset-0",children:(0,a.jsx)(p,{animationSpeed:4,containerClassName:"bg-black",colors:[[8,210,133],[255,255,255]],dotSize:6,reverse:!0})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(0,0,0,1)_0%,_transparent_100%)]"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-black to-transparent"})]}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col flex-1",children:[(0,a.jsx)(w,{}),(0,a.jsx)("div",{className:"flex flex-1 flex-col lg:flex-row",children:(0,a.jsx)("div",{className:"flex-1 flex flex-col justify-center items-center",children:(0,a.jsxs)("div",{className:"w-full mt-[150px] max-w-sm",children:[M&&(0,a.jsx)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm text-center",children:M.message}),(0,a.jsx)(r.N,{mode:"wait",children:"email"===x?(0,a.jsxs)(i.P.div,{initial:{opacity:0,x:-100},animate:{opacity:1,x:0},exit:{opacity:0,x:-100},transition:{duration:.4,ease:"easeOut"},className:"space-y-6 text-center",children:[(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h1",{className:"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white",children:"signin"===y?"Bem-vindo de volta":"Criar conta"}),(0,a.jsx)("p",{className:"text-[1.8rem] text-white/70 font-light",children:"signin"===y?"Entre na sua conta":"Junte-se a n\xf3s hoje"})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:J,disabled:T,className:"backdrop-blur-[2px] w-full flex items-center justify-center gap-2 bg-white/5 hover:bg-white/10 text-white border border-white/10 rounded-full py-3 px-4 transition-colors disabled:opacity-50",children:[(0,a.jsx)("span",{className:"text-lg",children:"G"}),(0,a.jsxs)("span",{children:["signin"===y?"Entrar":"Registrar"," com Google"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"h-px bg-white/10 flex-1"}),(0,a.jsx)("span",{className:"text-white/40 text-sm",children:"ou"}),(0,a.jsx)("div",{className:"h-px bg-white/10 flex-1"})]}),(0,a.jsx)("form",{onSubmit:q,children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"email",placeholder:"<EMAIL>",value:s,onChange:e=>l(e.target.value),disabled:T,className:"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50",required:!0}),(0,a.jsx)("button",{type:"submit",disabled:T,className:"absolute right-1.5 top-1.5 text-white w-9 h-9 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-colors group overflow-hidden disabled:opacity-50",children:(0,a.jsxs)("span",{className:"relative w-full h-full block overflow-hidden",children:[(0,a.jsx)("span",{className:"absolute inset-0 flex items-center justify-center transition-transform duration-300 group-hover:translate-x-full",children:"→"}),(0,a.jsx)("span",{className:"absolute inset-0 flex items-center justify-center transition-transform duration-300 -translate-x-full group-hover:translate-x-0",children:"→"})]})})]})})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("button",{onClick:()=>b("signin"===y?"signup":"signin"),className:"text-white/50 hover:text-white/70 transition-colors text-sm",children:"signin"===y?"N\xe3o tem conta? Registre-se":"J\xe1 tem conta? Entre"})}),(0,a.jsxs)("p",{className:"text-xs text-white/40 pt-10",children:["Ao continuar, voc\xea concorda com os"," ",(0,a.jsx)(o(),{href:"#",className:"underline text-white/40 hover:text-white/60 transition-colors",children:"Termos de Servi\xe7o"})," ","e"," ",(0,a.jsx)(o(),{href:"#",className:"underline text-white/40 hover:text-white/60 transition-colors",children:"Pol\xedtica de Privacidade"}),"."]})]},"email-step"):"password"===x?(0,a.jsxs)(i.P.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:100},transition:{duration:.4,ease:"easeOut"},className:"space-y-6 text-center",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h1",{className:"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white",children:"Digite sua senha"}),(0,a.jsxs)("p",{className:"text-[1.25rem] text-white/50 font-light",children:["Para ",s]})]}),(0,a.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:_?"text":"password",placeholder:"Sua senha",value:c,onChange:e=>f(e.target.value),disabled:T,className:"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>j(!_),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 text-sm",children:_?"Ocultar":"Mostrar"})]}),(0,a.jsxs)("div",{className:"flex w-full gap-3",children:[(0,a.jsx)(i.P.button,{type:"button",onClick:W,className:"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]",whileHover:{scale:1.02},whileTap:{scale:.98},transition:{duration:.2},children:"Voltar"}),(0,a.jsx)(i.P.button,{type:"submit",disabled:T||!c,className:"flex-1 rounded-full font-medium py-3 border transition-all duration-300 ".concat(c&&!T?"bg-white text-black border-transparent hover:bg-white/90 cursor-pointer":"bg-[#111] text-white/50 border-white/10 cursor-not-allowed"),children:T?"Entrando...":"Entrar"})]})]})]},"password-step"):"code"===x?(0,a.jsxs)(i.P.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:100},transition:{duration:.4,ease:"easeOut"},className:"space-y-6 text-center",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h1",{className:"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white",children:"C\xf3digo enviado"}),(0,a.jsx)("p",{className:"text-[1.25rem] text-white/50 font-light",children:"Verifique seu email"})]}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)("div",{className:"relative rounded-full py-4 px-5 border border-white/10 bg-transparent",children:(0,a.jsx)("div",{className:"flex items-center justify-center",children:v.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{ref:e=>{N.current[t]=e},type:"text",inputMode:"numeric",pattern:"[0-9]*",maxLength:1,value:e,onChange:e=>((e,t)=>{if(t.length<=1){let a=[...v];if(a[e]=t,g(a),t&&e<5){var s;null==(s=N.current[e+1])||s.focus()}5===e&&t&&a.every(e=>1===e.length)&&L(a.join(""))}})(t,e.target.value),onKeyDown:e=>((e,t)=>{if("Backspace"===t.key&&!v[e]&&e>0){var s;null==(s=N.current[e-1])||s.focus()}})(t,e),disabled:T,className:"w-8 text-center text-xl bg-transparent text-white border-none focus:outline-none focus:ring-0 appearance-none disabled:opacity-50",style:{caretColor:"transparent"}}),!e&&(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-xl text-white",children:"0"})})]}),t<5&&(0,a.jsx)("span",{className:"text-white/20 text-xl",children:"|"})]},t))})})}),(0,a.jsxs)("div",{className:"flex w-full gap-3",children:[(0,a.jsx)(i.P.button,{onClick:W,className:"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]",whileHover:{scale:1.02},whileTap:{scale:.98},transition:{duration:.2},children:"Voltar"}),(0,a.jsx)(i.P.button,{disabled:T,className:"flex-1 rounded-full font-medium py-3 border bg-[#111] text-white/50 border-white/10 cursor-not-allowed",children:T?"Verificando...":"Digite o c\xf3digo"})]})]},"code-step"):(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.4,ease:"easeOut",delay:.3},className:"space-y-6 text-center",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h1",{className:"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white",children:"Sucesso!"}),(0,a.jsx)("p",{className:"text-[1.25rem] text-white/50 font-light",children:"Redirecionando..."})]}),(0,a.jsx)(i.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5,delay:.5},className:"py-10",children:(0,a.jsx)("div",{className:"mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-white to-white/70 flex items-center justify-center",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-black",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})})]},"success-step")})]})})})]})]})}},6681:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(2115),n=s(2099);let i=()=>{let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(null),[r,l]=(0,a.useState)(!0),[o,c]=(0,a.useState)(null);return(0,a.useEffect)(()=>{(async()=>{let{data:{session:e},error:s}=await n.N.auth.getSession();if(s)c({message:s.message});else{var a;i(e),t(null!=(a=null==e?void 0:e.user)?a:null)}l(!1)})();let{data:{subscription:e}}=n.N.auth.onAuthStateChange(async(e,s)=>{var a;i(s),t(null!=(a=null==s?void 0:s.user)?a:null),l(!1),"SIGNED_OUT"===e&&c(null)});return()=>e.unsubscribe()},[]),{user:e,session:s,isLoading:r,error:o,signInWithEmail:async(e,t)=>{l(!0),c(null);let{data:s,error:a}=await n.N.auth.signInWithPassword({email:e,password:t});return a?(c({message:a.message}),l(!1),{success:!1,error:a}):(l(!1),{success:!0,data:s})},signUpWithEmail:async(e,t)=>{l(!0),c(null);let{data:s,error:a}=await n.N.auth.signUp({email:e,password:t});return a?(c({message:a.message}),l(!1),{success:!1,error:a}):(l(!1),{success:!0,data:s})},signInWithGoogle:async()=>{l(!0),c(null);let{data:e,error:t}=await n.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/dashboard")}});return t?(c({message:t.message}),l(!1),{success:!1,error:t}):{success:!0,data:e}},signInWithOTP:async e=>{l(!0),c(null);let{data:t,error:s}=await n.N.auth.signInWithOtp({email:e,options:{shouldCreateUser:!0}});return s?(c({message:s.message}),l(!1),{success:!1,error:s}):(l(!1),{success:!0,data:t})},verifyOTP:async(e,t)=>{l(!0),c(null);let{data:s,error:a}=await n.N.auth.verifyOtp({email:e,token:t,type:"email"});return a?(c({message:a.message}),l(!1),{success:!1,error:a}):(l(!1),{success:!0,data:s})},signOut:async()=>{l(!0);let{error:e}=await n.N.auth.signOut();return e&&c({message:e.message}),l(!1),{success:!e,error:e}}}}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),n=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}}},e=>{e.O(0,[367,831,413,302,926,36,562,441,964,358],()=>e(e.s=2543)),_N_E=e.O()}]);