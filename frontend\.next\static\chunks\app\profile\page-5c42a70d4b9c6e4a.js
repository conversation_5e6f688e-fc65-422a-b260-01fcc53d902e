(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{1007:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1264:(e,a,r)=>{Promise.resolve().then(r.bind(r,2829))},1824:(e,a,r)=>{"use strict";r.d(a,{x:()=>t.x});var t=r(6064)},2099:(e,a,r)=>{"use strict";r.d(a,{N:()=>t});let t=(0,r(5647).UU)("https://dpofnwutgpbwylwtbgnv.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwb2Zud3V0Z3Bid3lsd3RiZ252Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNDQyOTMsImV4cCI6MjA2ODgyMDI5M30.lE40lk7gBuq77mqJUezZYxUmHSeRSC6kOTVjwvP2hLw")},2829:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>w});var t=r(5155),s=r(2115),l=r(3463),i=r(760),n=r(6681),o=r(1824),c=r(5876),d=r(1007),u=r(9946);let m=(0,u.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),h=(0,u.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var p=r(6766);function x(e){let{currentAvatar:a,onAvatarChange:r,isUploading:n=!1,disabled:o=!1,size:c="md"}=e,[u,x]=(0,s.useState)(!1),[f,v]=(0,s.useState)(null),g=(0,s.useRef)(null),y=async e=>{if(!e.type.startsWith("image/"))return void alert("Por favor, selecione apenas arquivos de imagem");if(e.size>5242880)return void alert("A imagem deve ter no m\xe1ximo 5MB");let a=new FileReader;a.onload=e=>{var a;v(null==(a=e.target)?void 0:a.result)},a.readAsDataURL(e),await r(e)},b=f||a;return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(l.P.div,{className:"".concat({sm:"w-16 h-16",md:"w-24 h-24",lg:"w-32 h-32"}[c]," relative rounded-full overflow-hidden border-4 border-background shadow-lg cursor-pointer group"),whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{var e;return!o&&(null==(e=g.current)?void 0:e.click())},onDrop:e=>{e.preventDefault(),x(!1);let a=e.dataTransfer.files[0];a&&y(a)},onDragOver:e=>{e.preventDefault(),x(!0)},onDragLeave:()=>{x(!1)},children:(0,t.jsxs)("div",{className:"w-full h-full relative",children:[b?(0,t.jsx)(p.default,{src:b,alt:"Avatar do usu\xe1rio",fill:!0,className:"object-cover"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-primary/10",children:(0,t.jsx)(d.A,{className:"".concat({sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[c]," text-primary")})}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity ".concat(o?"hidden":""),children:(0,t.jsx)(m,{className:"w-6 h-6 text-white"})}),(0,t.jsx)(i.N,{children:u&&(0,t.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-primary/20 border-2 border-dashed border-primary flex items-center justify-center",children:(0,t.jsx)(h,{className:"w-6 h-6 text-primary"})})}),(0,t.jsx)(i.N,{children:n&&(0,t.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/70 flex items-center justify-center",children:(0,t.jsx)("div",{className:"w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"})})})]})}),!o&&(0,t.jsx)(l.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>{var e;return null==(e=g.current)?void 0:e.click()},className:"absolute -bottom-2 -right-2 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center shadow-lg hover:bg-primary/90 transition-colors",children:n?(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin"}):(0,t.jsx)(m,{className:"w-4 h-4"})}),(0,t.jsx)("input",{ref:g,type:"file",accept:"image/*",onChange:e=>{var a;let r=null==(a=e.target.files)?void 0:a[0];r&&y(r)},className:"hidden",disabled:o})]})}let f=(0,u.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),v=(0,u.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),g=(0,u.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),y=(0,u.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),b=(0,u.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function w(){let{isLoading:e}=(0,n.A)(),{profile:a,isLoading:r,error:u,updateProfile:m,uploadAvatar:h,removeAvatar:p,clearError:w}=(0,o.x)(),[N,j]=(0,s.useState)(!1),[k,A]=(0,s.useState)(!1),[E,_]=(0,s.useState)(!1),[C,S]=(0,s.useState)(""),[I,M]=(0,s.useState)(null);(0,s.useEffect)(()=>{a&&S(a.full_name||"")},[a]);let P=async e=>{_(!0),w();try{(await h(e)).success&&(M("Avatar atualizado com sucesso!"),setTimeout(()=>M(null),3e3))}catch(e){console.error("Erro no upload:",e)}finally{_(!1)}},O=async()=>{if(confirm("Tem certeza que deseja remover seu avatar?"))try{let e=await p();(null==e?void 0:e.success)&&(M("Avatar removido com sucesso!"),setTimeout(()=>M(null),3e3))}catch(e){console.error("Erro ao remover avatar:",e)}},z=async()=>{if(a){A(!0),w();try{(await m({full_name:C.trim()})).success&&(j(!1),M("Perfil atualizado com sucesso!"),setTimeout(()=>M(null),3e3))}catch(e){console.error("Erro ao salvar perfil:",e)}finally{A(!1)}}},U=e=>new Date(e).toLocaleDateString("pt-BR",{year:"numeric",month:"long",day:"numeric"});return e||r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Carregando perfil..."})]})}):(0,t.jsx)(c.A,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-background via-background to-muted/20",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"Meu Perfil"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Gerencie suas informa\xe7\xf5es pessoais e configura\xe7\xf5es da conta"})]}),(0,t.jsxs)(i.N,{children:[u&&(0,t.jsx)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive",children:u}),I&&(0,t.jsx)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mb-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-green-600",children:I})]}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-card border border-border rounded-xl shadow-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 p-6 border-b border-border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(x,{currentAvatar:null==a?void 0:a.avatar_url,onAvatarChange:P,isUploading:E,disabled:!N,size:"lg"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:(null==a?void 0:a.full_name)||"Usu\xe1rio"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:null==a?void 0:a.email})]})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:N?(0,t.jsxs)("div",{className:"flex space-x-2",children:[(null==a?void 0:a.avatar_url)&&(0,t.jsxs)("button",{onClick:O,className:"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2",children:[(0,t.jsx)(f,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Remover Avatar"})]}),(0,t.jsxs)("button",{onClick:()=>{j(!1),S((null==a?void 0:a.full_name)||""),w()},className:"px-4 py-2 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors flex items-center space-x-2",children:[(0,t.jsx)(v,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Cancelar"})]}),(0,t.jsxs)("button",{onClick:z,disabled:k,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50",children:[k?(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,t.jsx)(g,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:k?"Salvando...":"Salvar"})]})]}):(0,t.jsxs)("button",{onClick:()=>{j(!0),S((null==a?void 0:a.full_name)||"")},className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Editar Perfil"})]})})]})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-foreground flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Nome Completo"})]}),N?(0,t.jsx)("input",{type:"text",value:C,onChange:e=>S(e.target.value),placeholder:"Digite seu nome completo",className:"w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"}):(0,t.jsx)("p",{className:"px-3 py-2 bg-muted rounded-lg text-foreground",children:(null==a?void 0:a.full_name)||"N\xe3o informado"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-foreground flex items-center space-x-2",children:[(0,t.jsx)(y,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Email"})]}),(0,t.jsx)("p",{className:"px-3 py-2 bg-muted rounded-lg text-muted-foreground",children:null==a?void 0:a.email}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"O email n\xe3o pode ser alterado"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-foreground flex items-center space-x-2",children:[(0,t.jsx)(b,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Membro desde"})]}),(0,t.jsx)("p",{className:"px-3 py-2 bg-muted rounded-lg text-foreground",children:(null==a?void 0:a.created_at)?U(a.created_at):"N\xe3o dispon\xedvel"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-foreground flex items-center space-x-2",children:[(0,t.jsx)(b,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"\xdaltima Atualiza\xe7\xe3o"})]}),(0,t.jsx)("p",{className:"px-3 py-2 bg-muted rounded-lg text-foreground",children:(null==a?void 0:a.updated_at)?U(a.updated_at):"Nunca atualizado"})]})]})})]})]})})})}},5876:(e,a,r)=>{"use strict";r.d(a,{A:()=>n});var t=r(5155),s=r(2115),l=r(5695),i=r(6681);function n(e){let{children:a}=e,{user:r,isLoading:n}=(0,i.A)(),o=(0,l.useRouter)();return((0,s.useEffect)(()=>{n||r||o.push("/login")},[r,n,o]),n)?(0,t.jsx)("div",{className:"h-screen flex items-center justify-center bg-background",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):r?(0,t.jsx)(t.Fragment,{children:a}):null}},6064:(e,a,r)=>{"use strict";r.d(a,{H:()=>o,x:()=>c});var t=r(5155),s=r(2115),l=r(2099),i=r(6681);let n=(0,s.createContext)(void 0),o=e=>{let{children:a}=e,{user:r}=(0,i.A)(),[o,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(!0),[m,h]=(0,s.useState)(null),[p,x]=(0,s.useState)(!1),f=(0,s.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!r){c(null),u(!1),x(!1);return}if(!e&&o&&p)return void u(!1);try{u(!0),h(null);let{data:e,error:s}=await l.N.from("profiles").select("*").eq("id",r.id).single();if(s&&"PGRST116"!==s.code)throw s;if(e)c(e);else{var a,t;let e={id:r.id,email:r.email||"",full_name:(null==(a=r.user_metadata)?void 0:a.full_name)||"",avatar_url:(null==(t=r.user_metadata)?void 0:t.avatar_url)||"",created_at:r.created_at},{data:s,error:i}=await l.N.from("profiles").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(i)throw i;c(s)}x(!0)}catch(e){console.error("Erro ao carregar perfil:",e),h("Erro ao carregar perfil do usu\xe1rio"),x(!1)}finally{u(!1)}},[r,o,p]),v=async()=>{await f(!0)},g=async e=>{if(!r||!o)throw Error("Usu\xe1rio n\xe3o autenticado ou perfil n\xe3o carregado");try{h(null);let a={...e,updated_at:new Date().toISOString()},{data:t,error:s}=await l.N.from("profiles").update(a).eq("id",r.id).select().single();if(s)throw s;return c(t),{success:!0,data:t}}catch(a){console.error("Erro ao atualizar perfil:",a);let e="Erro ao salvar altera\xe7\xf5es";return h(e),{success:!1,error:e}}},y=async e=>{if(!r)throw Error("Usu\xe1rio n\xe3o autenticado");try{if(h(null),!e.type.startsWith("image/"))throw Error("Por favor, selecione apenas arquivos de imagem");if(e.size>5242880)throw Error("A imagem deve ter no m\xe1ximo 5MB");let a=e.name.split(".").pop(),t="".concat(r.id,"-").concat(Date.now(),".").concat(a),s="avatars/".concat(t);if(null==o?void 0:o.avatar_url){let e=o.avatar_url.split("/").pop();e&&await l.N.storage.from("avatars").remove(["avatars/".concat(e)])}let{error:i}=await l.N.storage.from("avatars").upload(s,e);if(i)throw i;let{data:{publicUrl:n}}=l.N.storage.from("avatars").getPublicUrl(s),c=await g({avatar_url:n});if(c.success)return{success:!0,url:n};throw Error(c.error)}catch(a){console.error("Erro ao fazer upload do avatar:",a);let e=a instanceof Error?a.message:"Erro ao fazer upload da imagem";return h(e),{success:!1,error:e}}},b=async()=>{if(r&&(null==o?void 0:o.avatar_url))try{h(null);let e=o.avatar_url.split("/").pop();return e&&await l.N.storage.from("avatars").remove(["avatars/".concat(e)]),await g({avatar_url:""})}catch(a){console.error("Erro ao remover avatar:",a);let e="Erro ao remover avatar";return h(e),{success:!1,error:e}}};return(0,s.useEffect)(()=>{r?(x(!1),f()):(c(null),u(!1),x(!1))},[null==r?void 0:r.id,r,f]),(0,t.jsx)(n.Provider,{value:{profile:o,isLoading:d,error:m,updateProfile:g,uploadAvatar:y,removeAvatar:b,clearError:()=>h(null),refreshProfile:v},children:a})},c=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useProfile deve ser usado dentro de um ProfileProvider");return e}},6681:(e,a,r)=>{"use strict";r.d(a,{A:()=>l});var t=r(2115),s=r(2099);let l=()=>{let[e,a]=(0,t.useState)(null),[r,l]=(0,t.useState)(null),[i,n]=(0,t.useState)(!0),[o,c]=(0,t.useState)(null);return(0,t.useEffect)(()=>{(async()=>{let{data:{session:e},error:r}=await s.N.auth.getSession();if(r)c({message:r.message});else{var t;l(e),a(null!=(t=null==e?void 0:e.user)?t:null)}n(!1)})();let{data:{subscription:e}}=s.N.auth.onAuthStateChange(async(e,r)=>{var t;l(r),a(null!=(t=null==r?void 0:r.user)?t:null),n(!1),"SIGNED_OUT"===e&&c(null)});return()=>e.unsubscribe()},[]),{user:e,session:r,isLoading:i,error:o,signInWithEmail:async(e,a)=>{n(!0),c(null);let{data:r,error:t}=await s.N.auth.signInWithPassword({email:e,password:a});return t?(c({message:t.message}),n(!1),{success:!1,error:t}):(n(!1),{success:!0,data:r})},signUpWithEmail:async(e,a)=>{n(!0),c(null);let{data:r,error:t}=await s.N.auth.signUp({email:e,password:a});return t?(c({message:t.message}),n(!1),{success:!1,error:t}):(n(!1),{success:!0,data:r})},signInWithGoogle:async()=>{n(!0),c(null);let{data:e,error:a}=await s.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/dashboard")}});return a?(c({message:a.message}),n(!1),{success:!1,error:a}):{success:!0,data:e}},signInWithOTP:async e=>{n(!0),c(null);let{data:a,error:r}=await s.N.auth.signInWithOtp({email:e,options:{shouldCreateUser:!0}});return r?(c({message:r.message}),n(!1),{success:!1,error:r}):(n(!1),{success:!0,data:a})},verifyOTP:async(e,a)=>{n(!0),c(null);let{data:r,error:t}=await s.N.auth.verifyOtp({email:e,token:a,type:"email"});return t?(c({message:t.message}),n(!1),{success:!1,error:t}):(n(!1),{success:!0,data:r})},signOut:async()=>{n(!0);let{error:e}=await s.N.auth.signOut();return e&&c({message:e.message}),n(!1),{success:!e,error:e}}}}},9946:(e,a,r)=>{"use strict";r.d(a,{A:()=>o});var t=r(2115);let s=e=>{let a=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,r)=>r?r.toUpperCase():a.toLowerCase());return a.charAt(0).toUpperCase()+a.slice(1)},l=function(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return a.filter((e,a,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===a).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,a)=>{let{color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:u,...m}=e;return(0,t.createElement)("svg",{ref:a,...i,width:s,height:s,stroke:r,strokeWidth:o?24*Number(n)/Number(s):n,className:l("lucide",c),...!d&&!(e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0})(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(d)?d:[d]])}),o=(e,a)=>{let r=(0,t.forwardRef)((r,i)=>{let{className:o,...c}=r;return(0,t.createElement)(n,{ref:i,iconNode:a,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...c})});return r.displayName=s(e),r}}},e=>{e.O(0,[302,36,441,964,358],()=>e(e.s=1264)),_N_E=e.O()}]);