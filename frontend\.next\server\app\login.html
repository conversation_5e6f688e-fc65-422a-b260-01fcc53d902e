<!DOCTYPE html><!--4G7Quf2nS71oGaKDC_JLG--><html lang="pt-BR" class="__variable_e8ce0c __variable_694534"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/e2a0a94af7378cb9.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9851933e7085a8da.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-d6e2a37b7965f281.js" async=""></script><script src="/_next/static/chunks/main-app-9e7857a45929810f.js" async=""></script><script src="/_next/static/chunks/302-c203ae1d098564d4.js" async=""></script><script src="/_next/static/chunks/926-41058729716f8cf0.js" async=""></script><script src="/_next/static/chunks/240-3755f7ec7fd28462.js" async=""></script><script src="/_next/static/chunks/app/layout-52c1db809847c951.js" async=""></script><script src="/_next/static/chunks/b536a0f1-da6632a683bdaed1.js" async=""></script><script src="/_next/static/chunks/bd904a5c-078716a8e125f184.js" async=""></script><script src="/_next/static/chunks/1329d575-ffa9b8b86084925f.js" async=""></script><script src="/_next/static/chunks/36-c864246f2b552ae7.js" async=""></script><script src="/_next/static/chunks/562-8571711edbebebdb.js" async=""></script><script src="/_next/static/chunks/app/login/page-9d54ea2f12d6283f.js" async=""></script><title>Frontend - Projeto Profit Growth</title><meta name="description" content="Frontend do projeto Profit Growth"/><link rel="shortcut icon" href="/icon-logo.svg"/><link rel="icon" href="/icon-logo.svg" type="image/svg+xml"/><link rel="apple-touch-icon" href="/icon-logo.svg"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c antialiased"><div hidden=""><!--$--><!--/$--></div><div class="w-full h-screen"><div class="flex w-[100%] flex-col min-h-screen bg-black relative"><div class="absolute inset-0 z-0"><div class="absolute inset-0"><div class="h-full relative w-full bg-black"><div class="h-full w-full"><div style="position:relative;width:100%;height:100%;overflow:hidden;pointer-events:auto" class="absolute inset-0 h-full w-full"><div style="width:100%;height:100%"><canvas style="display:block"></canvas></div></div></div><div class="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div></div></div><div class="absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(0,0,0,1)_0%,_transparent_100%)]"></div><div class="absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-black to-transparent"></div></div><div class="relative z-10 flex flex-col flex-1"><header class="fixed top-6 left-1/2 transform -translate-x-1/2 z-20 flex flex-col items-center pl-6 pr-6 py-3 backdrop-blur-sm rounded-full border border-[#333] bg-[#1f1f1f57] w-[calc(100%-2rem)] sm:w-auto transition-[border-radius] duration-0 ease-in-out"><div class="flex items-center justify-between w-full gap-x-6 sm:gap-x-8"><div class="flex items-center"><img alt="Logo" loading="lazy" width="144" height="20" decoding="async" data-nimg="1" class="w-36 h-5" style="color:transparent" src="/logo.svg"/></div><nav class="hidden sm:flex items-center space-x-4 sm:space-x-6 text-sm"><a href="#1" class="group relative inline-block overflow-hidden h-5 flex items-center text-sm"><div class="flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2"><span class="text-gray-300">Planos</span><span class="text-white">Planos</span></div></a><a href="#2" class="group relative inline-block overflow-hidden h-5 flex items-center text-sm"><div class="flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2"><span class="text-gray-300">Ferramentas</span><span class="text-white">Ferramentas</span></div></a><a href="#3" class="group relative inline-block overflow-hidden h-5 flex items-center text-sm"><div class="flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2"><span class="text-gray-300">Contato</span><span class="text-white">Contato</span></div></a></nav><div class="hidden sm:flex items-center gap-2 sm:gap-3"><button class="px-4 py-2 sm:px-3 text-xs sm:text-sm border border-[#333] bg-[rgba(31,31,31,0.62)] text-gray-300 rounded-full hover:border-white/50 hover:text-white transition-colors duration-200 w-full sm:w-auto">Entrar</button><div class="relative group w-full sm:w-auto"><div class="absolute inset-0 -m-2 rounded-full hidden sm:block bg-green-100 opacity-40 filter blur-lg pointer-events-none transition-all duration-300 ease-out group-hover:opacity-60 group-hover:blur-xl group-hover:-m-3"></div><button class="relative z-10 px-4 py-2 sm:px-3 text-xs sm:text-sm font-semibold text-black bg-gradient-to-br from-green-100 to-green-400 rounded-full hover:from-green-200 hover:to-green-400 transition-all duration-200 w-full sm:w-auto">Inscreva-se</button></div></div><button class="sm:hidden flex items-center justify-center w-8 h-8 text-gray-300 focus:outline-none" aria-label="Open Menu"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div><div class="sm:hidden flex flex-col items-center w-full transition-all ease-in-out duration-300 overflow-hidden max-h-0 opacity-0 pt-0 pointer-events-none"><nav class="flex flex-col items-center space-y-4 text-base w-full"><a href="#1" class="text-gray-300 hover:text-white transition-colors w-full text-center">Planos</a><a href="#2" class="text-gray-300 hover:text-white transition-colors w-full text-center">Ferramentas</a><a href="#3" class="text-gray-300 hover:text-white transition-colors w-full text-center">Contato</a></nav><div class="flex flex-col items-center space-y-4 mt-4 w-full"><button class="px-4 py-2 sm:px-3 text-xs sm:text-sm border border-[#333] bg-[rgba(31,31,31,0.62)] text-gray-300 rounded-full hover:border-white/50 hover:text-white transition-colors duration-200 w-full sm:w-auto">Entrar</button><div class="relative group w-full sm:w-auto"><div class="absolute inset-0 -m-2 rounded-full hidden sm:block bg-green-100 opacity-40 filter blur-lg pointer-events-none transition-all duration-300 ease-out group-hover:opacity-60 group-hover:blur-xl group-hover:-m-3"></div><button class="relative z-10 px-4 py-2 sm:px-3 text-xs sm:text-sm font-semibold text-black bg-gradient-to-br from-green-100 to-green-400 rounded-full hover:from-green-200 hover:to-green-400 transition-all duration-200 w-full sm:w-auto">Inscreva-se</button></div></div></div></header><div class="flex flex-1 flex-col lg:flex-row"><div class="flex-1 flex flex-col justify-center items-center"><div class="w-full mt-[150px] max-w-sm"><div class="space-y-6 text-center" style="opacity:0;transform:translateX(-100px)"><div class="space-y-6"><div class="space-y-1"><h1 class="text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white">Bem-vindo de volta</h1><p class="text-[1.8rem] text-white/70 font-light">Entre na sua conta</p></div></div><div class="space-y-4"><button disabled="" class="backdrop-blur-[2px] w-full flex items-center justify-center gap-2 bg-white/5 hover:bg-white/10 text-white border border-white/10 rounded-full py-3 px-4 transition-colors disabled:opacity-50"><span class="text-lg">G</span><span>Entrar<!-- --> com Google</span></button><div class="flex items-center gap-4"><div class="h-px bg-white/10 flex-1"></div><span class="text-white/40 text-sm">ou</span><div class="h-px bg-white/10 flex-1"></div></div><form><div class="relative"><input type="email" placeholder="<EMAIL>" disabled="" class="w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50" required="" value=""/><button type="submit" disabled="" class="absolute right-1.5 top-1.5 text-white w-9 h-9 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-colors group overflow-hidden disabled:opacity-50"><span class="relative w-full h-full block overflow-hidden"><span class="absolute inset-0 flex items-center justify-center transition-transform duration-300 group-hover:translate-x-full">→</span><span class="absolute inset-0 flex items-center justify-center transition-transform duration-300 -translate-x-full group-hover:translate-x-0">→</span></span></button></div></form></div><div class="flex justify-center"><button class="text-white/50 hover:text-white/70 transition-colors text-sm">Não tem conta? Registre-se</button></div><p class="text-xs text-white/40 pt-10">Ao continuar, você concorda com os<!-- --> <a class="underline text-white/40 hover:text-white/60 transition-colors" href="#">Termos de Serviço</a> <!-- -->e<!-- --> <a class="underline text-white/40 hover:text-white/60 transition-colors" href="#">Política de Privacidade</a>.</p></div></div></div></div></div></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-9851933e7085a8da.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[9389,[\"302\",\"static/chunks/302-c203ae1d098564d4.js\",\"926\",\"static/chunks/926-41058729716f8cf0.js\",\"240\",\"static/chunks/240-3755f7ec7fd28462.js\",\"177\",\"static/chunks/app/layout-52c1db809847c951.js\"],\"default\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[4215,[\"367\",\"static/chunks/b536a0f1-da6632a683bdaed1.js\",\"831\",\"static/chunks/bd904a5c-078716a8e125f184.js\",\"413\",\"static/chunks/1329d575-ffa9b8b86084925f.js\",\"302\",\"static/chunks/302-c203ae1d098564d4.js\",\"36\",\"static/chunks/36-c864246f2b552ae7.js\",\"926\",\"static/chunks/926-41058729716f8cf0.js\",\"562\",\"static/chunks/562-8571711edbebebdb.js\",\"520\",\"static/chunks/app/login/page-9d54ea2f12d6283f.js\"],\"SupabaseSignIn\"]\n6:I[9665,[],\"OutletBoundary\"]\n8:I[4911,[],\"AsyncMetadataOutlet\"]\na:I[9665,[],\"ViewportBoundary\"]\nc:I[9665,[],\"MetadataBoundary\"]\nd:\"$Sreact.suspense\"\nf:I[8393,[],\"\"]\n:HL[\"/_next/static/css/e2a0a94af7378cb9.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"4G7Quf2nS71oGaKDC-JLG\",\"p\":\"\",\"c\":[\"\",\"login\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"login\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/e2a0a94af7378cb9.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"pt-BR\",\"className\":\"__variable_e8ce0c __variable_694534\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"login\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"w-full h-screen\",\"children\":[\"$\",\"$L5\",null,{}]}],null,[\"$\",\"$L6\",null,{\"children\":[\"$L7\",[\"$\",\"$L8\",null,{\"promise\":\"$@9\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],null],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$d\",null,{\"fallback\":null,\"children\":\"$Le\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$f\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n7:null\n"])</script><script>self.__next_f.push([1,"10:I[8175,[],\"IconMark\"]\n9:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Frontend - Projeto Profit Growth\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Frontend do projeto Profit Growth\"}],[\"$\",\"link\",\"2\",{\"rel\":\"shortcut icon\",\"href\":\"/icon-logo.svg\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/icon-logo.svg\",\"type\":\"image/svg+xml\"}],[\"$\",\"link\",\"4\",{\"rel\":\"apple-touch-icon\",\"href\":\"/icon-logo.svg\"}],[\"$\",\"$L10\",\"5\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"e:\"$9:metadata\"\n"])</script></body></html>