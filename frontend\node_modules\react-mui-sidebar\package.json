{"name": "react-mui-sidebar", "version": "1.6.3", "type": "module", "description": "react mui sidebar", "main": "dist/index.umd.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "types": "./dist/index.d.ts"}}, "files": ["/dist"], "publishConfig": {"access": "public"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "repository": {"type": "git", "url": "git+https://github.com/adminmart/react-mui-sidebar.git"}, "keywords": ["react-mui-sidebar", "sidebar", "react"], "author": "Adminmart Team", "license": "MIT", "bugs": {"url": "https://github.com/adminmart/react-mui-sidebar/issues"}, "homepage": "https://adminmart.com", "dependencies": {"react-router-dom": "^7.2.0", "vite-plugin-dts": "^4.5.0"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "@types/react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "devDependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@eslint/js": "^9.17.0", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@types/node": "^22.10.7", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}