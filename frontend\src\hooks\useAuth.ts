'use client'

import { useState, useEffect } from 'react'
import { supabase, type AuthError } from '@/lib/supabase'
import { User, Session } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<AuthError | null>(null)

  useEffect(() => {
    // Obter sessão inicial
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        setError({ message: error.message })
      } else {
        setSession(session)
        setUser(session?.user ?? null)
      }
      
      setIsLoading(false)
    }

    getInitialSession()

    // Escutar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setIsLoading(false)
        
        if (event === 'SIGNED_OUT') {
          setError(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  // Função para login com email/senha
  const signInWithEmail = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para registro com email/senha
  const signUpWithEmail = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para login com Google
  const signInWithGoogle = async () => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`
      }
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    return { success: true, data }
  }

  // Função para enviar OTP por email
  const signInWithOTP = async (email: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: true,
      }
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para verificar OTP
  const verifyOTP = async (email: string, token: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'email'
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para logout
  const signOut = async () => {
    setIsLoading(true)
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      setError({ message: error.message })
    }
    
    setIsLoading(false)
    return { success: !error, error }
  }

  return {
    user,
    session,
    isLoading,
    error,
    signInWithEmail,
    signUpWithEmail,
    signInWithGoogle,
    signInWithOTP,
    verifyOTP,
    signOut,
  }
}